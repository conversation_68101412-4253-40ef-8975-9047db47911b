# ================================
# Python
# ================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ================================
# 虚拟环境
# ================================
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
py39ocr/
py39_rapidocr/

# ================================
# IDE和编辑器
# ================================
.vscode/
.idea/
*.swp
*.swo
*~

# ================================
# 日志文件
# ================================
*.log
logs/
log/

# ================================
# 临时文件和缓存
# ================================
*.tmp
*.temp
.cache/
.pytest_cache/
.coverage
htmlcov/

# ================================
# 模型文件和数据
# ================================
models/
*.pth
*.pt
*.onnx
*.pb
*.h5
*.pkl
*.pickle

# ================================
# 上传和测试文件
# ================================
uploads/
test_images/
temp_images/
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.tif
*.webp

# 保留示例图片
!examples/*.jpg
!examples/*.png
!docs/**/*.jpg
!docs/**/*.png

examples/em.py

# ================================
# 配置文件（敏感信息）
# ================================
config/local_config.yaml
config/production_config.yaml
.env.local
.env.production
secrets.yaml

# ================================
# 数据库文件
# ================================
*.db
*.sqlite
*.sqlite3

# ================================
# 系统文件
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ================================
# Windows
# ================================
*.lnk

# ================================
# 备份文件
# ================================
*.bak
*.backup
*.old
*~

# ================================
# 测试结果和报告
# ================================
test_results/
test_reports/
*.xml
coverage.xml
nosetests.xml

# ================================
# 文档生成
# ================================
docs/_build/
docs/build/
site/

# ================================
# 特定于项目的忽略
# ================================
# OCR处理的临时文件
ocr_temp/
processed_images/

# 模型下载缓存
.cache/huggingface/
.cache/torch/
.cache/transformers/

# Conda环境文件
environment.yml.lock

# 性能分析文件
*.prof
*.profile

# Jupyter Notebook
.ipynb_checkpoints

# ================================
# 开发和调试文件
# ================================
debug_*.py
test_*.py
quick_*.py
*_debug.py
*_test.py

# 但保留重要的测试文件
!src/test/
!test/README.md

# ================================
# 部署相关
# ================================
docker-compose.override.yml
.dockerignore
Dockerfile.local

# ================================
# 其他
# ================================
*.pid
*.seed
*.pid.lock
.npm
node_modules/
