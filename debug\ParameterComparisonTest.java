package debug;

import java.util.*;

/**
 * 档案要素检查参数对比测试
 * 用于对比新旧实现的参数差异
 */
public class ParameterComparisonTest {
    
    /**
     * 对比两个参数集合的差异
     */
    public static void compareParameters(Map<String, Object> oldParams, Map<String, Object> newParams) {
        System.out.println("🔍🔍🔍 === 参数对比分析 === 🔍🔍🔍");
        
        // 检查参数数量
        System.out.println("参数数量对比:");
        System.out.println("  原实现: " + oldParams.size());
        System.out.println("  新实现: " + newParams.size());
        
        // 检查参数名称
        Set<String> oldKeys = oldParams.keySet();
        Set<String> newKeys = newParams.keySet();
        
        System.out.println("\n参数名称对比:");
        System.out.println("  原实现参数: " + oldKeys);
        System.out.println("  新实现参数: " + newKeys);
        
        // 找出缺失的参数
        Set<String> missingInNew = new HashSet<>(oldKeys);
        missingInNew.removeAll(newKeys);
        if (!missingInNew.isEmpty()) {
            System.out.println("  ❌ 新实现中缺失的参数: " + missingInNew);
        }
        
        Set<String> extraInNew = new HashSet<>(newKeys);
        extraInNew.removeAll(oldKeys);
        if (!extraInNew.isEmpty()) {
            System.out.println("  ➕ 新实现中新增的参数: " + extraInNew);
        }
        
        // 对比相同参数的值
        System.out.println("\n参数值对比:");
        for (String key : oldKeys) {
            if (newKeys.contains(key)) {
                Object oldValue = oldParams.get(key);
                Object newValue = newParams.get(key);
                
                if (Objects.equals(oldValue, newValue)) {
                    System.out.println("  ✅ " + key + ": 值相同");
                } else {
                    System.out.println("  ❌ " + key + ": 值不同");
                    System.out.println("    原值: " + oldValue);
                    System.out.println("    新值: " + newValue);
                    
                    // 如果是字符串，进行详细对比
                    if (oldValue instanceof String && newValue instanceof String) {
                        compareStringValues(key, (String) oldValue, (String) newValue);
                    }
                }
            }
        }
        
        System.out.println("🔍🔍🔍 === 对比完成 === 🔍🔍🔍");
    }
    
    /**
     * 对比字符串值的差异
     */
    private static void compareStringValues(String paramName, String oldValue, String newValue) {
        System.out.println("    📝 " + paramName + " 字符串详细对比:");
        System.out.println("      长度: " + oldValue.length() + " vs " + newValue.length());
        
        if (oldValue.length() != newValue.length()) {
            System.out.println("      ❌ 长度不同，可能是内容结构变化");
        }
        
        // 如果是JSON字符串，尝试解析对比
        if (oldValue.startsWith("[") || oldValue.startsWith("{")) {
            System.out.println("      🔍 检测到JSON格式，建议进行JSON结构对比");
        }
    }
    
    /**
     * 创建测试用的参数映射
     */
    public static Map<String, Object> createTestParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("task_id", "test_task_123");
        params.put("excel_data", "[{\"dataKey\":\"test\",\"title\":\"测试题名\"}]");
        params.put("image_data", "[{\"path\":\"/test/path\",\"dataKey\":\"test\"}]");
        params.put("elements", "[\"title\",\"responsible_party\"]");
        params.put("confidence_threshold", 0.5);
        params.put("similarity_threshold", 0.8);
        params.put("enable_stamp_processing", true);
        params.put("stamp_confidence_threshold", 0.5);
        params.put("enable_preprocessing", true);
        return params;
    }
    
    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        // 模拟测试
        Map<String, Object> oldParams = createTestParams();
        Map<String, Object> newParams = createTestParams();
        
        // 模拟一个差异
        newParams.put("extra_param", "new_value");
        newParams.put("excel_data", "[{\"dataKey\":\"test\",\"title\":\"不同的题名\"}]");
        
        compareParameters(oldParams, newParams);
    }
}
