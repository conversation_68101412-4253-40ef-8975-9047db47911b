# 档案要素检查接口参数对比分析

## 问题描述
用户反馈：Java端调用AI侧的接口参数和之前不一样了，功能不对了。

## 参数对比分析

### 原来的实现（从git历史恢复）
```java
// 位置：WorkImpl.executeArchiveElementsCheck() -> callPythonArchiveService()
MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
params.add("task_id", taskId);
params.add("excel_data", JsonHelper.toJson(excelData));
params.add("image_data", JsonHelper.toJson(imageDataForPython)); // 🔍 传递完整的分件数据
params.add("elements", JsonHelper.toJson(Arrays.asList("title", "responsible_party", "document_number", "issue_date")));
params.add("confidence_threshold", 0.5);
params.add("similarity_threshold", 0.8);
params.add("enable_stamp_processing", true);  // 启用印章处理
params.add("stamp_confidence_threshold", 0.5); // 适合OpenCV的置信度阈值
params.add("enable_preprocessing", true);  // 启用图像预处理
```

### 现在的实现（ArchiveElementsCheckRule）
```java
// 位置：ArchiveElementsCheckRule.callPythonArchiveService()
MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
params.add("task_id", taskId);
params.add("excel_data", JsonHelper.toJson(excelData));
params.add("image_data", JsonHelper.toJson(imageDataForPython)); // 🔍 传递完整的分件数据
params.add("elements", JsonHelper.toJson(Arrays.asList("title", "responsible_party", "document_number", "issue_date")));
params.add("confidence_threshold", 0.5);
params.add("similarity_threshold", 0.8);
params.add("enable_stamp_processing", true);  // 启用印章处理
params.add("stamp_confidence_threshold", 0.5); // 适合OpenCV的置信度阈值
params.add("enable_preprocessing", true);  // 启用图像预处理
```

## 初步分析结果
**参数格式完全一致**，没有发现明显差异。

## 可能的问题点

### 1. **数据内容差异**
虽然参数名称相同，但参数的**内容**可能不同：

#### A. excelData内容差异
- **原来**：可能包含特定的字段结构
- **现在**：字段结构可能发生变化

#### B. imageDataForPython内容差异
- **原来**：图像数据的结构和内容
- **现在**：分件处理后的图像数据结构可能不同

### 2. **调用时机差异**
- **原来**：在WorkImpl.executor()中直接调用
- **现在**：通过异步Task执行，调用时机可能不同

### 3. **数据准备逻辑差异**
- **原来**：在executeArchiveElementsCheck中准备数据
- **现在**：在ArchiveElementsCheckRule.ruleExecute中准备数据

## 排查步骤

### 步骤1：对比数据内容
```java
// 在ArchiveElementsCheckRule.callPythonArchiveService()中添加详细日志
System.out.println("🔍 Excel数据详细内容:");
for (int i = 0; i < excelData.size(); i++) {
    System.out.println("  [" + i + "] " + excelData.get(i));
}

System.out.println("🔍 图像数据详细内容:");
for (int i = 0; i < imageDataForPython.size(); i++) {
    System.out.println("  [" + i + "] " + imageDataForPython.get(i));
}
```

### 步骤2：对比Python端接口期望
检查Python端`/extract/archive/batch_compare`接口的参数定义：
- 是否有新增的必需参数？
- 是否有参数格式要求变化？
- 是否有参数名称变化？

### 步骤3：检查响应处理
对比响应解析逻辑是否有差异：
```java
// 原来的响应处理
Map<String, Object> resultMap = JsonHelper.json2map(responseBody);
ArchiveElementsCheckResult result = new ArchiveElementsCheckResult();
result.setSuccess((Boolean) resultMap.get("success"));
result.setTaskId((String) resultMap.get("task_id"));
result.setComparisonResult(resultMap.get("comparison_result"));
result.setError((String) resultMap.get("error"));
```

### 步骤4：检查异步执行影响
- 异步执行是否影响了数据的准备时机？
- 是否存在数据竞争或时序问题？

## 建议的修复方案

### 方案1：恢复原始实现进行对比测试
```bash
# 临时恢复原来的实现
git stash
git checkout c62959cb -- kpass-exactness-check/kpass-exactness-soa/src/main/java/com/deeppaas/work/impl/WorkImpl.java
# 测试原始实现是否正常
# 然后对比差异
```

### 方案2：增强调试日志
在新实现中添加详细的参数对比日志，与原来的实现进行逐项对比。

### 方案3：分步验证
1. 先验证参数准备阶段是否正确
2. 再验证HTTP请求发送是否正确
3. 最后验证响应解析是否正确

## 下一步行动
1. 请用户提供具体的错误现象或日志
2. 添加详细的调试日志进行对比
3. 如有必要，临时恢复原始实现进行对比测试
