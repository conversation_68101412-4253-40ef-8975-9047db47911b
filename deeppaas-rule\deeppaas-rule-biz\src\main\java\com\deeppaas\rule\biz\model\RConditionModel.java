package com.deeppaas.rule.biz.model;

import com.deeppaas.rule.biz.databind.RDataBind;

import java.util.List;

/**
 * 数据模型查询匹配条件
 * <AUTHOR>
 * @date 2022/3/2
 */
public abstract class RConditionModel {
    /**
     * 运算符
     */
    private String operator;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public static class Logic extends RConditionModel {
        private List<RConditionModel> conditions;

        public List<RConditionModel> getConditions() {
            return conditions;
        }

        public void setConditions(List<RConditionModel> conditions) {
            this.conditions = conditions;
        }
    }

    /**
     * 简单条件
     */
    public static class Simple extends RConditionModel {
        /**
         * 数据模型字段名
         */
        private String fieldName;
        /**
         * 查询属性匹配值
         */
        private RDataBind dataBind;

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public RDataBind getDataBind() {
            return dataBind;
        }

        public void setDataBind(RDataBind dataBind) {
            this.dataBind = dataBind;
        }
    }
}
