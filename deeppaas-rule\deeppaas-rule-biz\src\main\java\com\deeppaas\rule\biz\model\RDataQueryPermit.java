package com.deeppaas.rule.biz.model;

import com.deeppaas.rule.biz.databind.RDataBind;
import lombok.Data;

/**
 * 查询授权
 * <AUTHOR>
 * @date 2022/6/14
 */
@Data
public class RDataQueryPermit {
    /**
     * 授权名称，如"当前登录人可以看自己的"
     */
    private String permitName;
    /**
     * 判断是否可使用该授权的依据(是否能看)，绑定信息必须为Boolean类型
     */
    private RDataBind authBind;
    /**
     * 被授权后的查询限制条件
     */
    private RConditionModel condition;
}
