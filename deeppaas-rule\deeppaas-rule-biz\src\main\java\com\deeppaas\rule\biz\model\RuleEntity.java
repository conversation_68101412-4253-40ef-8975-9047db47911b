package com.deeppaas.rule.biz.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/6/11
 */
@Data
public class RuleEntity {
    /**
     * ID
     */
    private String id;
    /**
     * 模型名称
     */
    private String name;

    /**
     * 规则备注
     */
    private String remark;

    /**
     * 属性
     */
    protected List<RuleEntityProperty> properties = new ArrayList<>();
}
