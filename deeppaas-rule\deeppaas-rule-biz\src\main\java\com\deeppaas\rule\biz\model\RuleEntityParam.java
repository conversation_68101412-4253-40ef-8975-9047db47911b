package com.deeppaas.rule.biz.model;

import com.deeppaas.rule.biz.entity.RuleEntityPropertyDO;
import lombok.Data;

import java.util.List;

/**
 * 规则执行参数
 * <AUTHOR>
 * @date 2022/4/27
 */
@Data
public class RuleEntityParam {

    /**
     * 模型名称
     */
    private String name;

    /**
     * 规则备注
     */
    private String remark;

    /**
     * 所属接口ID,如果是一个接口返回的结构定义那么有值，否则为PUBLIC
     */
    private String apiId;

    /**
     * 规则模型属性集合
     */
    private List<RuleEntityPropertyDO> propertyList;
}
