package com.deeppaas.rule.biz.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/11
 */
@Data
public class RuleEntityProperty {
    /**
     * 所属规则模型ID
     */
    private String modelId;
    /**
     * 属性名称，英文
     */
    private String name;

    /**
     * 类型：文本、数字、布尔、数据模型对象、规则模型对象、文件
     *      文本数组、数字数组、数据模型对象数组、规则模型对象数组、文件数组
     */
    private String type;

    /**
     * 对应模型：数据模型或数组为数据模型code,规则模型或数组为规则模型ID
     */
    private String model;

    /**
     * 属性
     */
    protected List<RuleEntityProperty> properties = new ArrayList<>();
}
