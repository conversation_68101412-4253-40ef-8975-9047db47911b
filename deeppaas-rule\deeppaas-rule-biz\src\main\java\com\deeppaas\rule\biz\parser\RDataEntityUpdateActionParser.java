package com.deeppaas.rule.biz.parser;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.action.RAction;
import com.deeppaas.rule.biz.action.RDataEntityUpdateAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RConditionModel;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 数据模型更新动作JSON解析器
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RDataEntityUpdateActionParser extends RActionParser{
    private static final String KEY_DATA_ENTITY = "dataEntity";
    private static final String KEY_CONDITION = "condition";
    private static final String KEY_UPDATE_FIELDS = "updateFields";

    private static final String KEY_UPDATE_FIELDS_FIELD = "field";
    private static final String KEY_UPDATE_FIELDS_DATA_BIND = "dataBind";

    protected static RAction buildAction(JsonNode actionNode) {
        RDataEntityUpdateAction action = new RDataEntityUpdateAction();
        buildBaseInfo(action, actionNode);
        action.setDataEntity(actionNode.get(KEY_DATA_ENTITY).textValue());
        JsonNode conditionNode = actionNode.get(KEY_CONDITION);
        RConditionModel rConditionModel = RConditionModelParser.buildRConditionModel(conditionNode);
        action.setCondition(rConditionModel);
        JsonNode updateNode = actionNode.get(KEY_UPDATE_FIELDS);
        List<RDataEntityUpdateAction.UpdateField> updateFieldList = buildUpdateFields(updateNode);
        action.setUpdateFields(updateFieldList.toArray(new RDataEntityUpdateAction.UpdateField[updateFieldList.size()]));
        return action;
    }

    private static List<RDataEntityUpdateAction.UpdateField> buildUpdateFields(JsonNode updateNode) {
        if (updateNode.isArray()) {
            List<RDataEntityUpdateAction.UpdateField> updateFieldList = new ArrayList<>();
            Iterator<JsonNode> updateFieldIterator = updateNode.elements();
            while (updateFieldIterator.hasNext()) {
                JsonNode updateFieldNode = updateFieldIterator.next();
                String fieldCode = updateFieldNode.get(KEY_UPDATE_FIELDS_FIELD).textValue();
                RDataBind dataBind = RDataBindParser.buildDataBind(updateFieldNode.get(KEY_UPDATE_FIELDS_DATA_BIND));
                RDataEntityUpdateAction.UpdateField updateField = new RDataEntityUpdateAction.UpdateField();
                updateField.setField(fieldCode);
                updateField.setDataBind(dataBind);
                updateFieldList.add(updateField);
            }
            return updateFieldList;
        } else {
            throw RunException.error("更新字段解析异常");
        }
    }
}
