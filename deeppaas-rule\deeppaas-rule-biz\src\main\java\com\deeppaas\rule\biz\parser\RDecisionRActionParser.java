package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RDecisionAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RDecisionRActionParser extends RActionParser {
    private static final String KEY_DATA_BIND = "dataBind";
    private static final String KEY_NEXT_TRUE = "trueNext";
    private static final String KEY_NEXT_FALSE = "falseNext";

    public static RDecisionAction buildAction(JsonNode actionNode) {
        RDecisionAction action = new RDecisionAction();
        buildBaseInfo(action, actionNode);
        JsonNode targetNode = actionNode.get(KEY_DATA_BIND);
        RDataBind dataBind = RDataBindParser.buildDataBind(targetNode);
        action.setDataBind(dataBind);
        action.setTrueNext(actionNode.get(KEY_NEXT_TRUE).textValue());
        action.setFalseNext(actionNode.get(KEY_NEXT_FALSE).textValue());
        return action;
    }
}
