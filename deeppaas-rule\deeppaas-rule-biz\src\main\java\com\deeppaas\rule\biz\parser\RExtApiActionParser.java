package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RExtApiAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RExtApiActionParser extends RActionParser{
    private static final String KEY_API_ID = "apiId";
    private static final String KEY_RETURN_VAR = "returnVar";

    private static final String KEY_PARAM_CODE = "code";

    private static final String KEY_RETURN_VAR_NAME = "varName";

    private static final String KEY_PARAMS = "params";
    protected static RExtApiAction buildAction(JsonNode actionNode) {
        RExtApiAction action = new RExtApiAction();
        buildBaseInfo(action, actionNode);
        action.setApiId(actionNode.get(KEY_API_ID).textValue());
        JsonNode paramsNode = actionNode.get(KEY_PARAMS);
        if (paramsNode!=null && paramsNode.isArray()) {
            Map<String, RDataBind> paramBindMap = new HashMap<>();
            Iterator<JsonNode> fieldIterator = paramsNode.elements();
            while (fieldIterator.hasNext()) {
                JsonNode fieldBindNode = fieldIterator.next();
                String fieldCode = fieldBindNode.get(KEY_PARAM_CODE).textValue();
                JsonNode bindNode = fieldBindNode.get(KEY_DATA_BIND);
                RDataBind fieldBind = RDataBindParser.buildDataBind(bindNode);
                paramBindMap.put(fieldCode, fieldBind);
            }
            action.setParamBinds(paramBindMap);
        }
        action.setReturnVar(actionNode.get(KEY_RETURN_VAR).asBoolean());
        if (actionNode.has(KEY_RETURN_VAR_NAME)) {
            action.setVarName(actionNode.get(KEY_RETURN_VAR_NAME).textValue());
        }
        return action;
    }
}
