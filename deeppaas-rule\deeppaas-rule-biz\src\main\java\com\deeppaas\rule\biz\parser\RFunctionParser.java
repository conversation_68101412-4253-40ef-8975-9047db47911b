package com.deeppaas.rule.biz.parser;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RFunction;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RFunctionParser {
    private static final String KEY_NAME = "name";
    private static final String KEY_PARAMS = "params";

    public static RFunction buildFunction(JsonNode functionNode) {
        RFunction rFunction = new RFunction();
        rFunction.setName(functionNode.get(KEY_NAME).textValue());
        JsonNode paramsNode = functionNode.get(KEY_PARAMS);
        List<RDataBind> params = new ArrayList<>();
        if (paramsNode.isArray()) {
            Iterator<JsonNode> paramNodes = paramsNode.elements();
            while (paramNodes.hasNext()) {
                JsonNode paramNode = paramNodes.next();
                RDataBind param = RDataBindParser.buildDataBind(paramNode);
                params.add(param);
            }
            rFunction.setParams(params);
            return rFunction;
        } else {
            throw RunException.error("方法参数必须是数组");
        }
    }
}
