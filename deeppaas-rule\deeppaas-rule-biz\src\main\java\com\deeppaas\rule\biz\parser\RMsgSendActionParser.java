package com.deeppaas.rule.biz.parser;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.rule.biz.action.RMsgSendAction;
import com.deeppaas.rule.biz.action.RUserMsgSendAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.deeppaas.rule.biz.model.RUserScope;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
public class RMsgSendActionParser extends RActionParser{
    protected static final String KEY_USER_SCOPES = "userScopes";
    protected static final String KEY_USER_SCOPE_TYPE = "type";
    protected static final String KEY_USER_SCOPE_CODE = "code";
    protected static final String KEY_USER_SCOPE_POST_CTRL_DEPT_LEVEL = "postControlDeptLevelBind";
    protected static final String KEY_USER_SCOPE_POST_CTRL_DEPT_CODE = "postControlDeptCodeBind";

    protected static final String KEY_MSG_PARAM = "msgParams";

    protected static final String KEY_PARAM_NAME = "name";
    protected static final String KEY_PARAM_VALUE_BIND = "valueBind";

    public static void buildBaseMsgSendAction(RMsgSendAction action, JsonNode actionNode){
        List<RUserScope> userScopes = buildUserScopes(actionNode.get(KEY_USER_SCOPES));
        action.setUserScopes(userScopes);
        action.setMsgParams(buildParams(actionNode.get(KEY_MSG_PARAM)));
    }

    private static List<RUserScope> buildUserScopes(JsonNode jsonNode) {
        if(jsonNode==null){
            return Collections.emptyList();
        }
        if (jsonNode.isArray()) {
            List<RUserScope> userScopes = new ArrayList<>();
            Iterator<JsonNode> userScopeIterator = jsonNode.elements();
            while (userScopeIterator.hasNext()) {
                JsonNode userScopeNode = userScopeIterator.next();
                RUserScope userScope = new RUserScope();
                userScope.setType(RUserScope.ScopeType.valueOf(userScopeNode.get(KEY_USER_SCOPE_TYPE).textValue()));
                userScope.setCodeBind(RDataBindParser.buildDataBind(userScopeNode.get(KEY_USER_SCOPE_CODE)));
                userScope.setPostControlDeptCodeBind(RDataBindParser.buildDataBind(userScopeNode.get(KEY_USER_SCOPE_POST_CTRL_DEPT_LEVEL)));
                userScope.setPostControlDeptLevelBind(RDataBindParser.buildDataBind(userScopeNode.get(KEY_USER_SCOPE_POST_CTRL_DEPT_CODE)));
                userScopes.add(userScope);
            }
            return userScopes;
        } else {
            throw RunException.error("发送人解析异常");
        }
    }

    private static List<RUserMsgSendAction.Param> buildParams(JsonNode jsonNode) {
        if(jsonNode==null){
            return Collections.emptyList();
        }
        if (jsonNode.isArray()) {
            List<RUserMsgSendAction.Param> params = new ArrayList<>();
            Iterator<JsonNode> paramIterator = jsonNode.elements();
            while (paramIterator.hasNext()) {
                JsonNode paramNode = paramIterator.next();
                String paramCode = paramNode.get(KEY_PARAM_NAME).textValue();
                RDataBind dataBind = RDataBindParser.buildDataBind(paramNode.get(KEY_PARAM_VALUE_BIND));
                RUserMsgSendAction.Param param = new RUserMsgSendAction.Param();
                param.setName(paramCode);
                param.setValueBind(dataBind);
                params.add(param);
            }
            return params;
        } else {
            throw RunException.error("参数解析异常");
        }
    }
}
