package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RAction;
import com.deeppaas.rule.biz.action.RRuleAction;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 子规则动作JSON解析器
 * <AUTHOR>
 * @date 2022/6/15
 */
public class RRuleActionParser extends RActionParser{
    private static final String KEY_RULE_ID = "ruleId";
    private static final String KEY_RULE_PARAM = "param";

    public static RAction buildAction(JsonNode actionNode) {
        RRuleAction action = new RRuleAction();
        buildBaseInfo(action, actionNode);
        action.setRuleId(actionNode.get(KEY_RULE_ID).textValue());
        action.setParam(RDataBindParser.buildDataBind(actionNode.get(KEY_RULE_PARAM)));
        return action;
    }
}
