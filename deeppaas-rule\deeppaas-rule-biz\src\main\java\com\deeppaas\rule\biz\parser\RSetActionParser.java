package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RAction;
import com.deeppaas.rule.biz.action.RSetAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RSetActionParser extends RActionParser{

    private static final String KEY_SET_SOURCE = "source";
    private static final String KEY_SET_TARGET = "target";

    protected static RAction buildAction(JsonNode actionNode) {
        RSetAction action = new RSetAction();
        buildBaseInfo(action, actionNode);
        JsonNode sourceNode = actionNode.get(KEY_SET_SOURCE);
        RDataBind rSource = RDataBindParser.buildDataBind(sourceNode);
        action.setSource(rSource);
        JsonNode valueNode = actionNode.get(KEY_SET_TARGET);
        RDataBind rTarget = RDataBindParser.buildDataBind(valueNode);
        action.setTarget(rTarget);
        return action;
    }

}
