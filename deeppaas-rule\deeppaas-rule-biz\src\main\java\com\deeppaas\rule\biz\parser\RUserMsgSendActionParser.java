package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RUserMsgSendAction;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
public class RUserMsgSendActionParser extends RMsgSendActionParser{
    private static final String KEY_TITLE = "title";
    private static final String KEY_CONTENT = "content";
    private static final String KEY_URL = "url";

    public static RUserMsgSendAction buildAction(JsonNode actionNode) {
        RUserMsgSendAction action = new RUserMsgSendAction();
        buildBaseMsgSendAction(action, actionNode);
        action.setTitle(actionNode.get(KEY_TITLE).textValue());
        action.setContent(actionNode.get(KEY_CONTENT).textValue());
        action.setUrl(actionNode.get(KEY_URL).textValue());
        return action;
    }

}
