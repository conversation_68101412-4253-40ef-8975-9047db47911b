package com.deeppaas.rule.biz.parser;

import com.deeppaas.rule.biz.action.RUserMsgSendAction;
import com.deeppaas.rule.biz.action.RUserTemplateMsgSendAction;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * <AUTHOR>
 * @date 2022/9/3
 */
public class RUserTemplateMsgSendActionParser  extends RMsgSendActionParser{
    private static final String KEY_TEMPLATE_ID = "templateId";

    public static RUserTemplateMsgSendAction buildAction(JsonNode actionNode) {
        RUserTemplateMsgSendAction action = new RUserTemplateMsgSendAction();
        buildBaseMsgSendAction(action, actionNode);
        action.setTemplateId(actionNode.get(KEY_TEMPLATE_ID).textValue());
        return action;
    }

}
