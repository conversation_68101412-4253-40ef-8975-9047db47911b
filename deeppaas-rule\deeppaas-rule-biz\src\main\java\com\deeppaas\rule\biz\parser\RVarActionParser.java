package com.deeppaas.rule.biz.parser;

import com.deeppaas.common.data.enums.SimpleDataType;
import com.deeppaas.rule.biz.action.RVarAction;
import com.deeppaas.rule.biz.databind.RDataBind;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
public class RVarActionParser extends RActionParser {

    private static final String KEY_VAR_TYPE = "varType";
    private static final String KEY_VAR_ENTITY = "varEntity";
    private static final String KEY_DATA_BIND = "dataBind";

    /**
     * 构建变量动作
     * @param actionNode
     * @return
     */
    protected static RVarAction buildAction(JsonNode actionNode) {
        RVarAction action = new RVarAction();
        buildBaseInfo(action, actionNode);
        SimpleDataType varType = SimpleDataType.valueOf(actionNode.get(KEY_VAR_TYPE).textValue());
        action.setVarType(varType);
        if(actionNode.get(KEY_VAR_ENTITY)!=null){
            action.setVarEntity(actionNode.get(KEY_VAR_ENTITY).textValue());
        }
        JsonNode valueNode = actionNode.get(KEY_DATA_BIND);
        RDataBind dataBind = RDataBindParser.buildDataBind(valueNode);
        action.setDataBind(dataBind);
        return action;
    }


}
