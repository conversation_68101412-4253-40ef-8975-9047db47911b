package com.deeppaas.rule.biz.parser;

import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.rule.biz.action.*;
import com.deeppaas.rule.biz.enums.ActionType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 规则解析器
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
public class RuleParser {
    public static List<RAction> parse(String json) {
        List<RAction> rActions = new ArrayList<>();
        if(!StringUtils.hasText(json)){
            return rActions;
        }
        JsonNode ruleNode;
        try {
            ruleNode = JsonHelper.readTree(json);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            throw RunException.error("规则解析异常");
        }
        if (ruleNode.isArray()) {
            Iterator<JsonNode> actionNodes = ruleNode.elements();
            while (actionNodes.hasNext()) {
                JsonNode actionNode = actionNodes.next();
                ActionType actionType = ActionType.valueOf(actionNode.get(RActionParser.KEY_TYPE).textValue());
                RAction rAction = switch (actionType) {
                    case VARIABLE -> RVarActionParser.buildAction(actionNode);
                    case DECISION -> RDecisionRActionParser.buildAction(actionNode);
                    case SETTER -> RSetActionParser.buildAction(actionNode);
                    case EXT_API -> RExtApiActionParser.buildAction(actionNode);
                    case DATA_GET -> RDataEntityQueryActionParser.buildGetAction(actionNode);
                    case DATA_LIST -> RDataEntityQueryActionParser.buildListAction(actionNode);
                    case DATA_PAGE -> RDataEntityQueryActionParser.buildPageAction(actionNode);
                    case DATA_SAVE -> RDataEntitySaveActionParser.buildAction(actionNode);
                    case DATA_UPDATE -> RDataEntityUpdateActionParser.buildAction(actionNode);
                    case DATA_SYNC -> RDataEntitySyncActionParser.buildAction(actionNode);
                    case DATA_DELETE -> RDataEntityDeleteActionParser.buildAction(actionNode);
                    case ERROR -> buildErrorAction(actionNode);
                    case RULE -> RRuleActionParser.buildAction(actionNode);
                    case USER_MSG_SEND -> RUserMsgSendActionParser.buildAction(actionNode);
                    case USER_TEMPLATE_MSG_SEND -> RUserTemplateMsgSendActionParser.buildAction(actionNode);
                    default -> null;
                };
                rActions.add(rAction);
            }
        }
        return rActions;
    }

    private static RAction buildErrorAction(JsonNode actionNode) {
        RErrorAction action = new RErrorAction();
        RActionParser.buildBaseInfo(action, actionNode);
        action.setMessage(actionNode.get("message").textValue());
        return action;
    }

}
