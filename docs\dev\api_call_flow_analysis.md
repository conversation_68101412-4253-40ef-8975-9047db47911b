# API调用流程分析报告

## 问题描述
用户反映没有看到"文件级处理API被调用"的日志输出，需要分析API调用流程。

## 问题根因分析

### 1. 真实问题
用户去掉JFJ配置后，系统确实调用了标准的 `localImage()` 方法，但是缺少了 `buildImage()` 方法的日志输出。

### 2. 完整API调用流程

#### 前端调用路径
1. **前端组件**: `UploadFiles.tsx`
   - 用户选择"服务器上传"
   - 调用 `/api/exactness/taskUpload/local/image`

2. **API控制器**: `ProjectTaskUploadApi.java`
   ```java
   @PostMapping("/local/image")
   public RestModel localImage(@BodyParam String filePath, @BodyParam String taskConfigId) {
       String customized = kubaoConfig.getCustomized();
       if (StringHelper.isNotEmpty(customized) && "jfj".equals(customized)) {
           projectTaskUploadService.localImageForJFJ(filePath, taskConfigId);  // JFJ定制版
       } else {
           projectTaskUploadService.localImage(filePath, taskConfigId);        // 标准版
       }
       return RestModel.success("上传成功！");
   }
   ```

3. **服务实现调用链**: `ProjectTaskUploadImpl.java`

   **标准版本调用链**:
   ```
   localImage()
   → 输出"📁📁📁 === 文件级处理API被调用 ==="
   → buildImage(taskId, configDTO)
   → 输出"🔨🔨🔨 === 标准版图像构建方法被调用 ==="
   ```

   **JFJ定制版调用链**:
   ```
   localImageForJFJ()
   → 输出"🏛️🏛️🏛️ === JFJ定制版文件级处理API被调用 ==="
   → buildImageForJFJ(taskId, configDTO)
   → 输出"🏗️🏗️🏗️ === JFJ定制版图像构建方法被调用 ==="
   ```

### 3. 案卷级处理调用
案卷级处理通过不同的API入口：
- **API**: `/api/uploadImageAndDataKey` (在 `API.java` 中)
- **调用链**:
  ```
  localImageAndDataKey()
  → 输出"🌟🌟🌟🌟🌟 === 案卷级处理API被调用 ==="
  → buildImage(taskId, configDTO, dataKey, partNo)
  → 输出"🚀🚀🚀 === 案卷级处理入口方法被调用 ==="
  ```

## 解决方案

### 1. 已添加的日志标识

#### JFJ定制版文件级处理
```java
public void localImageForJFJ(String filePath, String taskConfigId) {
    System.out.println("🏛️🏛️🏛️ === JFJ定制版文件级处理API被调用 === 🏛️🏛️🏛️");
    System.out.println("📂 文件路径: " + filePath);
    System.out.println("🆔 任务配置ID: " + taskConfigId);
    System.out.println("🏛️🏛️🏛️ === 开始JFJ定制版文件级处理流程 === 🏛️🏛️🏛️");
    // ...
}
```

#### JFJ定制版图像构建
```java
public void buildImageForJFJ(String taskId, ProjectTaskConfigDTO taskConfigDTO) {
    System.out.println("🏗️🏗️🏗️ === JFJ定制版图像构建方法被调用 === 🏗️🏗️🏗️");
    System.out.println("📋 任务ID: " + taskId);
    System.out.println("📂 文件路径: " + taskConfigDTO.getFilePath());
    System.out.println("🏗️🏗️🏗️ === 开始JFJ定制版图像构建流程 === 🏗️🏗️🏗️");
    // ...
}
```

### 2. 日志标识对照表

| 处理类型 | 日志标识 | 调用方法 |
|---------|---------|----------|
| 标准文件级处理API | 📁📁📁 === 文件级处理API被调用 === | `localImage()` |
| 标准图像构建 | 🔨🔨🔨 === 标准版图像构建方法被调用 === | `buildImage(taskId, configDTO)` |
| JFJ定制文件级处理API | 🏛️🏛️🏛️ === JFJ定制版文件级处理API被调用 === | `localImageForJFJ()` |
| JFJ定制图像构建 | 🏗️🏗️🏗️ === JFJ定制版图像构建方法被调用 === | `buildImageForJFJ()` |
| 案卷级处理API | 🌟🌟🌟🌟🌟 === 案卷级处理API被调用 === | `localImageAndDataKey()` |
| 案卷级处理入口 | 🚀🚀🚀 === 案卷级处理入口方法被调用 === | `buildImage(taskId, configDTO, dataKey, partNo)` |

### 3. 配置切换方案

如果需要使用标准版本而非JFJ定制版本，可以修改配置：

```properties
# 注释掉或删除JFJ定制配置
# kubao.ex.customized=jfj

# 或者设置为其他值
kubao.ex.customized=standard
```

## 测试验证

### 当前配置下的预期日志输出

#### 标准版本（kubao.ex.customized为空）
```
📁📁📁 === 文件级处理API被调用 === 📁📁📁
📂 文件路径: [实际路径]
🆔 任务配置ID: [实际ID]
📁📁📁 === 开始文件级处理流程 === 📁📁📁
🔨🔨🔨 === 标准版图像构建方法被调用 === 🔨🔨🔨
📋 任务ID: [实际任务ID]
📂 文件路径: [实际路径]
🔨🔨🔨 === 开始标准版图像构建流程 === 🔨🔨🔨
📝 档号规则: [配置的档号规则]
```

#### JFJ定制版本（kubao.ex.customized=jfj）
```
🏛️🏛️🏛️ === JFJ定制版文件级处理API被调用 === 🏛️🏛️🏛️
📂 文件路径: [实际路径]
🆔 任务配置ID: [实际ID]
🏛️🏛️🏛️ === 开始JFJ定制版文件级处理流程 === 🏛️🏛️🏛️
🏗️🏗️🏗️ === JFJ定制版图像构建方法被调用 === 🏗️🏗️🏗️
📋 任务ID: [实际任务ID]
📂 文件路径: [实际路径]
🏗️🏗️🏗️ === 开始JFJ定制版图像构建流程 === 🏗️🏗️🏗️
📝 正则规则配置:
  档号规则: (?<=aa).*(?=bb)
  件号规则: (?<=dd).*(?=ee)
```

#### 案卷级处理
```
🌟🌟🌟🌟🌟 === 案卷级处理API被调用 === 🌟🌟🌟🌟🌟
📂 文件路径: [实际路径]
🆔 任务配置ID: [实际ID]
🔑 数据键: [数据键]
📄 件号: [件号]
🌟🌟🌟🌟🌟 === 开始案卷级处理流程 === 🌟🌟🌟🌟🌟
🚀🚀🚀 === 案卷级处理入口方法被调用 === 🚀🚀🚀
📋 参数信息: taskId=[任务ID], dataKey=[数据键], partNo=[件号]
📋 配置信息: configId=[配置ID], filePath=[文件路径]
```

## 真实的图像构建流程

### 执行时机
图像构建**不是在上传时执行**，而是在**检测时执行**：

1. 用户点击"开始检测"按钮
2. 调用 `WorkImpl.executor()` 方法
3. 对每个配置调用 `buildExcel(taskId, taskConfigDTO, imageDataDOS)`
4. 在 `buildExcel` 中检查条件：
   - `configType == 2`（FileEnum.C，图像类型）
   - `imageDataDOS` 为空（还没有图像数据）
5. 如果满足条件，调用 `projectTaskUploadService.buildImage(taskId, taskConfigDTO)`

### 图像构建的触发条件
```java
if (Objects.equals(configType, FileEnum.C.getNum()) && CollectionUtils.isEmpty(imageDataDOS)) {
    projectTaskUploadService.buildImage(taskId, taskConfigDTO);
}
```

### 可能没有日志的原因
1. **配置类型不是图像类型**：`ruleConfigType` ≠ 2
2. **图像数据已存在**：之前已经构建过图像数据，`imageDataDOS` 不为空
3. **没有点击"开始检测"**：只上传了文件，没有开始检测流程

## 验证步骤

### 1. 检查配置类型
确保任务配置的 `ruleConfigType` 为 2（图像类型）

### 2. 清空图像数据（如果需要）
如果要重新构建图像数据，需要清空数据库中的图像数据

### 3. 观察检测日志
点击"开始检测"后，观察以下日志：
```
🔧🔧🔧 === buildExcel方法被调用 === 🔧🔧🔧
📋 任务ID: [任务ID]
📋 配置ID: [配置ID]
📋 配置类型: 2 (FileEnum.C=2)
📋 图像数据是否为空: true
📋 图像数据数量: 0
✅ 满足图像构建条件，调用buildImage方法
🔨🔨🔨 === 标准版图像构建方法被调用 === 🔨🔨🔨
```

## 总结

1. **问题原因**: 图像构建是在检测阶段执行，不是上传阶段
2. **解决方案**: 已添加详细的调试日志来跟踪执行流程
3. **验证方法**: 点击"开始检测"按钮，观察控制台日志输出
4. **关键条件**: 配置类型必须是图像类型(2)，且图像数据为空

## 更新时间
2024-12-19

## 相关文件
- `kpass-exactness-check/kpass-exactness-start/src/main/java/com/deeppaas/web/ProjectTaskUploadApi.java`
- `kpass-exactness-check/kpass-exactness-soa/src/main/java/com/deeppaas/task/data/impl/ProjectTaskUploadImpl.java`
- `kpass-exactness-check/kpass-exactness-start/src/main/resources/application-dev.properties`
