# PP-OCRv5升级指南

## 🎯 升级概述

本文档记录了从PP-OCRv4到PP-OCRv5的升级过程和配置变更。

## ✅ 升级完成状态

### **性能提升验证**
基于实际测试结果，PP-OCRv5相比PP-OCRv4有显著性能提升：

| 配置 | 文本块数 | 耗时(s) | 性能提升 | 检测版本 | 识别版本 |
|------|----------|---------|----------|----------|----------|
| **PP-OCRv4默认** | 4 | 1.360 | 基准 | PPOCRV4 | PPOCRV4 |
| **PP-OCRv5 Mobile** | 4 | 0.737 | **⚡ 46%提升** | PPOCRV5 | PPOCRV5 |
| **PP-OCRv5 Server** | 5 | 5.886 | 更高精度 | PPOCRV5 | PPOCRV5 |

### **推荐配置**
- **生产环境**: **PP-OCRv5 Mobile** - 最佳性能平衡（46%速度提升，相同精度）
- **高精度需求**: **PP-OCRv5 Server** - 更高精度（识别出更多文本块）

## 🔧 技术变更详情

### **1. RapidOCR API升级**

#### **从RapidOCR 1.x升级到3.0**
```python
# 旧版本 (rapidocr_onnxruntime)
from rapidocr_onnxruntime import RapidOCR
ocr = RapidOCR(det_use_cuda=True, rec_use_cuda=True)
result, elapse = ocr(image_path)

# 新版本 (rapidocr 3.0)
from rapidocr import RapidOCR
from rapidocr.utils.typings import OCRVersion, ModelType

params = {
    'Det.ocr_version': OCRVersion.PPOCRV5,
    'Det.model_type': ModelType.MOBILE,
    'Rec.ocr_version': OCRVersion.PPOCRV5,
    'Rec.model_type': ModelType.MOBILE
}
ocr = RapidOCR(params=params)
result = ocr(image_path)  # 返回RapidOCROutput对象
```

#### **RapidOCROutput对象处理**
```python
# 新的结果处理方式
if hasattr(result, 'txts'):
    txts = result.txts or []
    boxes = result.boxes or []
    scores = result.scores or []
    elapse = result.elapse
    
    # 构建兼容格式
    ocr_list = []
    for i, txt in enumerate(txts):
        box = boxes[i] if i < len(boxes) else None
        score = scores[i] if i < len(scores) else 1.0
        ocr_list.append([box, txt, score])
```

### **2. 配置文件变更**

#### **新增PP-OCRv5配置参数**
```yaml
ocr:
  engine_type: "rapidocr"
  model_name: "RapidOCR"
  config:
    # 新增PP-OCRv5配置
    ocr_version: "ppocrv5"  # ppocrv4 | ppocrv5
    model_type: "mobile"    # mobile | server
    
    # 保持原有配置
    use_angle_cls: true
    det_limit_side_len: 960
    rec_batch_num: 6
    force_cpu: false
```

#### **配置选项说明**
- **ocr_version**: 
  - `ppocrv4`: 使用PP-OCRv4模型（兼容性）
  - `ppocrv5`: 使用PP-OCRv5模型（推荐，46%性能提升）

- **model_type**:
  - `mobile`: 移动端模型，速度快（推荐生产环境）
  - `server`: 服务端模型，精度高（高精度需求场景）

## 📁 修改文件清单

### **核心代码文件**
1. **`src/models/rapidocr_engine.py`**
   - ✅ 更新导入路径：`from rapidocr import RapidOCR`
   - ✅ 添加Enum类型支持：`from rapidocr.utils.typings import OCRVersion, ModelType`
   - ✅ 实现PP-OCRv5配置参数构建
   - ✅ 添加RapidOCROutput对象处理逻辑
   - ✅ 保持向后兼容性

### **配置文件**
2. **`config/single_gpu_config.yaml`**
   - ✅ 添加`ocr_version: "ppocrv5"`配置
   - ✅ 添加`model_type: "mobile"`配置
   - ✅ 更新注释说明性能提升

3. **`config/dual_gpu_config.yaml`**
   - ✅ 同步单GPU配置的PP-OCRv5设置
   - ✅ 保持双GPU环境兼容性

4. **`src/core/config_manager.py`**
   - ✅ 更新默认配置为PP-OCRv5 Mobile
   - ✅ 添加新配置参数的默认值

## 🚀 部署说明

### **生产环境部署**
1. **确认环境**: 确保已安装RapidOCR 3.0和PP-OCRv5模型
2. **配置验证**: 检查配置文件中的`ocr_version`和`model_type`设置
3. **性能测试**: 建议先在测试环境验证性能提升
4. **逐步迁移**: 可通过配置文件快速切换PP-OCRv4/v5

### **配置切换**
```yaml
# 高性能配置（推荐）
ocr_version: "ppocrv5"
model_type: "mobile"

# 高精度配置
ocr_version: "ppocrv5"
model_type: "server"

# 兼容性配置
ocr_version: "ppocrv4"
model_type: "mobile"
```

## 📊 性能监控

### **关键指标**
- **处理时间**: PP-OCRv5 Mobile平均提升46%
- **识别精度**: 保持与PP-OCRv4相同或更高精度
- **GPU内存**: 无显著变化
- **文本块检测**: PP-OCRv5 Server可检测更多文本块

### **监控建议**
1. 监控平均处理时间变化
2. 对比识别准确率
3. 观察GPU内存使用情况
4. 记录错误率变化

## 🔄 回滚方案

如需回滚到PP-OCRv4，只需修改配置：
```yaml
ocr:
  config:
    ocr_version: "ppocrv4"  # 改回ppocrv4
    model_type: "mobile"
```

无需修改代码，引擎会自动适配。

## ✅ 验证清单

- [x] PP-OCRv5模型下载和验证
- [x] RapidOCR 3.0 API兼容性
- [x] 配置文件更新
- [x] 性能测试验证
- [x] 向后兼容性确认
- [x] 文档更新完成

## 📝 注意事项

1. **模型下载**: 首次使用PP-OCRv5时会自动下载模型文件
2. **内存使用**: PP-OCRv5模型文件略大，但运行时内存使用相似
3. **兼容性**: 保持与现有API完全兼容
4. **配置灵活性**: 可随时通过配置文件切换模型版本

---

**升级完成时间**: 2025-06-28  
**性能提升**: 46% (PP-OCRv5 Mobile vs PP-OCRv4)  
**推荐配置**: PP-OCRv5 Mobile  
