# RapidOCR迁移 Phase 2: 核心功能迁移计划

## 🎯 阶段目标
基于已成功配置的RapidOCR环境，开始核心OCR功能的迁移工作，实现从PaddleOCR+subprocess架构到RapidOCR+PyTorch统一架构的转换。

## ✅ Phase 1 完成状态
- ✅ 环境配置完全成功
- ✅ PyTorch 2.5.1+cu121 CUDA完全可用
- ✅ ONNX Runtime 1.19.2 GPU加速就绪
- ✅ TensorRT提供者可用 (额外性能提升)
- ✅ RapidOCR 1.4.4 初始化成功
- ✅ 无依赖冲突，所有组件完美共存

## 📋 Phase 2 任务规划

### **2.1 核心OCR功能迁移** (优先级: 高)

#### **2.1.1 基础OCR接口替换**
- **目标**: 将`src/models/ocr_worker.py`中的PaddleOCR调用替换为RapidOCR
- **当前架构**: PaddleOCR subprocess调用
- **目标架构**: RapidOCR直接调用
- **预期性能提升**: 12秒 → 2-3秒

**实施步骤**:
1. 创建`src/models/rapidocr_engine.py`新模块
2. 实现RapidOCR封装类，保持与现有接口兼容
3. 添加GPU加速配置和TensorRT优化
4. 实现错误处理和日志记录

#### **2.1.2 图像预处理集成**
- **目标**: 将图像预处理流程集成到统一架构中
- **当前问题**: subprocess导致图像重复读写，效率低下
- **解决方案**: 内存中图像处理管道

**实施步骤**:
1. 重构`src/services/archive_extraction_service.py`
2. 实现内存中图像预处理管道
3. 集成A4文档缩放和色彩通道过滤
4. 优化图像数据传递效率

#### **2.1.3 印章检测功能迁移**
- **目标**: 将印章检测从PaddleOCR迁移到RapidOCR生态
- **技术方案**: 研究RapidOCR的印章处理能力或集成第三方解决方案
- **关键挑战**: 保持印章检测精度

### **2.2 API接口兼容性保持** (优先级: 高)

#### **2.2.1 FastAPI接口保持不变**
- 保持现有`/extract_archive_elements`接口不变
- 确保Java后端集成无需修改
- 维持相同的请求/响应格式

#### **2.2.2 配置参数映射**
- 将PaddleOCR配置参数映射到RapidOCR
- 保持现有的GPU/CPU切换逻辑
- 添加TensorRT加速选项

### **2.3 性能优化** (优先级: 中)

#### **2.3.1 GPU加速优化**
- 启用ONNX Runtime CUDA提供者
- 配置TensorRT加速 (如果可用)
- 优化批处理推理

#### **2.3.2 内存管理优化**
- 消除subprocess通信开销
- 实现图像数据零拷贝传递
- 优化模型加载和缓存策略

### **2.4 测试验证** (优先级: 高)

#### **2.4.1 功能对比测试**
- 使用相同测试图像对比PaddleOCR和RapidOCR结果
- 验证档案要素提取准确性
- 确保印章检测功能正常

#### **2.4.2 性能基准测试**
- 测试处理时间对比
- 测试GPU内存使用情况
- 测试并发处理能力

## 🛠️ 技术实施细节

### **RapidOCR配置示例**
```python
from rapidocr_onnxruntime import RapidOCR

# GPU加速配置
ocr_engine = RapidOCR(
    det_use_cuda=True,      # 检测模型使用CUDA
    rec_use_cuda=True,      # 识别模型使用CUDA
    cls_use_cuda=True,      # 分类模型使用CUDA
    det_limit_side_len=960, # 检测边长限制
    rec_batch_num=6,        # 识别批处理大小
)
```

### **性能优化配置**
```python
import onnxruntime as ort

# 配置ONNX Runtime提供者优先级
providers = [
    ('TensorrtExecutionProvider', {
        'device_id': 0,
        'trt_max_workspace_size': **********,  # 2GB
        'trt_fp16_enable': True,
    }),
    ('CUDAExecutionProvider', {
        'device_id': 0,
        'arena_extend_strategy': 'kNextPowerOfTwo',
        'gpu_mem_limit': 4 * 1024 * 1024 * 1024,  # 4GB
        'cudnn_conv_algo_search': 'EXHAUSTIVE',
    }),
    'CPUExecutionProvider'
]
```

## 📊 预期成果

### **性能提升目标**
- **处理时间**: 12秒 → 2-3秒 (75%+ 提升)
- **内存使用**: 减少subprocess开销
- **并发能力**: 提升多请求处理能力
- **维护性**: 简化架构，减少调试复杂度

### **技术收益**
- ✅ 解决PyTorch/PaddlePaddle依赖冲突
- ✅ 统一深度学习框架栈
- ✅ 启用TensorRT额外加速
- ✅ 简化部署和维护

## 🗓️ 实施时间表

### **Week 1: 核心迁移**
- Day 1-2: 创建RapidOCR引擎封装
- Day 3-4: 基础OCR功能替换
- Day 5: 初步测试和调试

### **Week 2: 功能完善**
- Day 1-2: 图像预处理集成
- Day 3-4: 印章检测功能迁移
- Day 5: API接口兼容性测试

### **Week 3: 优化和验证**
- Day 1-2: 性能优化和TensorRT配置
- Day 3-4: 全面功能测试
- Day 5: 性能基准测试和文档更新

## 🚨 风险评估

### **技术风险**
- **中等风险**: 印章检测功能迁移可能需要额外方案
- **低风险**: RapidOCR识别精度与PaddleOCR差异
- **低风险**: TensorRT配置复杂性

### **缓解措施**
- 保留PaddleOCR作为备用方案
- 实施渐进式迁移，确保每步可回滚
- 充分的测试验证

---

**文档版本**: v1.0  
**创建时间**: 2024-12-27  
**状态**: 待执行  
**前置条件**: ✅ Phase 1 环境配置完成
