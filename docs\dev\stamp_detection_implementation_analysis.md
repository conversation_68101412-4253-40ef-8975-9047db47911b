# 现有印章检测识别功能实现分析文档

## 📅 文档创建时间
2025-01-05

## 🎯 文档目的
记录当前系统中印章检测识别功能的完整实现方式，为后续精准重构提供参考，避免破坏已测试通过的逻辑。

## 🏗️ 当前架构概览

### 1. 印章检测模型集成

#### 1.1 PaddleX模型集成 (`src/models/seal_models.py`)
```python
class SealDetectionModel:
    def __init__(self, model_name: str = "PP-OCRv4_mobile_seal_det"):
        """初始化PaddleX印章检测模型"""
        self.model_name = model_name
        self.model = None
        self._load_model()
    
    def detect_seals(self, image: Union[str, Path, Image.Image]) -> List[Dict[str, Any]]:
        """检测图像中的印章区域"""
        # 支持PIL.Image.Image输入类型
        if isinstance(image, Image.Image):
            # PIL图像转换为numpy数组
            image_array = np.array(image)
            # 如果是RGB，转换为BGR（OpenCV格式）
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                image_array = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            # 保存临时文件用于预测
            temp_path = "temp_seal_detection_pil.jpg"
            cv2.imwrite(temp_path, image_array)
            image_path = temp_path
        
        # PaddleX预测返回生成器
        results = self.model.predict(image_path)
        
        # 解析检测结果
        detected_seals = []
        for result in results:
            if hasattr(result, 'dt_polys') and hasattr(result, 'dt_scores'):
                dt_polys = result.dt_polys
                dt_scores = result.dt_scores
                
                for poly, score in zip(dt_polys, dt_scores):
                    if score >= confidence_threshold:
                        # 转换多边形为边界框
                        x_coords = poly[:, 0]
                        y_coords = poly[:, 1]
                        bbox = [int(min(x_coords)), int(min(y_coords)), 
                               int(max(x_coords)), int(max(y_coords))]
                        
                        detected_seals.append({
                            'bbox': bbox,
                            'confidence': float(score),
                            'polygon': poly.tolist()
                        })
        
        return detected_seals
```

#### 1.2 OpenCV印章检测 (已移除，但逻辑保留)
- 基于HSV颜色空间的红色/蓝色检测
- 轮廓检测和形状过滤
- 面积和圆形度阈值过滤

### 2. 图像预处理管道 (`src/utils/image_preprocessor.py`)

#### 2.1 预处理配置结构
```python
preprocessing_config = {
    'stamp_processing': {
        'enabled': True/False,
        'model_name': 'PP-OCRv4_mobile_seal_det',
        'confidence_threshold': 0.8,
        'enhancement_methods': ['redblue_removal'],
        'create_multiple_versions': True,
        'enable_stats': True
    }
}
```

#### 2.2 印章处理流程
```python
def _process_stamps_if_enabled(self, image_path, config):
    """印章处理主流程"""
    if not config.get('stamp_processing', {}).get('enabled', False):
        return image_path, {}
    
    # 1. 印章检测
    detected_stamps = self._detect_stamps(image_path, config)
    
    # 2. 创建处理版本
    processed_versions = self._create_stamp_processed_versions(
        image_path, detected_stamps, config
    )
    
    # 3. 选择最佳版本
    best_version = self._select_best_processed_version(processed_versions)
    
    return best_version, stamp_stats
```

#### 2.3 红蓝通道去除算法
```python
def _remove_redblue_channel(self, image_path: str, detected_stamps: List[Dict]) -> str:
    """基于HSV颜色空间的红蓝通道去除"""
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 红色范围 (HSV)
    red_lower1 = np.array([0, 50, 50])
    red_upper1 = np.array([10, 255, 255])
    red_lower2 = np.array([170, 50, 50])
    red_upper2 = np.array([180, 255, 255])
    
    # 蓝色范围 (HSV)
    blue_lower = np.array([100, 50, 50])
    blue_upper = np.array([130, 255, 255])
    
    # 创建掩码并应用
    red_mask1 = cv2.inRange(hsv, red_lower1, red_upper1)
    red_mask2 = cv2.inRange(hsv, red_lower2, red_upper2)
    blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)
    
    combined_mask = red_mask1 + red_mask2 + blue_mask
    
    # 将检测到的颜色区域替换为白色
    image[combined_mask > 0] = [255, 255, 255]
    
    # 保存处理后的图像
    processed_path = image_path.replace('.jpg', '_redblue_removed.jpg')
    cv2.imwrite(processed_path, image)
    
    return processed_path
```
