# Java后端集成测试计划

## 📋 测试概述

### 测试目标
验证Java后端集成的档案要素检查功能是否正常工作，包括：
1. HTTP客户端调用Python服务
2. 错误结果解析和持久化
3. 与现有工作流的集成

### 测试环境要求
- **Python服务**: `g:\workshop\RUYI\ruyi-aimp` 运行在 `http://localhost:8080`
- **Java后端**: `g:\Java-projects\hegui\deeppaas2-java` 
- **数据库**: PostgreSQL (ruyi数据库)
- **测试数据**: Excel文件和对应的档案图像文件

## 🧪 测试用例设计

### 测试用例1：基本功能测试
**目标**: 验证档案要素检查的基本流程

**前置条件**:
1. Python AI服务正常运行
2. 准备测试Excel数据（包含档案要素字段）
3. 准备对应的档案图像文件

**测试步骤**:
1. 创建包含档案要素规则的任务配置
2. 上传Excel文件和图像文件
3. 执行任务检查
4. 验证错误结果是否正确保存

**预期结果**:
- HTTP调用成功
- 错误结果正确解析
- 数据库中保存了正确的错误记录

### 测试用例2：错误处理测试
**目标**: 验证异常情况的处理

**测试场景**:
1. Python服务不可用
2. 网络超时
3. 响应格式错误
4. 空数据处理

**预期结果**:
- 异常被正确捕获
- 不影响其他规则的执行
- 记录适当的错误日志

### 测试用例3：数据格式验证
**目标**: 验证数据转换的正确性

**验证点**:
1. Excel数据JSON序列化
2. 图像路径列表格式
3. 响应结果解析
4. TaskErrorResultDO字段映射

## 🔧 测试数据准备

### Excel测试数据格式
```json
[
  {
    "dataKey": "2024-001",
    "rowNum": 2,
    "title": "关于加强档案管理的通知",
    "responsible_party": "办公室",
    "document_number": "办发[2024]001号",
    "issue_date": "2024-01-15"
  },
  {
    "dataKey": "2024-002", 
    "rowNum": 3,
    "title": "年度工作总结报告",
    "responsible_party": "人事部",
    "document_number": "人发[2024]002号",
    "issue_date": "2024-02-20"
  }
]
```

### 图像文件要求
- 格式: JPG/PNG
- 分辨率: 建议2000x3000以上
- 内容: 包含清晰的档案要素信息
- 命名: 与Excel中的档号对应

## 🚀 测试执行步骤

### 步骤1: 启动服务
```bash
# 启动Python AI服务
cd g:\workshop\RUYI\ruyi-aimp
conda activate py39ruyi
python start.py

# 启动Java后端服务
cd g:\Java-projects\hegui\deeppaas2-java
# 使用IDE启动或Maven命令
```

### 步骤2: 准备测试数据
1. 创建测试Excel文件
2. 准备对应的档案图像文件
3. 上传到系统中

### 步骤3: 配置测试任务
1. 创建新的项目任务
2. 选择档案要素检查规则
3. 配置检查参数

### 步骤4: 执行测试
1. 运行任务检查
2. 观察日志输出
3. 检查数据库结果

### 步骤5: 结果验证
1. 验证HTTP调用日志
2. 检查错误结果表数据
3. 确认错误统计正确

## 📊 测试结果记录

### 成功标准
- [ ] HTTP客户端成功调用Python服务
- [ ] 响应结果正确解析
- [ ] 错误记录正确保存到数据库
- [ ] 错误类型和描述准确
- [ ] 不影响其他规则执行
- [ ] 异常情况处理正常

### 性能指标
- HTTP调用响应时间: < 30秒
- 错误结果处理时间: < 5秒
- 数据库保存时间: < 2秒

## 🐛 问题排查指南

### 常见问题
1. **HTTP连接失败**
   - 检查Python服务是否启动
   - 验证端口8080是否可访问
   - 检查防火墙设置

2. **JSON序列化错误**
   - 验证Excel数据格式
   - 检查特殊字符处理
   - 确认编码格式

3. **数据库保存失败**
   - 检查数据库连接
   - 验证字段长度限制
   - 确认事务处理

### 调试方法
1. 启用详细日志输出
2. 使用断点调试
3. 检查网络请求详情
4. 验证数据库事务状态

## 📝 测试报告模板

### 测试执行记录
- 测试时间: ____
- 测试环境: ____
- 测试数据: ____
- 执行结果: ____

### 发现问题
- 问题描述: ____
- 重现步骤: ____
- 解决方案: ____
- 验证结果: ____

### 改进建议
- 性能优化: ____
- 错误处理: ____
- 用户体验: ____
