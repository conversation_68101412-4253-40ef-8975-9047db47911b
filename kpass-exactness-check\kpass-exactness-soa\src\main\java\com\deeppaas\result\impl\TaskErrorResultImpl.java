package com.deeppaas.result.impl;

import com.deeppaas.FileEnum;
import com.deeppaas.common.helper.DateHelper;
import com.deeppaas.common.helper.FileHelper;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.StringHelper;
import com.deeppaas.result.convert.TaskErrorResultConvert;
import com.deeppaas.result.dao.TaskErrorResultDao;
import com.deeppaas.result.dto.TaskErrorResultDTO;
import com.deeppaas.result.entity.TaskErrorResultDO;
import com.deeppaas.result.enums.ErrorResultType;
import com.deeppaas.result.service.TaskErrorResultService;
import com.deeppaas.result.vo.TaskStatisticsRuleVo;
import com.deeppaas.result.vo.TaskStatisticsVo;
import com.deeppaas.rule.dto.PublicRuleDTO;
import com.deeppaas.rule.enums.RuleImageEnums;
import com.deeppaas.rule.service.PublicRuleService;
import com.deeppaas.task.config.dto.ProjectTaskConfigDTO;
import com.deeppaas.task.config.service.ProjectTaskConfigService;
import com.deeppaas.task.data.dto.ProjectTaskFormDataDTO;
import com.deeppaas.task.data.dto.ProjectTaskImageDataDTO;
import com.deeppaas.task.data.service.ProjectTaskFormDataService;
import com.deeppaas.task.data.service.ProjectTaskImageDataService;
import com.deeppaas.task.info.dto.ProjectTaskInfoDTO;
import com.deeppaas.task.info.service.ProjectTaskInfoService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TaskErrorResultImpl implements TaskErrorResultService {

    @Autowired
    private TaskErrorResultDao taskErrorResultDao;

    @Autowired
    private TaskErrorResultConvert taskErrorResultConvert;

    @Autowired
    private ProjectTaskImageDataService projectTaskImageDataService;

    @Autowired
    private ProjectTaskFormDataService projectTaskFormDataService;

    @Autowired
    private PublicRuleService publicRuleService;

    @Autowired
    private ProjectTaskConfigService projectTaskConfigService;

    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;

    @Override
    public List<TaskErrorResultDTO> list() {
        return taskErrorResultConvert.toDto(taskErrorResultDao.findAll());
    }

    @Override
    public TaskErrorResultDTO findById(String id) {
        return taskErrorResultConvert.toDto(taskErrorResultDao.getById(id));
    }

    @Override
    public List<TaskErrorResultDTO> findByTaskId(String taskId) {
        return taskErrorResultConvert.toDto(taskErrorResultDao.findByTaskId(taskId));
    }

    @Override
    public List<TaskErrorResultDTO> findByTaskIdAndErrorType(String taskId, Integer errorType) {
        return taskErrorResultConvert.toDto(taskErrorResultDao.findByTaskIdAndErrorType(taskId, errorType));
    }

    @Override
    public List<TaskErrorResultDTO> findByTaskIdAndErrorTypeAndTaskConfigId(String taskId, String taskConfigId, Integer errorType) {
        return taskErrorResultConvert.toDto(taskErrorResultDao.findByTaskIdAndErrorTypeAndTaskConfigId(taskId, errorType, taskConfigId));
    }

    @Override
    public List<TaskErrorResultDTO> findByErrorTypeAndTaskConfigIdGroup(String taskConfigId, Integer errorType) {
        return taskErrorResultConvert.toDto(taskErrorResultDao.findByErrorTypeAndTaskConfigIdGroup(errorType, taskConfigId));
    }


    @Override
    public void save(TaskErrorResultDO taskErrorResultDO) {
        taskErrorResultDao.save(taskErrorResultDO);
    }

    @Override
    public void saves(List<TaskErrorResultDO> taskErrorResultDOs) {
        if (CollectionUtils.isEmpty(taskErrorResultDOs))
            return;

        taskErrorResultDao.saveAll(taskErrorResultDOs);
    }

    @Override
    public void delById(String id) {
        taskErrorResultDao.deleteById(id);
    }

    @Override
    public List<TaskStatisticsVo> statisticsImage(String taskId) {
        List<ProjectTaskImageDataDTO> lists = projectTaskImageDataService.findByTaskId(taskId);
        Integer count = lists.stream().mapToInt(ProjectTaskImageDataDTO::getImageCount).sum();
        List<TaskStatisticsVo> taskStatisticsVos = Lists.newArrayList();
        List<TaskErrorResultDTO> list = findByTaskIdAndErrorType(taskId, ErrorResultType.IMAGE.getNum());
        Map<String, List<TaskErrorResultDTO>> listMap = list.stream().collect(Collectors.groupingBy(item -> item.getRuleName() + "&_&" + item.getRuleType() + "&_&" + item.getErrorType()));
        List<TaskStatisticsVo> finalTaskStatisticsVos = taskStatisticsVos;
        listMap.forEach((k, y) -> {
            String[] split = k.split("&_&");
            if (split.length > 2) {
                TaskStatisticsVo taskStatisticsVo = new TaskStatisticsVo();
                String ruleName = split[0];
                String ruleType = split[1];
                String errorType = split[2];
                taskStatisticsVo.setRuleName(ruleName);
                taskStatisticsVo.setRuleType(ruleType);
                taskStatisticsVo.setErrorType(Integer.parseInt(errorType));
                taskStatisticsVo.setErrorNum(y.size());
                taskStatisticsVo.setLists(y);
                BigDecimal a = new BigDecimal(String.valueOf(y.size()));
                BigDecimal b = new BigDecimal(String.valueOf(count));
                taskStatisticsVo.setErrorRate(a.divide(b, 2, BigDecimal.ROUND_HALF_UP).doubleValue() * 100);

                finalTaskStatisticsVos.add(taskStatisticsVo);
            }
        });
        final Integer[] pageSizeCount = {0};
        lists.forEach(item -> {
            String pizecount = item.getPageSizeCount();
            if (StringHelper.isNotEmpty(pizecount)) {
                Map<String, Object> json2map = JsonHelper.json2map(pizecount);
                Collection<Object> list1 = json2map.values();
                for (Object l : list1) {
                    if (StringHelper.isNum(StringHelper.toString(l))) {
                        pageSizeCount[0] = pageSizeCount[0] + Integer.parseInt(l.toString());
                    }
                }
            }
        });

        taskStatisticsVos = taskStatisticsVos.stream().sorted(Comparator.comparing(TaskStatisticsVo::getRuleType).reversed()).collect(Collectors.toList());

        TaskStatisticsVo taskStatisticsVo1 = new TaskStatisticsVo();
        taskStatisticsVo1.setRuleName("篇幅统计总数");
        taskStatisticsVo1.setRuleType("其他");
        taskStatisticsVo1.setErrorNum(pageSizeCount[0]);
        taskStatisticsVos.add(taskStatisticsVo1);
        TaskStatisticsVo taskStatisticsVo = new TaskStatisticsVo();
        taskStatisticsVo.setRuleName("图片总数量");
        taskStatisticsVo.setRuleType("其他");
        taskStatisticsVo.setErrorNum(count);
        taskStatisticsVos.add(taskStatisticsVo);
        return taskStatisticsVos;
    }

    @Override
    public void delTaskId(String taskId) {
        taskErrorResultDao.delTaskId(taskId);
    }

    @Override
    public void delTaskIdAndNotAiCheck(String taskId) {
        taskErrorResultDao.delTaskIdAndNotAiCheck(taskId);
    }

    @Override
    public List<String> errorImage(String taskId) {
        List<TaskErrorResultDO> list = taskErrorResultDao.findByTaskIdAndErrorType(taskId, ErrorResultType.IMAGE.getNum());
        Set<String> stringSet = list.stream().filter(item -> item.getDataKey() != null).map(TaskErrorResultDO::getFieldName).collect(Collectors.toSet());
        return stringSet.stream().toList();
    }

    @Override
    public List<TaskErrorResultDTO> errorImageDetail(String taskId, String fieldName) {
        List<TaskErrorResultDO> list = taskErrorResultDao.findByTaskIdAndFieldName(taskId, fieldName);
        list.forEach(item -> {
            String ruleName=item.getRuleName();
            if(!Objects.equals(ruleName,RuleImageEnums.STAINVALUE.getValueName())&&!Objects.equals(ruleName,RuleImageEnums.HOLE.getValueName())&&!Objects.equals(ruleName,RuleImageEnums.STAINVALUE.getValueName())){
                item.setErrorCoordinate(item.getErrorFileValue());
            }

        });
        return taskErrorResultConvert.toDto(list);
    }

    /**
     * 逻辑规则
     */
    @Override
    public List<TaskStatisticsRuleVo> statisticsRule(String taskId, String taskConfigId) {
        double taskImageDataDTO = projectTaskFormDataService.findByTaskConfigIdCount(taskConfigId);
        ProjectTaskConfigDTO taskInfoDTO = projectTaskConfigService.findById(taskConfigId);
        List<PublicRuleDTO> lists = publicRuleService.findByLibraryId(taskInfoDTO.getRuleTemplateId()).stream().filter(item->Objects.equals(item.getRuleType(),ErrorResultType.FILE.getNum())).collect(Collectors.toList());
        Map<String, TaskStatisticsRuleVo> map = new HashMap<>();

        List<TaskStatisticsRuleVo> taskStatisticsVos = Lists.newArrayList();
        List<TaskErrorResultDTO> list = findByErrorTypeAndTaskConfigIdGroup(taskConfigId, ErrorResultType.FILE.getNum());
        Map<String, List<TaskErrorResultDTO>> listMap = list.stream().collect(Collectors.groupingBy(item -> item.getFieldName()));

        List<TaskStatisticsRuleVo> finalTaskStatisticsVos = taskStatisticsVos;
        lists.forEach(item -> {
            List<String> fieldsNames = item.getRuleFieldsName();
            fieldsNames.forEach(items -> {
                if (map.get(items) == null) {
                    List<TaskErrorResultDTO> list1 = listMap.get(items);
                    TaskStatisticsRuleVo taskStatisticsRuleVo = new TaskStatisticsRuleVo();
                    taskStatisticsRuleVo.setFileName(items);
                    taskStatisticsRuleVo.setErrorNum(0);
                    taskStatisticsRuleVo.setErrorRate(0d);
                    if (!CollectionUtils.isEmpty(list1)) {
                        Integer size = list1.size();
                        BigDecimal a = new BigDecimal(String.valueOf(Double.valueOf(size)));
                        BigDecimal b = new BigDecimal(String.valueOf(taskImageDataDTO));
                        taskStatisticsRuleVo.setErrorNum(list1.size());
                        taskStatisticsRuleVo.setErrorRate(a.divide(b, 2, BigDecimal.ROUND_HALF_UP).doubleValue() * 100);

                    }
                    finalTaskStatisticsVos.add(taskStatisticsRuleVo);
                    map.put(items, taskStatisticsRuleVo);
                }

            });

        });

        // 🔍 移除档案要素检查统计 - 档案要素检查现在只在逻辑审查统计中显示
        // addArchiveElementsStatistics(taskId, taskConfigId, taskImageDataDTO, finalTaskStatisticsVos, map);

        taskStatisticsVos = taskStatisticsVos.stream().sorted(Comparator.comparingInt(TaskStatisticsRuleVo::getErrorNum).reversed()).collect(Collectors.toList());
        return taskStatisticsVos;
    }

    /**
     * 添加档案要素检查统计到规则审查统计中
     */
    private void addArchiveElementsStatistics(String taskId, String taskConfigId, double totalCount,
                                            List<TaskStatisticsRuleVo> taskStatisticsVos, Map<String, TaskStatisticsRuleVo> map) {
        // 获取档案要素检查的错误结果
        List<TaskErrorResultDTO> archiveElementsErrors = findByTaskIdAndErrorTypeAndTaskConfigId(taskId, taskConfigId, ErrorResultType.RULE.getNum())
            .stream()
            .filter(error -> "archiveElements".equals(error.getRuleType()))
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(archiveElementsErrors)) {
            // 按字段名称分组统计
            Map<String, List<TaskErrorResultDTO>> fieldErrorMap = archiveElementsErrors.stream()
                .collect(Collectors.groupingBy(TaskErrorResultDTO::getFieldName));

            fieldErrorMap.forEach((fieldName, errors) -> {
                if (map.get(fieldName) == null) {
                    TaskStatisticsRuleVo ruleVo = new TaskStatisticsRuleVo();
                    ruleVo.setFileName(fieldName);
                    ruleVo.setErrorNum(errors.size());

                    // 计算错误率
                    BigDecimal errorCount = new BigDecimal(String.valueOf(errors.size()));
                    BigDecimal total = new BigDecimal(String.valueOf(totalCount));
                    double errorRate = errorCount.divide(total, 4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100;
                    ruleVo.setErrorRate(Double.valueOf(String.format("%.2f", errorRate)));

                    taskStatisticsVos.add(ruleVo);
                    map.put(fieldName, ruleVo);
                }
            });
        }
    }

    @Override
    public List<TaskErrorResultDO> exportData(String taskId) {
        List<TaskErrorResultDO> list = taskErrorResultDao.findByTaskId(taskId);

        return list;
    }

    @Override
    public List<TaskStatisticsVo> statistics(String taskId) {
        List<ProjectTaskImageDataDTO> lists = projectTaskImageDataService.findByTaskId(taskId);
        Integer count = lists.stream().mapToInt(ProjectTaskImageDataDTO::getImageCount).sum();
        List<TaskStatisticsVo> taskStatisticsVos = Lists.newArrayList();
        List<TaskErrorResultDTO> list = findByTaskId(taskId);
        Map<String, List<TaskErrorResultDTO>> listMap = list.stream().collect(Collectors.groupingBy(item -> item.getRuleName() + "&_&" + item.getRuleType() + "&_&" + item.getErrorType()));
        List<TaskStatisticsVo> finalTaskStatisticsVos = taskStatisticsVos;
        listMap.forEach((k, y) -> {
            String[] split = k.split("&_&");
            if (split.length > 2) {
                TaskStatisticsVo taskStatisticsVo = new TaskStatisticsVo();
                String ruleName = split[0];
                String ruleType = split[1];
                String errorType = split[2];
                taskStatisticsVo.setRuleName(ruleName);
                taskStatisticsVo.setRuleType(ruleType);
                taskStatisticsVo.setErrorType(Integer.parseInt(errorType));
                taskStatisticsVo.setErrorNum(y.size());
                taskStatisticsVo.setLists(y);
                BigDecimal a = new BigDecimal(String.valueOf(y.size()));
                BigDecimal b = new BigDecimal(String.valueOf(count));
                taskStatisticsVo.setErrorRate( Double.valueOf(String.format("%.2f", a.divide(b, 2, BigDecimal.ROUND_HALF_UP).doubleValue() * 100 )));

                finalTaskStatisticsVos.add(taskStatisticsVo);
            }


        });
        taskStatisticsVos = taskStatisticsVos.stream().sorted(Comparator.comparing(TaskStatisticsVo::getRuleType).reversed()).collect(Collectors.toList());
        return taskStatisticsVos;
    }

    @Override
    public Map<String, Object> pdfDataMap(String taskId) {
        Map<String, Object> dataMap = new HashMap<>();

        List<TaskErrorResultDO> list = exportData(taskId);
        List<ProjectTaskConfigDTO> taskConfigDTOs = projectTaskConfigService.findByTaskId(taskId);
        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);


        List<ProjectTaskFormDataDTO> formDataDTOS = projectTaskFormDataService.findByTaskId(taskId);
        List<ProjectTaskImageDataDTO> imageDataDTOS = projectTaskImageDataService.findByTaskId(taskId);

        //规则错的结果
        List<TaskErrorResultDO> ruleResult = list.stream().filter(item ->
                Objects.equals(item.getErrorType(), ErrorResultType.FILE.getNum())).collect(Collectors.toList());
        //规则错的结果
        List<TaskErrorResultDO> logicRuleResult = list.stream().filter(item ->
                Objects.equals(item.getErrorType(), ErrorResultType.RULE.getNum())).collect(Collectors.toList());
        //日志错的结果
        List<TaskErrorResultDO> imageeResult = list.stream().filter(item ->
                Objects.equals(item.getErrorType(), ErrorResultType.IMAGE.getNum())).collect(Collectors.toList());

        dataMap.put("reportTime", DateHelper.getNowTimeStr());
        long entryCount = formDataDTOS.size();
        //条目数量
        dataMap.put("itemNum", entryCount);
        //各表条目数
        StringBuffer everyPageNum = new StringBuffer();
        taskConfigDTOs.forEach(item -> {
            if (Objects.equals(item.getRuleConfigType(), FileEnum.A.getNum()) || Objects.equals(item.getRuleConfigType(), FileEnum.B.getNum())) {
                everyPageNum.append(item.getFileName() + ":" + formDataDTOS.stream().filter(it -> Objects.equals(it.getTaskConfigId(), item.getId())).toList().size() + "   ");
            }
        });
        dataMap.put("itemDetails", everyPageNum);
        //图片总数量
        int imageCount = imageDataDTOS.stream().mapToInt(ProjectTaskImageDataDTO::getImageCount).sum();
        dataMap.put("pageTotalNum", imageCount);
        //文件夹数量
        int fileCount = imageDataDTOS.size();
        dataMap.put("totalNum", fileCount);
        //数据总数量
        String fileSize = FileHelper.buildFileSize(imageDataDTOS.stream().filter(it -> it.getImageSize() != null).mapToLong(ProjectTaskImageDataDTO::getImageSize).sum());
        dataMap.put("dataSize", fileSize);
        //抽检比例
        Integer exactnessScale = taskInfoDTO.getExactnessScale();
        dataMap.put("checkRatio", exactnessScale + "%");

        dataMap.put("rowsNum", entryCount);
        //连续性检查
        int logicPageNum = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.CONTINUITY.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();
        dataMap.put("logicPageNum", logicPageNum);
        //错误规则数量
        //Integer rule = ruleResult.stream().map(TaskErrorResultDO::getFieldName).collect(Collectors.toSet()).size();
        dataMap.put("ruleErrorNum", ruleResult.stream().map(item -> item.getTaskConfigId() + "^" + item.getErrorRow()).collect(Collectors.toSet()).size());


        dataMap.put("logicErrorNum", logicRuleResult.stream().map(item -> item.getTaskConfigId() + "^" + item.getErrorRow()).collect(Collectors.toSet()).size());

        //图像格式错误
        int fomartCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.FORMAT.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();
        dataMap.put("flagFormat", fomartCount);
        //DPI
        int dpiCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.DPI.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();
        dataMap.put("flagDpi", dpiCount);
        //KB
        int kbCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.KB.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();
        dataMap.put("flagKb", kbCount);
        //空文件
        int blankFilesCheckCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.BLANKFILESCHECK.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();
        dataMap.put("flagEmptyfolder", blankFilesCheckCount);
        //幅面统计
        Map<String, Integer> pageSizeCount = new HashMap<>();
        imageDataDTOS.forEach(item -> {
            String pageSize = item.getPageSizeCount();
            if (StringHelper.isNotEmpty(pageSize)) {
                Map<String, Object> pageSizeMap = JsonHelper.json2map(pageSize);
                pageSizeMap.forEach((k, y) -> {
                    String count = StringHelper.toString(pageSizeCount.get(k));
                    if (StringHelper.isEmpty(count)) {
                        count = "0";
                    }
                    pageSizeCount.put(k, Integer.valueOf(count) + Integer.parseInt(y.toString()));
                });

            }
        });
        StringBuffer stringBuffer = new StringBuffer();
        pageSizeCount.forEach((k, y) -> {
            stringBuffer.append(k + "：" + y + "  ");
        });
        dataMap.put("paperType", stringBuffer);
        //重复图片数
        int repeatimageCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.REPEATIMAGE.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();
        dataMap.put("flagDuplicateimage", repeatimageCount);
        //空白数量数
        int blankCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.BLANK.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();
        dataMap.put("flagSpacerimage", blankCount);
        //内容质量不合格数
        int contentCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.BLANK.getTypeName(), item.getRuleType())).collect(Collectors.toList()).size();
        dataMap.put("qualityNum", contentCount);
        //图像视觉角度错误数
        int angleCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.BIAS.getTypeName(), item.getRuleType())).collect(Collectors.toList()).size();

        dataMap.put("visualNum", angleCount);
        //瑕疵图像数
        int flawCount = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.EDGEREMOVE.getTypeName(), item.getRuleType())).map(TaskErrorResultDO::getFieldName).collect(Collectors.toSet()).size();
        dataMap.put("weaknessNum", flawCount);
        //格式检查合格率
        int formatRate = imageeResult.stream().filter(item -> Objects.equals(RuleImageEnums.FORMAT.getValueName(), item.getRuleName())).collect(Collectors.toList()).size();

        if (formatRate != 0) {
            BigDecimal a = new BigDecimal(Double.valueOf(formatRate).toString());
            BigDecimal b = new BigDecimal(imageCount);
            dataMap.put("formatRate", a.divide(b, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).intValue() + "%");
        }

        //图像合格率
        if (imageCount != 0) {
            int errorCount = imageeResult.stream().map(TaskErrorResultDO::getFieldName).collect(Collectors.toSet()).size() / imageCount * 100;
            dataMap.put("imageRate", (100 - errorCount) + "%");
        }
        //开始时间
        dataMap.put("startTime", DateHelper.localDateTime2str(taskInfoDTO.getStartTime()));
        //结束时间
        dataMap.put("endTime", DateHelper.localDateTime2str(taskInfoDTO.getEndTime()));
        dataMap.put("checkTime", DateHelper.localDateTime2str(taskInfoDTO.getStartTime()));
        return dataMap;
    }


    @Override
    public List<TaskStatisticsVo> statisticsLogicRule(String taskId, String taskConfigId) {
        double taskImageDataDTOs = projectTaskFormDataService.findByTaskConfigIdCount(taskConfigId);
        List<TaskStatisticsVo> taskStatisticsVos = Lists.newArrayList();

        // 🔍 添加调试信息
        System.out.println("🔍🔍🔍 === statisticsLogicRule 开始 === 🔍🔍🔍");
        System.out.println(String.format("taskId: %s", taskId));
        System.out.println(String.format("taskConfigId: %s", taskConfigId));
        System.out.println(String.format("ErrorResultType.RULE.getNum(): %d", ErrorResultType.RULE.getNum()));

        // 🔍 先查询所有该taskId的错误记录，不限制taskConfigId和errorType
        List<TaskErrorResultDTO> allErrors = findByTaskId(taskId);
        System.out.println(String.format("该taskId的所有错误记录数量: %d", allErrors.size()));

        // 打印前几条记录的详细信息
        allErrors.stream().limit(5).forEach(error -> {
            System.out.println(String.format("错误记录: taskConfigId=%s, errorType=%d, ruleName=%s, ruleType=%s",
                error.getTaskConfigId(), error.getErrorType(), error.getRuleName(), error.getRuleType()));
        });

        List<TaskErrorResultDTO> list = findByTaskIdAndErrorTypeAndTaskConfigId(taskId, taskConfigId, ErrorResultType.RULE.getNum());
        System.out.println(String.format("按条件筛选后的错误记录数量: %d", list.size()));
        System.out.println("🔍🔍🔍 === statisticsLogicRule 查询完成 === 🔍🔍🔍");

        // 🔍 分离档案要素检查和其他规则错误
        System.out.println("🔍🔍🔍 === 开始分离档案要素错误 === 🔍🔍🔍");
        System.out.println(String.format("总错误数量: %d", list.size()));

        // 打印所有错误的ruleType，用于调试
        list.forEach(error -> {
            System.out.println(String.format("错误记录: ruleName=%s, ruleType=%s, fieldName=%s",
                error.getRuleName(), error.getRuleType(), error.getFieldName()));
        });

        List<TaskErrorResultDTO> archiveElementsErrors = list.stream()
            .filter(error -> "archiveElements".equals(error.getRuleType()))
            .collect(Collectors.toList());

        List<TaskErrorResultDTO> otherRuleErrors = list.stream()
            .filter(error -> !"archiveElements".equals(error.getRuleType()))
            .collect(Collectors.toList());

        System.out.println(String.format("档案要素错误数量: %d", archiveElementsErrors.size()));
        System.out.println(String.format("其他规则错误数量: %d", otherRuleErrors.size()));
        System.out.println("🔍🔍🔍 === 档案要素错误分离完成 === 🔍🔍🔍");

        // 🔍 处理其他规则错误（原有逻辑）
        Map<String, List<TaskErrorResultDTO>> listMap = otherRuleErrors.stream().collect(Collectors.groupingBy(item -> item.getRuleName() + "&_&" + item.getRuleType() + "&_&" + item.getErrorType()));
        List<TaskStatisticsVo> finalTaskStatisticsVos = taskStatisticsVos;
        listMap.forEach((k, y) -> {
            String[] split = k.split("&_&");
            if (split.length > 2) {
                TaskStatisticsVo taskStatisticsVo = new TaskStatisticsVo();
                String ruleName = split[0];
                String ruleType = split[1];
                String errorType = split[2];
                taskStatisticsVo.setRuleName(ruleName);
                taskStatisticsVo.setRuleType(ruleType);
                taskStatisticsVo.setErrorType(Integer.parseInt(errorType));
                taskStatisticsVo.setErrorNum(y.size());
                taskStatisticsVo.setLists(y);
                BigDecimal a = new BigDecimal(StringHelper.toString(Double.valueOf(y.size())));
                BigDecimal b = new BigDecimal(String.valueOf(taskImageDataDTOs));
                taskStatisticsVo.setErrorRate( Double.valueOf(String.format("%.2f", a.divide(b, 2, BigDecimal.ROUND_HALF_UP).doubleValue() * 100 )));
                finalTaskStatisticsVos.add(taskStatisticsVo);
            }
        });

        // 🔍 处理档案要素检查统计 - 按规则名称聚合显示
        if (!CollectionUtils.isEmpty(archiveElementsErrors)) {
            System.out.println("🔍🔍🔍 === 档案要素统计开始 === 🔍🔍🔍");
            System.out.println(String.format("档案要素错误总数: %d", archiveElementsErrors.size()));

            // 🔍 详细打印每个错误记录
            archiveElementsErrors.forEach(error -> {
                System.out.println(String.format("错误记录: taskId=%s, dataKey=%s, fieldName=%s, ruleName=%s, errorRow=%d",
                    error.getTaskId(), error.getDataKey(), error.getFieldName(), error.getRuleName(), error.getErrorRow()));
            });

            // 按规则名称分组（规则模板中定义的规则名称）
            Map<String, List<TaskErrorResultDTO>> archiveElementsMap = archiveElementsErrors.stream()
                .collect(Collectors.groupingBy(TaskErrorResultDTO::getRuleName));

            System.out.println(String.format("按规则名称分组后的组数: %d", archiveElementsMap.size()));

            archiveElementsMap.forEach((ruleName, errors) -> {
                System.out.println(String.format("🔍 处理规则: %s, 错误数量: %d", ruleName, errors.size()));

                TaskStatisticsVo taskStatisticsVo = new TaskStatisticsVo();
                taskStatisticsVo.setRuleName(ruleName); // 显示规则模板中定义的规则名称
                taskStatisticsVo.setRuleType("archiveElements");
                taskStatisticsVo.setErrorType(ErrorResultType.RULE.getNum());
                taskStatisticsVo.setErrorNum(errors.size()); // 该规则下所有要素的错误总数
                taskStatisticsVo.setLists(errors);

                // 🔍 错误率计算：基于所有选中要素条目总数
                // 获取该规则涉及的要素数量（通过不同fieldName计算）
                long elementCount = errors.stream()
                    .map(TaskErrorResultDTO::getFieldName)
                    .distinct()
                    .count();

                // 🔍 详细分析要素分布
                Map<String, Long> fieldNameCounts = errors.stream()
                    .collect(Collectors.groupingBy(TaskErrorResultDTO::getFieldName, Collectors.counting()));
                System.out.println(String.format("🔍 要素错误分布: %s", fieldNameCounts));
                System.out.println(String.format("🔍 有错误的要素种类数: %d", elementCount));

                // 🔍 修复：使用所有参与审查的Excel行数，而不是只统计有错误的行数
                // taskImageDataDTOs 应该是所有参与审查的Excel行数
                long totalExcelRows = (long) taskImageDataDTOs;
                System.out.println(String.format("🔍 Excel总行数(taskImageDataDTOs): %d", totalExcelRows));

                double totalElementItems = elementCount * totalExcelRows;
                System.out.println(String.format("🔍 总条目数计算: %d(要素种类) × %d(Excel行数) = %.0f", elementCount, totalExcelRows, totalElementItems));

                BigDecimal errorCount = new BigDecimal(String.valueOf(errors.size()));
                BigDecimal totalItems = new BigDecimal(String.valueOf(totalElementItems));
                double errorRate = totalItems.compareTo(BigDecimal.ZERO) > 0 ?
                    errorCount.divide(totalItems, 4, BigDecimal.ROUND_HALF_UP).doubleValue() * 100 : 0;

                // 🔍 添加调试日志
                System.out.println(String.format("🔍 档案要素错误率计算 - 规则: %s", ruleName));
                System.out.println(String.format("  错误数量: %d", errors.size()));
                System.out.println(String.format("  要素数量: %d", elementCount));
                System.out.println(String.format("  Excel总行数: %d", totalExcelRows));
                System.out.println(String.format("  总条目数: %.0f", totalElementItems));
                System.out.println(String.format("  错误率: %.2f%%", errorRate));
                taskStatisticsVo.setErrorRate(Double.valueOf(String.format("%.2f", errorRate)));

                finalTaskStatisticsVos.add(taskStatisticsVo);
            });
        }

        taskStatisticsVos = taskStatisticsVos.stream().sorted(Comparator.comparing(TaskStatisticsVo::getRuleType).reversed()).collect(Collectors.toList());
        return taskStatisticsVos;
    }



}
