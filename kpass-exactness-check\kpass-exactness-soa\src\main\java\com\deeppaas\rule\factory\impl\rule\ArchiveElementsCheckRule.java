package com.deeppaas.rule.factory.impl.rule;

import com.deeppaas.common.helper.*;
import com.deeppaas.result.entity.TaskErrorResultDO;
import com.deeppaas.result.enums.ErrorResultType;

import com.deeppaas.rule.dto.PublicRuleDTO;
import com.deeppaas.rule.factory.base.RuleExecuteFactoryBase;
import com.deeppaas.rule.service.RuleExecuteFactoryService;
import com.deeppaas.task.config.dto.ProjectTaskConfigDTO;
import com.deeppaas.task.config.service.ProjectTaskConfigService;
import com.deeppaas.task.data.dto.ProjectTaskFormDataDTO;
import com.deeppaas.task.data.dto.ProjectTaskImageDataDTO;
import com.deeppaas.task.data.dto.ProjectTaskPdfDataDTO;
import com.deeppaas.task.data.service.ProjectTaskFormDataService;
import com.deeppaas.task.info.dto.ProjectTaskInfoDTO;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.*;

/**
 * 档案要素检查规则执行器
 * 支持图像分件处理和智能要素提取比对
 */
@Service("rule_execute_archiveElements")
public class ArchiveElementsCheckRule extends RuleExecuteFactoryBase implements RuleExecuteFactoryService {

    @Autowired
    private ProjectTaskConfigService projectTaskConfigService;
    
    @Autowired
    private ProjectTaskFormDataService projectTaskFormDataService;

    @Override
    public List<TaskErrorResultDO> ruleExecute(PublicRuleDTO ruleDTO, 
                                             List<ProjectTaskFormDataDTO> formDataDTOList, 
                                             List<ProjectTaskImageDataDTO> taskImageDataDTOList, 
                                             ProjectTaskInfoDTO projectTaskInfoDTO, 
                                             ProjectTaskPdfDataDTO taskPdfDataDTO, 
                                             ProjectTaskConfigDTO taskConfigDTO) {
        
        List<TaskErrorResultDO> errorResults = new ArrayList<>();
        
        try {
            String taskId = projectTaskInfoDTO.getId();

            System.out.println("🔍🔍🔍 === 开始档案要素检查 === 🔍🔍🔍");
            System.out.println("📋 任务ID: " + taskId);
            System.out.println("📋 配置ID: " + taskConfigDTO.getId());
            System.out.println("📋 Excel条目数量: " + (formDataDTOList != null ? formDataDTOList.size() : 0));
            System.out.println("📋 图像数据数量: " + (taskImageDataDTOList != null ? taskImageDataDTOList.size() : 0));

            // 🔍 空值检查
            if (formDataDTOList == null || formDataDTOList.isEmpty()) {
                System.err.println("❌ Excel数据为空，无法执行档案要素检查");
                return errorResults;
            }

            if (taskImageDataDTOList == null || taskImageDataDTOList.isEmpty()) {
                System.err.println("❌ 图像数据为空，无法执行档案要素检查");
                return errorResults;
            }

            // 🔍 1. 检查是否需要图像分件处理
            ImageGroupingResult groupingResult = checkImageGroupingNeeded(formDataDTOList, taskConfigDTO);
            System.out.println("🔍 分件检查结果: " + groupingResult.getGroupingType());

            List<ProjectTaskImageDataDTO> processedImageList = taskImageDataDTOList;

            if (groupingResult.needsGrouping()) {
                System.out.println("🚀 开始执行图像分件处理");
                // 🔍 2. 执行图像分件处理（内存中处理，不存数据库）
                processedImageList = processImageGroupingInMemory(formDataDTOList, taskImageDataDTOList, taskConfigDTO, groupingResult);
                System.out.println("📊 分件处理完成，处理后图像组数量: " + processedImageList.size());
            } else {
                System.out.println("📝 无需分件处理，使用原始图像数据");
            }

            // 🔍 3. 继续执行档案要素检查逻辑（使用处理后的图像数据）
            System.out.println("📋 开始档案要素提取和比对");
            System.out.println("📋 使用图像数据数量: " + (processedImageList != null ? processedImageList.size() : 0));

            // 🔍 解析规则模板配置获取字段映射
            Map<String, String> elementToExcelFieldMap = getElementToExcelFieldMapping(ruleDTO, taskConfigDTO);
            System.out.println("档案要素字段映射: " + elementToExcelFieldMap);

            // 🔍 准备Excel数据
            List<Map<String, Object>> excelData = new ArrayList<>();
            for (ProjectTaskFormDataDTO formData : formDataDTOList) {
                if (Objects.equals(formData.getTaskConfigId(), taskConfigDTO.getId())) {
                    Map<String, Object> rowData = new HashMap<>();
                    rowData.put("dataKey", formData.getDataKey());
                    rowData.put("partNumber", formData.getPartNumber()); // 🔍 添加件号信息
                    rowData.put("rowNum", formData.getRowNum());

                    System.out.println(String.format("📋 Excel数据行: dataKey=%s, partNumber=%s, rowNum=%d",
                        formData.getDataKey(), formData.getPartNumber(), formData.getRowNum()));

                    // 🔍 根据规则配置的字段映射获取Excel数据
                    String taskJson = formData.getTaskJson();
                    if (StringHelper.isNotEmpty(taskJson)) {
                        Map<String, Object> taskData = JsonHelper.json2map(taskJson);

                        // 使用动态字段映射
                        for (Map.Entry<String, String> mapping : elementToExcelFieldMap.entrySet()) {
                            String elementKey = mapping.getKey();     // 如: "title"
                            String excelFieldName = mapping.getValue(); // 如: "卷内题名"
                            Object excelValue = taskData.get(excelFieldName);
                            rowData.put(elementKey, excelValue);
                            System.out.println(String.format("字段映射 - %s -> %s: [%s]", elementKey, excelFieldName, excelValue));
                        }
                    }
                    excelData.add(rowData);
                }
            }

            // 🖼️ 准备图像文件路径
            List<String> imagePaths = new ArrayList<>();

            System.out.println("🖼️🖼️🖼️ === 收集图像路径信息 === 🖼️🖼️🖼️");
            System.out.println("processedImageList数量: " + processedImageList.size());

            for (int i = 0; i < processedImageList.size(); i++) {
                ProjectTaskImageDataDTO imageData = processedImageList.get(i);
                System.out.println(String.format("图像数据[%d]:", i));
                System.out.println("  - ID: " + imageData.getId());
                System.out.println("  - dataKey: " + imageData.getDataKey());
                System.out.println("  - partNumber: " + imageData.getPartNumber());
                System.out.println("  - imageFilePath: " + imageData.getImageFilePath());
                System.out.println("  - imageNames: " + imageData.getImageNames());
                System.out.println("  - imageCount: " + imageData.getImageCount());

                if (StringHelper.isNotEmpty(imageData.getImageFilePath())) {
                    imagePaths.add(imageData.getImageFilePath());
                    System.out.println("  ✅ 添加到图像路径列表: " + imageData.getImageFilePath());
                } else {
                    System.out.println("  ❌ 图像路径为空，跳过");
                }
            }

            System.out.println("📊 最终图像路径列表:");
            for (int i = 0; i < imagePaths.size(); i++) {
                System.out.println(String.format("  [%d] %s", i, imagePaths.get(i)));
            }
            System.out.println("🖼️🖼️🖼️ === 图像路径收集完成 === 🖼️🖼️🖼️");

            if (excelData.isEmpty() || imagePaths.isEmpty()) {
                System.err.println("❌ 数据检查失败:");
                System.err.println("  Excel数据为空: " + excelData.isEmpty());
                System.err.println("  图像路径为空: " + imagePaths.isEmpty());
                return errorResults; // 没有数据需要处理
            }

            // 🚀 调用Python AI服务 - 传递完整的分件数据
            ArchiveElementsCheckResult result = callPythonArchiveService(taskId, excelData, processedImageList);

            if (result != null && result.isSuccess()) {
                // 📝 处理检查结果，转换为TaskErrorResultDO列表
                errorResults = convertToTaskErrorResults(result, taskConfigDTO, projectTaskInfoDTO, ruleDTO);
            }

        } catch (Exception e) {
            // 记录错误但不中断整个流程
            System.err.println("档案要素检查失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return errorResults;
    }

    /**
     * 检查是否需要图像分件处理
     */
    private ImageGroupingResult checkImageGroupingNeeded(List<ProjectTaskFormDataDTO> formDataList,
                                                       ProjectTaskConfigDTO taskConfigDTO) {
        ImageGroupingResult result = new ImageGroupingResult();
        result.setGroupingType("NONE");
        result.setNeedsGrouping(false);

        try {
            // 🔍 获取字段库配置
            Map<String, String> ruleKeyFieldMap = taskConfigDTO.buildRuleKeyFieldMap();

            // 🔍 检查是否存在图像分件相关字段
            boolean hasStartEndPage = ruleKeyFieldMap.containsKey("起始页码") && ruleKeyFieldMap.containsKey("终止页码");
            boolean hasStartPageAndCount = ruleKeyFieldMap.containsKey("起始页码") && ruleKeyFieldMap.containsKey("页数");
            boolean hasPageRange = ruleKeyFieldMap.containsKey("页码范围");

            if (hasStartEndPage) {
                result.setGroupingType("START_END_PAGE");
                result.setNeedsGrouping(true);
                result.setStartPageField(ruleKeyFieldMap.get("起始页码"));
                result.setEndPageField(ruleKeyFieldMap.get("终止页码"));
            } else if (hasStartPageAndCount) {
                result.setGroupingType("START_PAGE_COUNT");
                result.setNeedsGrouping(true);
                result.setStartPageField(ruleKeyFieldMap.get("起始页码"));
                result.setPageCountField(ruleKeyFieldMap.get("页数"));
            } else if (hasPageRange) {
                result.setGroupingType("PAGE_RANGE");
                result.setNeedsGrouping(true);
                result.setPageRangeField(ruleKeyFieldMap.get("页码范围"));
            }

            System.out.println("🔍 图像分件检查结果:");
            System.out.println("  - 起始页码+终止页码: " + hasStartEndPage);
            System.out.println("  - 起始页码+页数: " + hasStartPageAndCount);
            System.out.println("  - 页码范围: " + hasPageRange);
            System.out.println("  - 最终分件类型: " + result.getGroupingType());
            System.out.println("  - 需要分件: " + result.needsGrouping());

        } catch (Exception e) {
            System.err.println("检查图像分件需求失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 内存中执行图像分件处理
     */
    private List<ProjectTaskImageDataDTO> processImageGroupingInMemory(List<ProjectTaskFormDataDTO> formDataList,
                                                                     List<ProjectTaskImageDataDTO> imageDataList,
                                                                     ProjectTaskConfigDTO taskConfigDTO,
                                                                     ImageGroupingResult groupingResult) {
        try {
            System.out.println("🚀 开始图像分件处理，分件类型: " + groupingResult.getGroupingType());

            switch (groupingResult.getGroupingType()) {
                case "START_END_PAGE":
                    return processStartEndPageGrouping(formDataList, imageDataList, groupingResult);
                case "START_PAGE_COUNT":
                    return processStartPageCountGrouping(formDataList, imageDataList, groupingResult);
                case "PAGE_RANGE":
                    return processPageRangeGrouping(formDataList, imageDataList, groupingResult);
                default:
                    System.out.println("🔍 单件处理模式，返回原始图像数据");
                    return imageDataList;
            }
        } catch (Exception e) {
            System.err.println("❌ 图像分件处理异常: " + e.getMessage());
            e.printStackTrace();
            System.out.println("🔄 回退到原始图像数据");
            return imageDataList;
        }
    }

    /**
     * 处理首页号+尾页号分件模式
     */
    private List<ProjectTaskImageDataDTO> processStartEndPageGrouping(
            List<ProjectTaskFormDataDTO> formDataList,
            List<ProjectTaskImageDataDTO> imageDataList,
            ImageGroupingResult groupingResult) {

        System.out.println("📄 处理首页号+尾页号分件模式");

        String startPageField = groupingResult.getStartPageField();
        String endPageField = groupingResult.getEndPageField();

        System.out.println("📋 首页号字段ID: " + startPageField);
        System.out.println("📋 尾页号字段ID: " + endPageField);

        if (startPageField == null || endPageField == null) {
            System.err.println("❌ 首页号或尾页号字段ID为空，无法进行分件处理");
            return imageDataList;
        }

        List<ProjectTaskImageDataDTO> result = new ArrayList<>();

        // 🔍 遍历Excel数据，为每个条目创建对应的图像组
        for (ProjectTaskFormDataDTO formData : formDataList) {
            try {
                // 🔍 解析Excel中的首页号和尾页号
                String startPageStr = getFieldValueFromFormData(formData, startPageField);
                String endPageStr = getFieldValueFromFormData(formData, endPageField);

                System.out.println(String.format("📋 Excel行%d: 首页号=%s, 尾页号=%s",
                    formData.getRowNum(), startPageStr, endPageStr));

                if (startPageStr != null && endPageStr != null) {
                    try {
                        int startPage = Integer.parseInt(startPageStr.trim());
                        int endPage = Integer.parseInt(endPageStr.trim());

                        if (startPage > 0 && endPage >= startPage) {
                            // 🔍 为这个页号范围创建图像数据组
                            ProjectTaskImageDataDTO imageGroup = createImageGroupForPageRange(
                                formData, imageDataList, startPage, endPage);

                            if (imageGroup != null) {
                                result.add(imageGroup);
                                System.out.println(String.format("✅ 创建图像组: 行%d, 页号%d-%d, 共%d页",
                                    formData.getRowNum(), startPage, endPage, (endPage - startPage + 1)));
                            }
                        } else {
                            System.err.println(String.format("❌ Excel行%d页号范围无效: %d-%d",
                                formData.getRowNum(), startPage, endPage));
                        }
                    } catch (NumberFormatException e) {
                        System.err.println(String.format("❌ Excel行%d页号解析失败: 首页号=%s, 尾页号=%s",
                            formData.getRowNum(), startPageStr, endPageStr));
                    }
                } else {
                    System.err.println(String.format("❌ Excel行%d页号字段为空: 首页号=%s, 尾页号=%s",
                        formData.getRowNum(), startPageStr, endPageStr));
                }
            } catch (Exception e) {
                System.err.println(String.format("❌ Excel行%d处理异常: %s", formData.getRowNum(), e.getMessage()));
            }
        }

        System.out.println(String.format("📊 首页号+尾页号分件完成: 输入%d个Excel条目，输出%d个图像组",
            formDataList.size(), result.size()));

        return result.isEmpty() ? imageDataList : result;
    }

    /**
     * 处理首页号+页数分件模式
     */
    private List<ProjectTaskImageDataDTO> processStartPageCountGrouping(
            List<ProjectTaskFormDataDTO> formDataList,
            List<ProjectTaskImageDataDTO> imageDataList,
            ImageGroupingResult groupingResult) {

        System.out.println("📄 处理首页号+页数分件模式");

        // 🔍 这里实现具体的分件逻辑
        // 暂时返回原始数据，后续可以根据实际需求完善
        System.out.println("⚠️ 首页号+页数分件逻辑待完善，暂时返回原始数据");
        return imageDataList;
    }

    /**
     * 处理起止页号分件模式
     */
    private List<ProjectTaskImageDataDTO> processPageRangeGrouping(
            List<ProjectTaskFormDataDTO> formDataList,
            List<ProjectTaskImageDataDTO> imageDataList,
            ImageGroupingResult groupingResult) {

        System.out.println("📄 处理起止页号分件模式");

        // 🔍 这里实现具体的分件逻辑
        // 暂时返回原始数据，后续可以根据实际需求完善
        System.out.println("⚠️ 起止页号分件逻辑待完善，暂时返回原始数据");
        return imageDataList;
    }

    /**
     * 根据Excel数据提取对应的图像文件名列表
     */
    private List<String> extractImageNamesForPart(Map<String, Object> taskData,
                                                Map<String, String> ruleKeyFieldMap,
                                                ImageGroupingResult groupingResult,
                                                List<ProjectTaskImageDataDTO> dataKeyImages) {
        List<String> imageNames = new ArrayList<>();

        try {
            // 🔍 获取该dataKey下所有可用的图像文件名
            Set<String> availableImages = new HashSet<>();
            for (ProjectTaskImageDataDTO imageData : dataKeyImages) {
                if (imageData.getImageNames() != null && !imageData.getImageNames().isEmpty()) {
                    for (String name : imageData.getImageNames()) {
                        availableImages.add(name.trim());
                    }
                }
            }

            // 🔍 根据分件类型提取图像
            switch (groupingResult.getGroupingType()) {
                case "START_END_PAGE":
                    imageNames = extractByStartEndPage(taskData, ruleKeyFieldMap, availableImages);
                    break;
                case "START_PAGE_COUNT":
                    imageNames = extractByStartPageCount(taskData, ruleKeyFieldMap, availableImages);
                    break;
                case "PAGE_RANGE":
                    imageNames = extractByPageRange(taskData, ruleKeyFieldMap, availableImages);
                    break;
                default:
                    // 如果没有分件信息，返回所有图像
                    imageNames.addAll(availableImages);
                    break;
            }

        } catch (Exception e) {
            System.err.println("提取图像文件名失败: " + e.getMessage());
            e.printStackTrace();
        }

        return imageNames;
    }

    /**
     * 根据起始页码和终止页码提取图像
     */
    private List<String> extractByStartEndPage(Map<String, Object> taskData,
                                             Map<String, String> ruleKeyFieldMap,
                                             Set<String> availableImages) {
        List<String> result = new ArrayList<>();

        try {
            String startPageField = ruleKeyFieldMap.get("起始页码");
            String endPageField = ruleKeyFieldMap.get("终止页码");

            if (startPageField != null && endPageField != null) {
                Object startPageObj = taskData.get(startPageField);
                Object endPageObj = taskData.get(endPageField);

                if (startPageObj != null && endPageObj != null) {
                    int startPage = Integer.parseInt(startPageObj.toString());
                    int endPage = Integer.parseInt(endPageObj.toString());

                    for (int page = startPage; page <= endPage; page++) {
                        String imageName = String.format("page_%d.jpg", page);
                        if (availableImages.contains(imageName)) {
                            result.add(imageName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("根据起始终止页码提取图像失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 根据起始页码和页数提取图像
     */
    private List<String> extractByStartPageCount(Map<String, Object> taskData,
                                               Map<String, String> ruleKeyFieldMap,
                                               Set<String> availableImages) {
        List<String> result = new ArrayList<>();

        try {
            String startPageField = ruleKeyFieldMap.get("起始页码");
            String pageCountField = ruleKeyFieldMap.get("页数");

            if (startPageField != null && pageCountField != null) {
                Object startPageObj = taskData.get(startPageField);
                Object pageCountObj = taskData.get(pageCountField);

                if (startPageObj != null && pageCountObj != null) {
                    int startPage = Integer.parseInt(startPageObj.toString());
                    int pageCount = Integer.parseInt(pageCountObj.toString());
                    int endPage = startPage + pageCount - 1;

                    for (int page = startPage; page <= endPage; page++) {
                        String imageName = String.format("page_%d.jpg", page);
                        if (availableImages.contains(imageName)) {
                            result.add(imageName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("根据起始页码和页数提取图像失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 根据页码范围提取图像
     */
    private List<String> extractByPageRange(Map<String, Object> taskData,
                                          Map<String, String> ruleKeyFieldMap,
                                          Set<String> availableImages) {
        List<String> result = new ArrayList<>();

        try {
            String pageRangeField = ruleKeyFieldMap.get("页码范围");

            if (pageRangeField != null) {
                Object pageRangeObj = taskData.get(pageRangeField);

                if (pageRangeObj != null) {
                    String pageRange = pageRangeObj.toString();
                    // 解析页码范围，如 "1-5" 或 "1,3,5-7"
                    String[] ranges = pageRange.split(",");

                    for (String range : ranges) {
                        range = range.trim();
                        if (range.contains("-")) {
                            String[] parts = range.split("-");
                            if (parts.length == 2) {
                                int start = Integer.parseInt(parts[0].trim());
                                int end = Integer.parseInt(parts[1].trim());
                                for (int page = start; page <= end; page++) {
                                    String imageName = String.format("page_%d.jpg", page);
                                    if (availableImages.contains(imageName)) {
                                        result.add(imageName);
                                    }
                                }
                            }
                        } else {
                            int page = Integer.parseInt(range);
                            String imageName = String.format("page_%d.jpg", page);
                            if (availableImages.contains(imageName)) {
                                result.add(imageName);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("根据页码范围提取图像失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取档案要素到Excel字段的映射
     */
    private Map<String, String> getElementToExcelFieldMapping(PublicRuleDTO ruleDTO, ProjectTaskConfigDTO taskConfigDTO) {
        Map<String, String> elementToExcelFieldMap = new HashMap<>();

        try {
            // 🔍 获取规则模板配置的字段映射
            Map<String, String> ruleKeyFieldMap = taskConfigDTO.buildRuleKeyFieldMap();

            // 🔍 档案要素映射配置
            elementToExcelFieldMap.put("title", ruleKeyFieldMap.getOrDefault("题名", "卷内题名"));
            elementToExcelFieldMap.put("responsible_party", ruleKeyFieldMap.getOrDefault("责任者", "责任者"));
            elementToExcelFieldMap.put("document_number", ruleKeyFieldMap.getOrDefault("文号", "文号"));
            elementToExcelFieldMap.put("issue_date", ruleKeyFieldMap.getOrDefault("发文日期", "发文日期"));

            System.out.println("🔍 字段映射配置:");
            for (Map.Entry<String, String> entry : elementToExcelFieldMap.entrySet()) {
                System.out.println(String.format("  %s -> %s", entry.getKey(), entry.getValue()));
            }

        } catch (Exception e) {
            System.err.println("获取字段映射失败: " + e.getMessage());
            e.printStackTrace();

            // 🔍 使用默认映射
            elementToExcelFieldMap.put("title", "卷内题名");
            elementToExcelFieldMap.put("responsible_party", "责任者");
            elementToExcelFieldMap.put("document_number", "文号");
            elementToExcelFieldMap.put("issue_date", "发文日期");
        }

        return elementToExcelFieldMap;
    }

    /**
     * 调用Python档案要素检查服务
     */
    private ArchiveElementsCheckResult callPythonArchiveService(String taskId,
                                                              List<Map<String, Object>> excelData,
                                                              List<ProjectTaskImageDataDTO> imageDataList) {
        try {
            // 🌐 智能提取AI服务地址
            String serviceUrl = "http://localhost:8080/extract/archive/batch_compare";

            // 🔍 准备分件数据 - 转换为Python端需要的格式
            List<Map<String, Object>> imageDataForPython = new ArrayList<>();
            for (ProjectTaskImageDataDTO imageData : imageDataList) {
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("path", imageData.getImageFilePath());
                imageInfo.put("filename", imageData.getImageFilePath() != null ?
                    new File(imageData.getImageFilePath()).getName() : "unknown");
                imageInfo.put("dataKey", imageData.getDataKey());
                imageInfo.put("partNumber", imageData.getPartNumber());
                imageInfo.put("imageNames", imageData.getImageNames());
                imageInfo.put("imageCount", imageData.getImageCount());
                imageDataForPython.add(imageInfo);

                System.out.println(String.format("🐍 传递给Python的图像数据: dataKey=%s, partNumber=%s, path=%s, imageNames=%s",
                    imageData.getDataKey(), imageData.getPartNumber(), imageData.getImageFilePath(), imageData.getImageNames()));
            }

            // 📝 准备请求参数
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            params.add("task_id", taskId);
            params.add("excel_data", JsonHelper.toJson(excelData));
            params.add("image_data", JsonHelper.toJson(imageDataForPython)); // 🔍 传递完整的分件数据
            params.add("elements", JsonHelper.toJson(Arrays.asList("title", "responsible_party", "document_number", "issue_date")));
            params.add("confidence_threshold", 0.5);
            params.add("similarity_threshold", 0.8);
            params.add("enable_stamp_processing", true);  // 启用印章处理
            params.add("stamp_confidence_threshold", 0.5); // 适合OpenCV的置信度阈值
            params.add("enable_preprocessing", true);  // 启用图像预处理

            System.out.println("🐍🐍🐍 === 调用Python服务参数 === 🐍🐍🐍");
            System.out.println("task_id: " + taskId);
            System.out.println("excel_data条目数: " + excelData.size());
            System.out.println("image_data条目数: " + imageDataForPython.size());
            System.out.println("🐍🐍🐍 === 参数准备完成 === 🐍🐍🐍");

            // 🚀 发送HTTP请求
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                // 📊 解析响应结果
                String responseBody = response.getBody();

                // 🔍 添加调试日志 - 查看Python服务响应
                System.out.println("🐍🐍🐍 === Python档案要素检查服务完整响应 === 🐍🐍🐍");
                System.out.println("响应状态码: " + response.getStatusCode());
                System.out.println("响应体长度: " + (responseBody != null ? responseBody.length() : 0));
                System.out.println("响应内容: " + responseBody);

                Map<String, Object> resultMap = JsonHelper.json2map(responseBody);

                ArchiveElementsCheckResult result = new ArchiveElementsCheckResult();
                result.setSuccess((Boolean) resultMap.get("success"));
                result.setTaskId((String) resultMap.get("task_id"));
                result.setComparisonResult(resultMap.get("comparison_result"));
                result.setError((String) resultMap.get("error"));

                System.out.println("✅✅✅ === Java端解析完成 === ✅✅✅");
                System.out.println("最终结果success: " + result.isSuccess());
                System.out.println("最终结果task_id: " + result.getTaskId());
                System.out.println("最终结果error: " + result.getError());

                return result;
            } else {
                System.err.println("Python服务调用失败，状态码: " + response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            System.err.println("调用Python档案要素检查服务失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将档案要素检查结果转换为TaskErrorResultDO列表
     */
    private List<TaskErrorResultDO> convertToTaskErrorResults(ArchiveElementsCheckResult result,
                                                            ProjectTaskConfigDTO taskConfigDTO,
                                                            ProjectTaskInfoDTO taskInfoDTO,
                                                            PublicRuleDTO ruleDTO) {
        List<TaskErrorResultDO> errorResults = new ArrayList<>();

        try {
            if (result == null || !result.isSuccess() || result.getComparisonResult() == null) {
                return errorResults;
            }

            String taskId = taskInfoDTO.getId();

            // 🔍 预先获取formData映射，避免重复查询
            List<ProjectTaskFormDataDTO> formDataList = projectTaskFormDataService.findByTaskId(taskId);
            Map<String, Integer> dataKeyToRowNumMap = new HashMap<>();
            for (ProjectTaskFormDataDTO formData : formDataList) {
                if (formData.getDataKey() != null && formData.getRowNum() != null) {
                    dataKeyToRowNumMap.put(formData.getDataKey(), formData.getRowNum());
                }
            }

            // 📊 解析比对结果
            Object comparisonResult = result.getComparisonResult();
            if (comparisonResult instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) comparisonResult;

                // 处理每个Excel行的比对结果
                for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                    String resultKey = entry.getKey(); // 现在是 dataKey_rowNum 格式
                    Object rowResult = entry.getValue();

                    if (rowResult instanceof Map) {
                        Map<String, Object> rowData = (Map<String, Object>) rowResult;

                        // 🔍 解析新的键格式：dataKey_rowNum
                        String dataKey;
                        Integer rowNum;

                        if (resultKey.contains("_")) {
                            // 新格式：dataKey_rowNum
                            String[] parts = resultKey.split("_");
                            if (parts.length >= 2) {
                                dataKey = parts[0]; // 可能包含多个部分，如 123-023-00001
                                try {
                                    rowNum = Integer.parseInt(parts[parts.length - 1]); // 最后一部分是rowNum
                                    // 重新构建dataKey（除了最后的rowNum部分）
                                    if (parts.length > 2) {
                                        StringBuilder sb = new StringBuilder();
                                        for (int i = 0; i < parts.length - 1; i++) {
                                            if (i > 0) sb.append("_");
                                            sb.append(parts[i]);
                                        }
                                        dataKey = sb.toString();
                                    }
                                } catch (NumberFormatException e) {
                                    // 如果最后一部分不是数字，则整个作为dataKey
                                    dataKey = resultKey;
                                    rowNum = dataKeyToRowNumMap.get(dataKey);
                                }
                            } else {
                                dataKey = resultKey;
                                rowNum = dataKeyToRowNumMap.get(dataKey);
                            }
                        } else {
                            // 旧格式：纯dataKey
                            dataKey = resultKey;
                            rowNum = dataKeyToRowNumMap.get(dataKey);
                        }

                        System.out.println(String.format("🔍 解析结果键: %s -> dataKey: %s, rowNum: %d",
                            resultKey, dataKey, rowNum != null ? rowNum : -1));

                        // 检查每个档案要素的错误
                        processArchiveElementErrors(errorResults, taskId, taskConfigDTO.getId(),
                                                   dataKey, rowData, ruleDTO, dataKeyToRowNumMap, rowNum);
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("转换档案要素检查错误结果失败: " + e.getMessage());
            e.printStackTrace();
        }

        return errorResults;
    }

    /**
     * 处理单行档案要素错误
     */
    private void processArchiveElementErrors(List<TaskErrorResultDO> errorResults,
                                           String taskId, String taskConfigId,
                                           String dataKey, Map<String, Object> rowData,
                                           PublicRuleDTO ruleDTO,
                                           Map<String, Integer> dataKeyToRowNumMap,
                                           Integer providedRowNum) {

        // 🔍 获取档案要素到Excel字段的映射
        Map<String, String> elementToExcelFieldMap = new HashMap<>();
        ProjectTaskConfigDTO taskConfigDTO = null;

        try {
            // 🔍 获取任务配置
            taskConfigDTO = projectTaskConfigService.findById(taskConfigId);
            if (taskConfigDTO != null) {
                elementToExcelFieldMap = getElementToExcelFieldMapping(ruleDTO, taskConfigDTO);
            }
        } catch (Exception e) {
            System.err.println("获取任务配置失败: " + e.getMessage());
            // 使用默认映射
            elementToExcelFieldMap.put("title", "卷内题名");
            elementToExcelFieldMap.put("responsible_party", "责任者");
            elementToExcelFieldMap.put("document_number", "文号");
            elementToExcelFieldMap.put("issue_date", "发文日期");
        }

        // 🔍 处理每个档案要素
        String[] elements = {"title", "responsible_party", "document_number", "issue_date"};
        for (String element : elements) {
            Object elementResult = rowData.get(element);
            if (elementResult instanceof Map) {
                Map<String, Object> elementData = (Map<String, Object>) elementResult;
                Boolean hasError = (Boolean) elementData.get("has_error");

                if (hasError != null && hasError) {
                    // 🔍 创建错误记录
                    TaskErrorResultDO errorResult = new TaskErrorResultDO();
                    errorResult.setTaskId(taskId);
                    errorResult.setTaskConfigId(taskConfigId);
                    errorResult.setDataKey(dataKey);
                    errorResult.setErrorRow(providedRowNum != null ? providedRowNum : dataKeyToRowNumMap.get(dataKey));
                    errorResult.setRuleType(ruleDTO.getRuleName());
                    errorResult.setRuleName(ruleDTO.getRuleAliasName());
                    errorResult.setErrorType(ErrorResultType.RULE.getNum());
                    errorResult.setAiCheck(BoolHelper.INT_FALSE);

                    // 🔍 设置字段名（使用Excel列名）
                    String excelFieldName = elementToExcelFieldMap.get(element);
                    errorResult.setFieldName(excelFieldName != null ? excelFieldName : element);

                    // 🔍 设置错误值信息
                    String excelValue = (String) elementData.get("excel_value");
                    String extractedValue = (String) elementData.get("extracted_value");
                    Double similarity = (Double) elementData.get("similarity");

                    // 🔍 构建错误信息
                    String errorInfo = String.format("要素正确性:\n%s不匹配：\n提取值[%s]\n相似度[%.2f]",
                        excelFieldName != null ? excelFieldName : element,
                        extractedValue != null ? extractedValue : "未提取到",
                        similarity != null ? similarity : 0.0);

                    errorResult.setErrorFileValue(errorInfo);

                    errorResults.add(errorResult);

                    System.out.println(String.format("🔍 创建档案要素错误: dataKey=%s, 字段=%s, Excel值=[%s], 提取值=[%s], 相似度=%.2f",
                        dataKey, excelFieldName, excelValue, extractedValue, similarity != null ? similarity : 0.0));
                }
            }
        }
    }

    /**
     * 档案要素检查结果数据类
     */
    @Data
    public static class ArchiveElementsCheckResult {
        private boolean success;
        private String taskId;
        private Object comparisonResult;
        private String error;
    }

    /**
     * 图像分件结果数据类
     */
    @Data
    public static class ImageGroupingResult {
        private String groupingType;
        private boolean needsGrouping;
        private String startPageField;
        private String endPageField;
        private String pageCountField;
        private String pageRangeField;

        public boolean needsGrouping() {
            return needsGrouping;
        }
    }

    /**
     * 从Excel表单数据中获取指定字段的值
     */
    private String getFieldValueFromFormData(ProjectTaskFormDataDTO formData, String fieldId) {
        try {
            if (formData.getTaskJson() != null) {
                // 🔍 解析JSON数据
                Map<String, Object> taskJson = JsonHelper.fromJson(formData.getTaskJson(), Map.class);
                if (taskJson != null && taskJson.containsKey(fieldId)) {
                    Object value = taskJson.get(fieldId);
                    return value != null ? value.toString() : null;
                }
            }
        } catch (Exception e) {
            System.err.println(String.format("❌ 解析Excel行%d字段%s失败: %s",
                formData.getRowNum(), fieldId, e.getMessage()));
        }
        return null;
    }

    /**
     * 为指定页号范围创建图像数据组
     */
    private ProjectTaskImageDataDTO createImageGroupForPageRange(
            ProjectTaskFormDataDTO formData,
            List<ProjectTaskImageDataDTO> imageDataList,
            int startPage, int endPage) {

        try {
            // 🔍 根据dataKey查找对应的原始图像数据
            ProjectTaskImageDataDTO matchingImage = null;
            for (ProjectTaskImageDataDTO imageData : imageDataList) {
                if (formData.getDataKey().equals(imageData.getDataKey())) {
                    matchingImage = imageData;
                    break;
                }
            }

            // 🔍 如果没找到匹配的图像，使用第一个作为模板
            ProjectTaskImageDataDTO template = matchingImage != null ? matchingImage : imageDataList.get(0);

            if (template != null) {
                ProjectTaskImageDataDTO imageGroup = new ProjectTaskImageDataDTO();
                imageGroup.setTaskId(template.getTaskId());
                imageGroup.setTaskConfigId(template.getTaskConfigId());
                imageGroup.setDataKey(formData.getDataKey());
                imageGroup.setPartNumber(formData.getPartNumber());

                // 🔍 使用匹配的图像路径，如果没有匹配则构建路径
                String imagePath;
                if (matchingImage != null) {
                    imagePath = matchingImage.getImageFilePath();
                    System.out.println(String.format("✅ 找到匹配图像: dataKey=%s -> path=%s",
                        formData.getDataKey(), imagePath));
                } else {
                    // 🔍 根据dataKey构建图像路径
                    String basePath = template.getImageFilePath();
                    // 提取基础路径并替换最后的dataKey部分
                    String[] pathParts = formData.getDataKey().split("-");
                    if (pathParts.length >= 3) {
                        String newPath = basePath.replaceAll("\\\\[^\\\\]+\\\\[^\\\\]+\\\\[^\\\\]+$",
                            "\\\\" + pathParts[0] + "\\\\" + pathParts[1] + "\\\\" + pathParts[2]);
                        imagePath = newPath;
                        System.out.println(String.format("🔧 构建图像路径: dataKey=%s -> path=%s",
                            formData.getDataKey(), imagePath));
                    } else {
                        imagePath = template.getImageFilePath();
                        System.out.println(String.format("⚠️ 无法构建路径，使用模板: dataKey=%s -> path=%s",
                            formData.getDataKey(), imagePath));
                    }
                }

                imageGroup.setImageFilePath(imagePath);
                imageGroup.setImageCount(endPage - startPage + 1);
                imageGroup.setCreateTime(template.getCreateTime());

                // 🔍 构建页号范围的图像名称列表
                List<String> imageNames = new ArrayList<>();
                for (int page = startPage; page <= endPage; page++) {
                    imageNames.add(String.format("page_%d.jpg", page));
                }
                imageGroup.setImageNames(imageNames);

                System.out.println(String.format("📋 创建图像组: dataKey=%s, 页号%d-%d, 图像数量=%d, 路径=%s",
                    formData.getDataKey(), startPage, endPage, imageGroup.getImageCount(), imagePath));

                return imageGroup;
            }
        } catch (Exception e) {
            System.err.println(String.format("❌ 创建图像组失败: dataKey=%s, 页号%d-%d, 错误=%s",
                formData.getDataKey(), startPage, endPage, e.getMessage()));
            e.printStackTrace();
        }

        return null;
    }
}
