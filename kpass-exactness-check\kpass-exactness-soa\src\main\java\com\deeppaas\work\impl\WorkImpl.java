package com.deeppaas.work.impl;

import com.deeppaas.ExactnessConfig;
import com.deeppaas.FileEnum;
import com.deeppaas.SelectDTO;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.*;
import com.deeppaas.library.service.PublicLibraryService;
import com.deeppaas.result.entity.TaskErrorResultDO;
import com.deeppaas.result.service.TaskErrorResultService;
import com.deeppaas.rule.dto.PublicRuleDTO;
import com.deeppaas.rule.factory.RuleExecuteFactory;
import com.deeppaas.rule.service.PublicRuleService;
import com.deeppaas.rule.service.RuleExecuteFactoryService;
import com.deeppaas.task.config.dto.ProjectTaskConfigDTO;
import com.deeppaas.task.config.service.ProjectTaskConfigService;
import com.deeppaas.task.config.service.ProjectTaskUploadService;
import com.deeppaas.task.data.dto.ProjectTaskFormDataDTO;
import com.deeppaas.task.data.dto.ProjectTaskImageDataDTO;
import com.deeppaas.task.data.entity.ProjectTaskFormDataDO;
import com.deeppaas.task.data.service.ProjectTaskFormDataService;
import com.deeppaas.task.data.service.ProjectTaskImageDataService;
import com.deeppaas.task.info.dto.ProjectTaskInfoDTO;
import com.deeppaas.task.info.service.ProjectTaskInfoService;
import com.deeppaas.userCheck.task.service.UserCheckService;
import com.deeppaas.work.WorkService;
import com.deeppaas.work.enums.TaskStatusEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service
public class WorkImpl implements WorkService {



    @Autowired
    private ExecutorService executorService;
    @Autowired
    private ProjectTaskConfigService projectTaskConfigService;

    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;
    @Autowired
    private ProjectTaskFormDataService projectTaskFormDataService;
    @Autowired
    private ProjectTaskImageDataService projectTaskImageDataService;
    @Autowired
    private PublicRuleService publicRuleService;
    @Autowired
    private PublicLibraryService publicLibraryService;
    @Autowired
    private TaskErrorResultService taskErrorResultService;
    @Autowired
    private RuleExecuteFactory ruleExecuteFactory;

    @Autowired
    private ExactnessConfig exactnessConfig;
    @Autowired
    private UserCheckService userCheckService;
    @Autowired
    private ProjectTaskUploadService projectTaskUploadService;

    public void start(String taskId) {
        projectTaskFormDataService.delTaskId(taskId);
        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.ING.getNum());
        taskInfoDTO.setStartTime(LocalDateTime.now());
        taskInfoDTO.setTaskProgress(new BigDecimal("0"));
        taskInfoDTO.setExcelProgress(new BigDecimal("0"));
        taskInfoDTO.setUserCheckProgress(0f);
        taskInfoDTO.setImageProgress(new BigDecimal("0"));
        projectTaskInfoService.save(taskInfoDTO);
        //开始前删除所有目录数据跟图像数据
        taskErrorResultService.delTaskId(taskId);
        userCheckService.delByTaskId(taskId);

        projectTaskImageDataService.updateSuccess(taskId, BoolHelper.INT_FALSE);
        executor(taskInfoDTO);
        //设置抽查
        userCheckService.batchAllocation(taskId);
    }


    public void executor(ProjectTaskInfoDTO taskInfoDTO) {

        String taskId = taskInfoDTO.getId();
        List<ProjectTaskImageDataDTO> imageDataDOS = projectTaskImageDataService.findByTaskId(taskId);
        List<ProjectTaskConfigDTO> taskConfigDTOS = projectTaskConfigService.findByTaskId(taskId);
        for (int j = 0; j < taskConfigDTOS.size(); j++) {
            ProjectTaskConfigDTO taskConfigDTO = taskConfigDTOS.get(j);
            buildExcel(taskId, taskConfigDTO, imageDataDOS);
        }
        if (CollectionUtils.isEmpty(imageDataDOS))
            imageDataDOS = projectTaskImageDataService.findByTaskId(taskId);

        List<ProjectTaskFormDataDTO> lists = projectTaskFormDataService.findByTaskId(taskId);
        if (CollectionUtils.isEmpty(imageDataDOS))
            imageDataDOS = projectTaskImageDataService.findByTaskId(taskId);

        List<ProjectTaskConfigDTO> imageConfig = taskConfigDTOS.stream().filter(item -> Objects.equals(item.getRuleConfigType(), FileEnum.C.getNum())).collect(Collectors.toList());
        List<ProjectTaskConfigDTO> excelConfig = taskConfigDTOS.stream().filter(item -> Objects.equals(item.getRuleConfigType(), FileEnum.A.getNum()) || Objects.equals(item.getRuleConfigType(), FileEnum.B.getNum())).collect(Collectors.toList());
        taskConfigDTOS=taskConfigDTOS.stream().filter(item -> !Objects.equals(item.getRuleConfigType(), FileEnum.D.getNum())&&!Objects.equals(item.getRuleConfigType(), FileEnum.E.getNum())).collect(Collectors.toList());
        for (int j = 0; j < taskConfigDTOS.size(); j++) {
            ProjectTaskConfigDTO taskConfigDTO = taskConfigDTOS.get(j);
            if (!Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.D.getNum())) {
                //获取任务对应规则模板
                String templateId = taskConfigDTO.getRuleTemplateId();

                //获取分批执行规则 图像规则/条目规则/联合规则
                List<PublicRuleDTO> ruleDTOS = publicRuleService.findByLibraryId(templateId);
                if(CollectionUtils.isEmpty(ruleDTOS)){
                    throw RunException.optReject("未找到任何规则，请检查是否配置规则，如配置的图像规则，请点击保存！");
                }

                // 🚀 统一执行所有规则（包括档案要素检查）
                for (int i = 0; i < ruleDTOS.size(); i++) {
                    PublicRuleDTO ruleDTO = ruleDTOS.get(i);
                    executorService.execute(new Task(ruleDTO, lists, imageDataDOS, taskConfigDTO, taskInfoDTO));
                }
                try {
                    Thread.sleep(3*1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                BigDecimal b = new BigDecimal(String.valueOf(excelConfig.size()));
                //更新进度条excel
                if (Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.A.getNum()) || Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.B.getNum())) {
                    BigDecimal a1 = new BigDecimal("0");
                    if (CollectionUtils.isEmpty(imageConfig))
                        a1 = new BigDecimal("100");
                    projectTaskInfoService.updateTaskProcess(taskId, a1.divide(b, 2, BigDecimal.ROUND_HALF_UP), FileEnum.A.getNum());

                }
            }
        }
    }


    @Override
    public void stop(String taskId) {

        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);

        projectTaskFormDataService.delTaskId(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.READY.getNum());
        taskInfoDTO.setTaskProgress(new BigDecimal("0"));
        projectTaskInfoService.save(taskInfoDTO);
        //开始前删除所有目录数据跟图像数据
        taskErrorResultService.delTaskId(taskId);
        //更新图像数据状态
        projectTaskImageDataService.updateSuccess(taskId, BoolHelper.INT_FALSE);
    }

    @Override
    public void suspend(String taskId) {
        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);
        //删除excel数据
        projectTaskFormDataService.delTaskId(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.STOP.getNum());
        projectTaskInfoService.save(taskInfoDTO);
        //删除所有除了图像以外的数据
        taskErrorResultService.delTaskIdAndNotAiCheck(taskId);
    }

    @Override
    public void continueTask(String taskId) {
        projectTaskFormDataService.delTaskId(taskId);
        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.ING.getNum());
        taskInfoDTO.setStartTime(LocalDateTime.now());
        taskInfoDTO.setExcelProgress(new BigDecimal("0"));
        projectTaskInfoService.save(taskInfoDTO);
        //删除所有除了图像以外的数据
        taskErrorResultService.delTaskIdAndNotAiCheck(taskId);
        executor(taskInfoDTO);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Task implements Runnable {
        private PublicRuleDTO ruleDTO;

        private List<ProjectTaskFormDataDTO> lists;
        private List<ProjectTaskImageDataDTO> imageDataDOS;
        private ProjectTaskConfigDTO taskConfigDTO;
        private ProjectTaskInfoDTO projectTaskInfoDTO;

        @Override
        public void run() {
            try {

                RuleExecuteFactoryService executeFactoryService = ruleExecuteFactory.get(ruleDTO.getRuleCode());
                List<ProjectTaskFormDataDTO> configs = null;
                if (Objects.equals(ruleDTO.getRuleType(), FileEnum.C.getNum())) {
                    configs = lists;

                } else {
                    configs = lists.stream().filter(item -> Objects.equals(item.getTaskConfigId(), taskConfigDTO.getId())).collect(Collectors.toList());
                }

                List<TaskErrorResultDO> list = executeFactoryService.ruleExecute(ruleDTO, configs, imageDataDOS, projectTaskInfoDTO, null,taskConfigDTO);
                taskErrorResultService.saves(list);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }


    private void buildExcel(String taskId, ProjectTaskConfigDTO taskConfigDTO, List<ProjectTaskImageDataDTO> imageDataDOS) {
        Integer configType = taskConfigDTO.getRuleConfigType();

        System.out.println("🔧🔧🔧 === buildExcel方法被调用 === 🔧🔧🔧");
        System.out.println("📋 任务ID: " + taskId);
        System.out.println("📋 配置ID: " + taskConfigDTO.getId());
        System.out.println("📋 配置类型: " + configType + " (FileEnum.C=" + FileEnum.C.getNum() + ")");
        System.out.println("📋 图像数据是否为空: " + CollectionUtils.isEmpty(imageDataDOS));
        System.out.println("📋 图像数据数量: " + (imageDataDOS != null ? imageDataDOS.size() : 0));

        if (Objects.equals(configType, FileEnum.C.getNum()) && CollectionUtils.isEmpty(imageDataDOS)) {
            System.out.println("✅ 满足图像构建条件，调用buildImage方法");
            projectTaskUploadService.buildImage(taskId, taskConfigDTO);
        } else {
            System.out.println("❌ 不满足图像构建条件:");
            System.out.println("   - 配置类型匹配: " + Objects.equals(configType, FileEnum.C.getNum()));
            System.out.println("   - 图像数据为空: " + CollectionUtils.isEmpty(imageDataDOS));
        }

        if (!Objects.equals(configType, FileEnum.A.getNum()) && !Objects.equals(configType, FileEnum.B.getNum())) {
            return;
        }

        ExcelReader excelReader = null;
        try {
            String excelPath = taskConfigDTO.getFilePath();
            Map<String, String> ruleMapping = taskConfigDTO.buildRuleMappingMap();
            List<ProjectTaskFormDataDO> lists = Lists.newArrayList();
            excelReader = ExcelReader.of(new File(excelPath));
            //1.将图像/excel数据库数据落地
            String sheetName = taskConfigDTO.getSheetName();
            if (StringHelper.isEmpty(sheetName)) {
                sheetName = excelReader.getSheetName().get(0);
            }
            List<String> headersName = excelReader.getHeaders(sheetName);

            List<String[]> datas = excelReader.getData(taskConfigDTO.getSheetName(), headersName.size());
            //转化dataKey
            String urlRule = taskConfigDTO.getUrlRule();
            List<SelectDTO> selectDTOS = JsonHelper.readToLists(urlRule, SelectDTO.class);

            //保存excel数据
            for (int i = 0; i < datas.size(); i++) {
                String[] columnsData = datas.get(i);
                Map<String, Object> taskJson = new HashMap<>();
                ProjectTaskFormDataDO formDataDO = new ProjectTaskFormDataDO();
                for (int j = 0; j < headersName.size(); j++) {
                    String col = columnsData[j];
                    String titleName = headersName.get(j).replaceAll(" ", "");
                    String key = ruleMapping.get(titleName);
                    key = StringHelper.isEmpty(key) ? titleName : key;
                    taskJson.put(key, col);
                }
                StringBuffer dataKey = new StringBuffer();
                for (int j = 0; j < selectDTOS.size(); j++) {
                    SelectDTO selectDTO = selectDTOS.get(j);
                    String label = selectDTO.getLabel();
                    Object value = taskJson.get(label);
                    if (!NumeralrHelper.isOdd(j)) {
                        dataKey.append(label);
                    }
                    if (value != null) {
                        dataKey.append(StringHelper.toString(value));
                    }
                }

                if (BoolHelper.intToBool(taskConfigDTO.getPieceHaveIs()) && dataKey.indexOf("-") > 0) {
                    formDataDO.setDataKey(dataKey.substring(0, dataKey.lastIndexOf("-")));
                    formDataDO.setPartNumber(dataKey.substring(dataKey.lastIndexOf("-") + 1));
                } else {
                    formDataDO.setDataKey(dataKey.toString());
                }


                formDataDO.setRowNum(i + 2);
                formDataDO.setTaskConfigId(taskConfigDTO.getId());
                formDataDO.setTaskId(taskId);

                formDataDO.setTaskJson(JsonHelper.toJson(taskJson));
                formDataDO.setCreateTime(LocalDateTime.now());
                formDataDO.setMateIs(BoolHelper.INT_FALSE);
                ProjectTaskImageDataDTO dataDTO = imageDataDOS.stream().filter(item -> Objects.equals(item.getDataKey(), formDataDO.getDataKey())).findFirst().orElse(null);
                if (dataDTO != null) {
                    formDataDO.setMateIs(BoolHelper.INT_TRUE);
                }

                lists.add(formDataDO);
            }
            projectTaskFormDataService.saves(lists);
            excelReader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelReader != null) excelReader.close();
        }
    }





















}
