package com.deeppaas.work.impl;

import com.deeppaas.ExactnessConfig;
import com.deeppaas.FileEnum;
import com.deeppaas.KeyValueDTO;
import com.deeppaas.SelectDTO;
import com.deeppaas.common.exception.RunException;
import com.deeppaas.common.helper.*;
import com.deeppaas.library.service.PublicLibraryService;
import com.deeppaas.result.entity.TaskErrorResultDO;
import com.deeppaas.result.enums.ErrorResultType;
import com.deeppaas.result.service.TaskErrorResultService;
import com.deeppaas.rule.dto.PublicRuleDTO;
import com.deeppaas.rule.factory.RuleExecuteFactory;
import com.deeppaas.rule.service.PublicRuleService;
import com.deeppaas.rule.service.RuleExecuteFactoryService;
import com.deeppaas.task.config.dto.ProjectTaskConfigDTO;
import com.deeppaas.task.config.service.ProjectTaskConfigService;
import com.deeppaas.task.config.service.ProjectTaskUploadService;
import com.deeppaas.task.data.dto.ProjectTaskFormDataDTO;
import com.deeppaas.task.data.dto.ProjectTaskImageDataDTO;
import com.deeppaas.task.data.entity.ProjectTaskFormDataDO;
import com.deeppaas.task.data.service.ProjectTaskFormDataService;
import com.deeppaas.task.data.service.ProjectTaskImageDataService;
import com.deeppaas.task.info.dto.ProjectTaskInfoDTO;
import com.deeppaas.task.info.service.ProjectTaskInfoService;
import com.deeppaas.userCheck.task.service.UserCheckService;
import com.deeppaas.work.WorkService;
import com.deeppaas.work.enums.TaskStatusEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service
public class WorkImpl implements WorkService {

    /**
     * 图像分件方式枚举
     */
    public enum ImageGroupingType {
        SINGLE_PIECE,           // 全部图像作为一件
        START_END_PAGE,         // 首页号+尾页号
        START_PAGE_COUNT,       // 首页号+页数
        PAGE_RANGE             // 起止页号（如"1-5"）
    }

    /**
     * 图像分组检查结果
     */
    public static class ImageGroupingResult {
        private ImageGroupingType groupingType;
        private Map<String, String> fieldMapping;
        private String startPageField;
        private String endPageField;
        private String pageCountField;
        private String pageRangeField;

        public ImageGroupingResult(ImageGroupingType groupingType) {
            this.groupingType = groupingType;
            this.fieldMapping = new HashMap<>();
        }

        public boolean needsGrouping() {
            return groupingType != ImageGroupingType.SINGLE_PIECE;
        }

        // Getters and setters
        public ImageGroupingType getGroupingType() { return groupingType; }
        public void setGroupingType(ImageGroupingType groupingType) { this.groupingType = groupingType; }
        public Map<String, String> getFieldMapping() { return fieldMapping; }
        public void setFieldMapping(Map<String, String> fieldMapping) { this.fieldMapping = fieldMapping; }
        public String getStartPageField() { return startPageField; }
        public void setStartPageField(String startPageField) { this.startPageField = startPageField; }
        public String getEndPageField() { return endPageField; }
        public void setEndPageField(String endPageField) { this.endPageField = endPageField; }
        public String getPageCountField() { return pageCountField; }
        public void setPageCountField(String pageCountField) { this.pageCountField = pageCountField; }
        public String getPageRangeField() { return pageRangeField; }
        public void setPageRangeField(String pageRangeField) { this.pageRangeField = pageRangeField; }
    }

    @Autowired
    private ExecutorService executorService;
    @Autowired
    private ProjectTaskConfigService projectTaskConfigService;

    @Autowired
    private ProjectTaskInfoService projectTaskInfoService;
    @Autowired
    private ProjectTaskFormDataService projectTaskFormDataService;
    @Autowired
    private ProjectTaskImageDataService projectTaskImageDataService;
    @Autowired
    private PublicRuleService publicRuleService;
    @Autowired
    private PublicLibraryService publicLibraryService;
    @Autowired
    private TaskErrorResultService taskErrorResultService;
    @Autowired
    private RuleExecuteFactory ruleExecuteFactory;

    @Autowired
    private ExactnessConfig exactnessConfig;
    @Autowired
    private UserCheckService userCheckService;
    @Autowired
    private ProjectTaskUploadService projectTaskUploadService;

    public void start(String taskId) {
        projectTaskFormDataService.delTaskId(taskId);
        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.ING.getNum());
        taskInfoDTO.setStartTime(LocalDateTime.now());
        taskInfoDTO.setTaskProgress(new BigDecimal("0"));
        taskInfoDTO.setExcelProgress(new BigDecimal("0"));
        taskInfoDTO.setUserCheckProgress(0f);
        taskInfoDTO.setImageProgress(new BigDecimal("0"));
        projectTaskInfoService.save(taskInfoDTO);
        //开始前删除所有目录数据跟图像数据
        taskErrorResultService.delTaskId(taskId);
        userCheckService.delByTaskId(taskId);

        projectTaskImageDataService.updateSuccess(taskId, BoolHelper.INT_FALSE);
        executor(taskInfoDTO);
        //设置抽查
        userCheckService.batchAllocation(taskId);
    }


    public void executor(ProjectTaskInfoDTO taskInfoDTO) {

        String taskId = taskInfoDTO.getId();
        List<ProjectTaskImageDataDTO> imageDataDOS = projectTaskImageDataService.findByTaskId(taskId);
        List<ProjectTaskConfigDTO> taskConfigDTOS = projectTaskConfigService.findByTaskId(taskId);
        for (int j = 0; j < taskConfigDTOS.size(); j++) {
            ProjectTaskConfigDTO taskConfigDTO = taskConfigDTOS.get(j);
            buildExcel(taskId, taskConfigDTO, imageDataDOS);
        }
        if (CollectionUtils.isEmpty(imageDataDOS))
            imageDataDOS = projectTaskImageDataService.findByTaskId(taskId);

        List<ProjectTaskFormDataDTO> lists = projectTaskFormDataService.findByTaskId(taskId);
        if (CollectionUtils.isEmpty(imageDataDOS))
            imageDataDOS = projectTaskImageDataService.findByTaskId(taskId);

        List<ProjectTaskConfigDTO> imageConfig = taskConfigDTOS.stream().filter(item -> Objects.equals(item.getRuleConfigType(), FileEnum.C.getNum())).collect(Collectors.toList());
        List<ProjectTaskConfigDTO> excelConfig = taskConfigDTOS.stream().filter(item -> Objects.equals(item.getRuleConfigType(), FileEnum.A.getNum()) || Objects.equals(item.getRuleConfigType(), FileEnum.B.getNum())).collect(Collectors.toList());
        taskConfigDTOS=taskConfigDTOS.stream().filter(item -> !Objects.equals(item.getRuleConfigType(), FileEnum.D.getNum())&&!Objects.equals(item.getRuleConfigType(), FileEnum.E.getNum())).collect(Collectors.toList());
        for (int j = 0; j < taskConfigDTOS.size(); j++) {
            ProjectTaskConfigDTO taskConfigDTO = taskConfigDTOS.get(j);
            if (!Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.D.getNum())) {
                //获取任务对应规则模板
                String templateId = taskConfigDTO.getRuleTemplateId();

                //获取分批执行规则 图像规则/条目规则/联合规则
                List<PublicRuleDTO> ruleDTOS = publicRuleService.findByLibraryId(templateId);
                if(CollectionUtils.isEmpty(ruleDTOS)){
                    throw RunException.optReject("未找到任何规则，请检查是否配置规则，如配置的图像规则，请点击保存！");
                }

                // 🔍 检查是否包含档案要素检查规则
                // PublicRuleDTO archiveElementsRule = ruleDTOS.stream()
                //     .filter(rule -> "archiveElements".equals(rule.getRuleCode()))
                //     .findFirst()
                //     .orElse(null);

                // if (archiveElementsRule != null) {
                //     // 🚀 执行档案要素检查，传入规则信息以获取正确的规则名称
                //     executeArchiveElementsCheck(taskConfigDTO, lists, imageDataDOS, taskInfoDTO, archiveElementsRule);
                // }

                // 继续执行其他规则（现有逻辑保持不变）
                for (int i = 0; i < ruleDTOS.size(); i++) {
                    PublicRuleDTO ruleDTO = ruleDTOS.get(i);
                    executorService.execute(new Task(ruleDTO, lists, imageDataDOS, taskConfigDTO, taskInfoDTO));
                    // 跳过档案要素规则，因为已经单独处理
                    // if (!"archiveElements".equals(ruleDTO.getRuleCode())) {
                    //     executorService.execute(new Task(ruleDTO, lists, imageDataDOS, taskConfigDTO, taskInfoDTO));
                    // }
                }
                try {
                    Thread.sleep(3*1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                BigDecimal b = new BigDecimal(String.valueOf(excelConfig.size()));
                //更新进度条excel
                if (Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.A.getNum()) || Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.B.getNum())) {
                    BigDecimal a1 = new BigDecimal("0");
                    if (CollectionUtils.isEmpty(imageConfig))
                        a1 = new BigDecimal("100");
                    projectTaskInfoService.updateTaskProcess(taskId, a1.divide(b, 2, BigDecimal.ROUND_HALF_UP), FileEnum.A.getNum());

                }
            }
        }
    }


    @Override
    public void stop(String taskId) {

        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);

        projectTaskFormDataService.delTaskId(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.READY.getNum());
        taskInfoDTO.setTaskProgress(new BigDecimal("0"));
        projectTaskInfoService.save(taskInfoDTO);
        //开始前删除所有目录数据跟图像数据
        taskErrorResultService.delTaskId(taskId);
        //更新图像数据状态
        projectTaskImageDataService.updateSuccess(taskId, BoolHelper.INT_FALSE);
    }

    @Override
    public void suspend(String taskId) {
        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);
        //删除excel数据
        projectTaskFormDataService.delTaskId(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.STOP.getNum());
        projectTaskInfoService.save(taskInfoDTO);
        //删除所有除了图像以外的数据
        taskErrorResultService.delTaskIdAndNotAiCheck(taskId);
    }

    @Override
    public void continueTask(String taskId) {
        projectTaskFormDataService.delTaskId(taskId);
        ProjectTaskInfoDTO taskInfoDTO = projectTaskInfoService.findById(taskId);
        taskInfoDTO.setTaskStatus(TaskStatusEnums.ING.getNum());
        taskInfoDTO.setStartTime(LocalDateTime.now());
        taskInfoDTO.setExcelProgress(new BigDecimal("0"));
        projectTaskInfoService.save(taskInfoDTO);
        //删除所有除了图像以外的数据
        taskErrorResultService.delTaskIdAndNotAiCheck(taskId);
        executor(taskInfoDTO);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Task implements Runnable {
        private PublicRuleDTO ruleDTO;

        private List<ProjectTaskFormDataDTO> lists;
        private List<ProjectTaskImageDataDTO> imageDataDOS;
        private ProjectTaskConfigDTO taskConfigDTO;
        private ProjectTaskInfoDTO projectTaskInfoDTO;

        @Override
        public void run() {
            try {

                RuleExecuteFactoryService executeFactoryService = ruleExecuteFactory.get(ruleDTO.getRuleCode());
                List<ProjectTaskFormDataDTO> configs = null;
                if (Objects.equals(ruleDTO.getRuleType(), FileEnum.C.getNum())) {
                    configs = lists;

                } else {
                    configs = lists.stream().filter(item -> Objects.equals(item.getTaskConfigId(), taskConfigDTO.getId())).collect(Collectors.toList());
                }

                List<TaskErrorResultDO> list = executeFactoryService.ruleExecute(ruleDTO, configs, imageDataDOS, projectTaskInfoDTO, null,taskConfigDTO);
                taskErrorResultService.saves(list);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }


    private void buildExcel(String taskId, ProjectTaskConfigDTO taskConfigDTO, List<ProjectTaskImageDataDTO> imageDataDOS) {
        Integer configType = taskConfigDTO.getRuleConfigType();

        System.out.println("🔧🔧🔧 === buildExcel方法被调用 === 🔧🔧🔧");
        System.out.println("📋 任务ID: " + taskId);
        System.out.println("📋 配置ID: " + taskConfigDTO.getId());
        System.out.println("📋 配置类型: " + configType + " (FileEnum.C=" + FileEnum.C.getNum() + ")");
        System.out.println("📋 图像数据是否为空: " + CollectionUtils.isEmpty(imageDataDOS));
        System.out.println("📋 图像数据数量: " + (imageDataDOS != null ? imageDataDOS.size() : 0));

        if (Objects.equals(configType, FileEnum.C.getNum()) && CollectionUtils.isEmpty(imageDataDOS)) {
            System.out.println("✅ 满足图像构建条件，调用buildImage方法");
            projectTaskUploadService.buildImage(taskId, taskConfigDTO);
        } else {
            System.out.println("❌ 不满足图像构建条件:");
            System.out.println("   - 配置类型匹配: " + Objects.equals(configType, FileEnum.C.getNum()));
            System.out.println("   - 图像数据为空: " + CollectionUtils.isEmpty(imageDataDOS));
        }

        if (!Objects.equals(configType, FileEnum.A.getNum()) && !Objects.equals(configType, FileEnum.B.getNum())) {
            return;
        }

        ExcelReader excelReader = null;
        try {
            String excelPath = taskConfigDTO.getFilePath();
            Map<String, String> ruleMapping = taskConfigDTO.buildRuleMappingMap();
            List<ProjectTaskFormDataDO> lists = Lists.newArrayList();
            excelReader = ExcelReader.of(new File(excelPath));
            //1.将图像/excel数据库数据落地
            String sheetName = taskConfigDTO.getSheetName();
            if (StringHelper.isEmpty(sheetName)) {
                sheetName = excelReader.getSheetName().get(0);
            }
            List<String> headersName = excelReader.getHeaders(sheetName);

            List<String[]> datas = excelReader.getData(taskConfigDTO.getSheetName(), headersName.size());
            //转化dataKey
            String urlRule = taskConfigDTO.getUrlRule();
            List<SelectDTO> selectDTOS = JsonHelper.readToLists(urlRule, SelectDTO.class);

            //保存excel数据
            for (int i = 0; i < datas.size(); i++) {
                String[] columnsData = datas.get(i);
                Map<String, Object> taskJson = new HashMap<>();
                ProjectTaskFormDataDO formDataDO = new ProjectTaskFormDataDO();
                for (int j = 0; j < headersName.size(); j++) {
                    String col = columnsData[j];
                    String titleName = headersName.get(j).replaceAll(" ", "");
                    String key = ruleMapping.get(titleName);
                    key = StringHelper.isEmpty(key) ? titleName : key;
                    taskJson.put(key, col);
                }
                StringBuffer dataKey = new StringBuffer();
                for (int j = 0; j < selectDTOS.size(); j++) {
                    SelectDTO selectDTO = selectDTOS.get(j);
                    String label = selectDTO.getLabel();
                    Object value = taskJson.get(label);
                    if (!NumeralrHelper.isOdd(j)) {
                        dataKey.append(label);
                    }
                    if (value != null) {
                        dataKey.append(StringHelper.toString(value));
                    }
                }

                if (BoolHelper.intToBool(taskConfigDTO.getPieceHaveIs()) && dataKey.indexOf("-") > 0) {
                    formDataDO.setDataKey(dataKey.substring(0, dataKey.lastIndexOf("-")));
                    formDataDO.setPartNumber(dataKey.substring(dataKey.lastIndexOf("-") + 1));
                } else {
                    formDataDO.setDataKey(dataKey.toString());
                }


                formDataDO.setRowNum(i + 2);
                formDataDO.setTaskConfigId(taskConfigDTO.getId());
                formDataDO.setTaskId(taskId);

                formDataDO.setTaskJson(JsonHelper.toJson(taskJson));
                formDataDO.setCreateTime(LocalDateTime.now());
                formDataDO.setMateIs(BoolHelper.INT_FALSE);
                ProjectTaskImageDataDTO dataDTO = imageDataDOS.stream().filter(item -> Objects.equals(item.getDataKey(), formDataDO.getDataKey())).findFirst().orElse(null);
                if (dataDTO != null) {
                    formDataDO.setMateIs(BoolHelper.INT_TRUE);
                }

                lists.add(formDataDO);
            }
            projectTaskFormDataService.saves(lists);
            excelReader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelReader != null) excelReader.close();
        }
    }

    /**
     * 执行档案要素检查
     * 调用Python AI服务进行批量档案要素提取和比对
     */
    private void executeArchiveElementsCheck(ProjectTaskConfigDTO taskConfigDTO,
                                           List<ProjectTaskFormDataDTO> formDataList,
                                           List<ProjectTaskImageDataDTO> imageDataList,
                                           ProjectTaskInfoDTO taskInfoDTO,
                                           PublicRuleDTO archiveElementsRule) {
        try {
            String taskId = taskInfoDTO.getId();

            System.out.println("🔍🔍🔍 === 开始档案要素检查 === 🔍🔍🔍");
            System.out.println("📋 任务ID: " + taskId);
            System.out.println("📋 配置ID: " + taskConfigDTO.getId());
            System.out.println("📋 Excel条目数量: " + (formDataList != null ? formDataList.size() : 0));
            System.out.println("📋 图像数据数量: " + (imageDataList != null ? imageDataList.size() : 0));

            // 🔍 1. 检查是否需要图像分件处理
            ImageGroupingResult groupingResult = checkImageGroupingNeeded(formDataList, taskConfigDTO);
            System.out.println("🔍 分件检查结果: " + groupingResult.getGroupingType());

            List<ProjectTaskImageDataDTO> processedImageList = imageDataList;

            if (groupingResult.needsGrouping()) {
                System.out.println("🚀 开始执行图像分件处理");
                // 🔍 2. 执行图像分件处理（内存中处理，不存数据库）
                processedImageList = processImageGroupingInMemory(formDataList, imageDataList, taskConfigDTO, groupingResult);
                System.out.println("📊 分件处理完成，处理后图像组数量: " + processedImageList.size());
            } else {
                System.out.println("📝 无需分件处理，使用原始图像数据");
            }

            // 🔍 3. 继续执行档案要素检查逻辑（使用处理后的图像数据）
            System.out.println("📋 开始档案要素提取和比对");
            System.out.println("📋 使用图像数据数量: " + (processedImageList != null ? processedImageList.size() : 0));

            // 🔍 空值检查
            if (formDataList == null || formDataList.isEmpty()) {
                System.err.println("❌ Excel数据为空，无法执行档案要素检查");
                return;
            }

            if (processedImageList == null || processedImageList.isEmpty()) {
                System.err.println("❌ 图像数据为空，无法执行档案要素检查");
                return;
            }

            // 🔍 解析规则模板配置获取字段映射
            Map<String, String> elementToExcelFieldMap = getElementToExcelFieldMapping(archiveElementsRule, taskConfigDTO);
            System.out.println("档案要素字段映射: " + elementToExcelFieldMap);

            // 🔍 准备Excel数据
            List<Map<String, Object>> excelData = new ArrayList<>();
            for (ProjectTaskFormDataDTO formData : formDataList) {
                if (Objects.equals(formData.getTaskConfigId(), taskConfigDTO.getId())) {
                    Map<String, Object> rowData = new HashMap<>();
                    rowData.put("dataKey", formData.getDataKey());
                    rowData.put("partNumber", formData.getPartNumber()); // 🔍 添加件号信息
                    rowData.put("rowNum", formData.getRowNum());

                    System.out.println(String.format("📋 Excel数据行: dataKey=%s, partNumber=%s, rowNum=%d",
                        formData.getDataKey(), formData.getPartNumber(), formData.getRowNum()));

                    // 🔍 根据规则配置的字段映射获取Excel数据
                    String taskJson = formData.getTaskJson();
                    if (StringHelper.isNotEmpty(taskJson)) {
                        Map<String, Object> taskData = JsonHelper.json2map(taskJson);

                        // 使用动态字段映射
                        for (Map.Entry<String, String> mapping : elementToExcelFieldMap.entrySet()) {
                            String elementKey = mapping.getKey();     // 如: "title"
                            String excelFieldName = mapping.getValue(); // 如: "卷内题名"
                            Object excelValue = taskData.get(excelFieldName);
                            rowData.put(elementKey, excelValue);
                            System.out.println(String.format("字段映射 - %s -> %s: [%s]", elementKey, excelFieldName, excelValue));
                        }
                    }
                    excelData.add(rowData);
                }
            }

            // 🖼️ 准备图像文件路径
            List<String> imagePaths = new ArrayList<>();

            System.out.println("🖼️🖼️🖼️ === 收集图像路径信息 === 🖼️🖼️🖼️");
            System.out.println("processedImageList数量: " + processedImageList.size());

            for (int i = 0; i < processedImageList.size(); i++) {
                ProjectTaskImageDataDTO imageData = processedImageList.get(i);
                System.out.println(String.format("图像数据[%d]:", i));
                System.out.println("  - ID: " + imageData.getId());
                System.out.println("  - dataKey: " + imageData.getDataKey());
                System.out.println("  - partNumber: " + imageData.getPartNumber());
                System.out.println("  - imageFilePath: " + imageData.getImageFilePath());
                System.out.println("  - imageNames: " + imageData.getImageNames());
                System.out.println("  - imageCount: " + imageData.getImageCount());

                if (StringHelper.isNotEmpty(imageData.getImageFilePath())) {
                    imagePaths.add(imageData.getImageFilePath());
                    System.out.println("  ✅ 添加到图像路径列表: " + imageData.getImageFilePath());
                } else {
                    System.out.println("  ❌ 图像路径为空，跳过");
                }
            }

            System.out.println("📊 最终图像路径列表:");
            for (int i = 0; i < imagePaths.size(); i++) {
                System.out.println(String.format("  [%d] %s", i, imagePaths.get(i)));
            }
            System.out.println("🖼️🖼️🖼️ === 图像路径收集完成 === 🖼️🖼️🖼️");

            if (excelData.isEmpty() || imagePaths.isEmpty()) {
                System.err.println("❌ 数据检查失败:");
                System.err.println("  Excel数据为空: " + excelData.isEmpty());
                System.err.println("  图像路径为空: " + imagePaths.isEmpty());
                return; // 没有数据需要处理
            }

            // 🚀 调用Python AI服务 - 传递完整的分件数据
            ArchiveElementsCheckResult result = callPythonArchiveService(taskId, excelData, processedImageList);

            if (result != null && result.isSuccess()) {
                // 📝 处理检查结果，保存错误信息
                saveArchiveElementsErrors(result, taskConfigDTO, taskInfoDTO, archiveElementsRule);
            }

        } catch (Exception e) {
            // 记录错误但不中断整个流程
            System.err.println("档案要素检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 调用Python档案要素检查服务 - 支持智能提取策略和分件数据
     */
    private ArchiveElementsCheckResult callPythonArchiveService(String taskId,
                                                              List<Map<String, Object>> excelData,
                                                              List<ProjectTaskImageDataDTO> imageDataList) {
        try {
            // 🌐 智能提取AI服务地址
            String serviceUrl = "http://localhost:8080/extract/archive/batch_compare";

            // � 准备分件数据 - 转换为Python端需要的格式
            List<Map<String, Object>> imageDataForPython = new ArrayList<>();
            for (ProjectTaskImageDataDTO imageData : imageDataList) {
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("path", imageData.getImageFilePath());
                imageInfo.put("filename", imageData.getImageFilePath() != null ?
                    new File(imageData.getImageFilePath()).getName() : "unknown");
                imageInfo.put("dataKey", imageData.getDataKey());
                imageInfo.put("partNumber", imageData.getPartNumber());
                imageInfo.put("imageNames", imageData.getImageNames());
                imageInfo.put("imageCount", imageData.getImageCount());
                imageDataForPython.add(imageInfo);

                System.out.println(String.format("🐍 传递给Python的图像数据: dataKey=%s, partNumber=%s, path=%s, imageNames=%s",
                    imageData.getDataKey(), imageData.getPartNumber(), imageData.getImageFilePath(), imageData.getImageNames()));
            }

            // �📝 准备请求参数
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            params.add("task_id", taskId);
            params.add("excel_data", JsonHelper.toJson(excelData));
            params.add("image_data", JsonHelper.toJson(imageDataForPython)); // 🔍 传递完整的分件数据
            params.add("elements", JsonHelper.toJson(Arrays.asList("title", "responsible_party", "document_number", "issue_date")));
            params.add("confidence_threshold", 0.5);
            params.add("similarity_threshold", 0.8);
            params.add("enable_stamp_processing", true);  // 启用印章处理
            params.add("stamp_confidence_threshold", 0.5); // 适合OpenCV的置信度阈值
            params.add("enable_preprocessing", true);  // 启用图像预处理

            System.out.println("🐍🐍🐍 === 调用Python服务参数 === 🐍🐍🐍");
            System.out.println("task_id: " + taskId);
            System.out.println("excel_data条目数: " + excelData.size());
            System.out.println("image_data条目数: " + imageDataForPython.size());
            System.out.println("🐍🐍🐍 === 参数准备完成 === 🐍🐍🐍");

            // 🚀 发送HTTP请求
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                // 📊 解析响应结果
                String responseBody = response.getBody();

                // 🔍 添加调试日志 - 查看Python服务响应
                System.out.println("🐍🐍🐍 === Python档案要素检查服务完整响应 === 🐍🐍🐍");
                System.out.println("响应状态码: " + response.getStatusCode());
                System.out.println("响应体长度: " + (responseBody != null ? responseBody.length() : 0));
                System.out.println("响应内容: " + responseBody);

                Map<String, Object> resultMap = JsonHelper.json2map(responseBody);

                // 🔍 详细解析响应结构
                System.out.println("📊📊📊 === 解析响应结构 === 📊📊📊");
                System.out.println("success: " + resultMap.get("success"));
                System.out.println("task_id: " + resultMap.get("task_id"));
                System.out.println("error: " + resultMap.get("error"));

                Object comparisonResult = resultMap.get("comparison_result");
                System.out.println("comparison_result类型: " + (comparisonResult != null ? comparisonResult.getClass().getSimpleName() : "null"));

                if (comparisonResult instanceof Map) {
                    Map<String, Object> compMap = (Map<String, Object>) comparisonResult;
                    System.out.println("comparison_result条目数: " + compMap.size());
                    System.out.println("comparison_result键列表: " + compMap.keySet());

                    // 🔍 详细打印每个dataKey的比对结果
                    for (Map.Entry<String, Object> entry : compMap.entrySet()) {
                        String dataKey = entry.getKey();
                        Object rowResult = entry.getValue();
                        System.out.println(String.format("  📋 dataKey: %s -> 结果类型: %s",
                            dataKey, rowResult != null ? rowResult.getClass().getSimpleName() : "null"));

                        if (rowResult instanceof Map) {
                            Map<String, Object> rowData = (Map<String, Object>) rowResult;
                            System.out.println(String.format("    📄 %s包含字段: %s", dataKey, rowData.keySet()));

                            // 打印每个要素的详细信息
                            for (Map.Entry<String, Object> elementEntry : rowData.entrySet()) {
                                String elementName = elementEntry.getKey();
                                Object elementData = elementEntry.getValue();
                                System.out.println(String.format("      🔍 %s.%s: %s",
                                    dataKey, elementName, elementData));
                            }
                        }
                    }
                }

                ArchiveElementsCheckResult result = new ArchiveElementsCheckResult();
                result.setSuccess((Boolean) resultMap.get("success"));
                result.setTaskId((String) resultMap.get("task_id"));
                result.setComparisonResult(resultMap.get("comparison_result"));
                result.setError((String) resultMap.get("error"));

                System.out.println("✅✅✅ === Java端解析完成 === ✅✅✅");
                System.out.println("最终结果success: " + result.isSuccess());
                System.out.println("最终结果task_id: " + result.getTaskId());
                System.out.println("最终结果error: " + result.getError());

                return result;
            } else {
                System.err.println("Python服务调用失败，状态码: " + response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            System.err.println("调用Python档案要素检查服务失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 转换智能提取结果为比对结果格式
     */
    private ArchiveElementsCheckResult convertSmartExtractionResult(Map<String, Object> smartResult,
                                                                  List<Map<String, Object>> excelData) {
        try {
            ArchiveElementsCheckResult result = new ArchiveElementsCheckResult();
            result.setSuccess((Boolean) smartResult.get("success"));
            result.setTaskId((String) smartResult.get("task_id"));

            if (!result.isSuccess()) {
                result.setError((String) smartResult.get("error"));
                return result;
            }

            // 📊 获取提取的要素
            Map<String, Object> extractedElements = (Map<String, Object>) smartResult.get("elements");
            if (extractedElements == null) {
                result.setSuccess(false);
                result.setError("智能提取未返回要素数据");
                return result;
            }

            // 🔄 构建比对结果格式
            Map<String, Object> comparisonResult = new HashMap<>();

            // 要素映射：中文 -> 英文
            Map<String, String> elementMapping = new HashMap<>();
            elementMapping.put("题名", "title");
            elementMapping.put("责任者", "responsible_party");
            elementMapping.put("文号", "document_number");
            elementMapping.put("成文日期", "issue_date");

            // 为每个Excel行创建比对结果
            for (int i = 0; i < excelData.size(); i++) {
                Map<String, Object> excelRow = excelData.get(i);
                String dataKey = (String) excelRow.getOrDefault("dataKey", "row_" + (i + 1));

                Map<String, Object> rowComparison = new HashMap<>();

                // 比对每个要素
                for (Map.Entry<String, String> mapping : elementMapping.entrySet()) {
                    String chineseName = mapping.getKey();
                    String englishName = mapping.getValue();

                    String extractedValue = (String) extractedElements.get(chineseName);
                    String excelValue = (String) excelRow.get(englishName);

                    Map<String, Object> elementComparison = new HashMap<>();
                    elementComparison.put("excel_value", excelValue);
                    elementComparison.put("extracted_value", extractedValue);

                    // 简单的相似度计算
                    double similarity = calculateSimilarity(excelValue, extractedValue);
                    elementComparison.put("similarity", similarity);
                    elementComparison.put("has_error", similarity < 0.8); // 相似度阈值
                    elementComparison.put("suggestion", similarity < 0.8 ? "建议人工核查" : "匹配正常");

                    rowComparison.put(englishName, elementComparison);
                }

                comparisonResult.put(dataKey, rowComparison);
            }

            result.setComparisonResult(comparisonResult);
            return result;

        } catch (Exception e) {
            System.err.println("转换智能提取结果失败: " + e.getMessage());
            e.printStackTrace();

            ArchiveElementsCheckResult errorResult = new ArchiveElementsCheckResult();
            errorResult.setSuccess(false);
            errorResult.setError("结果转换失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 计算字符串相似度（简单实现）
     */
    private double calculateSimilarity(String str1, String str2) {
        if (str1 == null && str2 == null) return 1.0;
        if (str1 == null || str2 == null) return 0.0;
        if (str1.equals(str2)) return 1.0;

        // 简单的包含关系检查
        if (str1.contains(str2) || str2.contains(str1)) {
            return 0.8;
        }

        // 默认相似度
        return 0.3;
    }

    /**
     * 保存档案要素检查错误结果
     */
    private void saveArchiveElementsErrors(ArchiveElementsCheckResult result,
                                         ProjectTaskConfigDTO taskConfigDTO,
                                         ProjectTaskInfoDTO taskInfoDTO,
                                         PublicRuleDTO archiveElementsRule) {
        try {
            if (result == null || !result.isSuccess() || result.getComparisonResult() == null) {
                return;
            }

            String taskId = taskInfoDTO.getId();
            List<TaskErrorResultDO> errorResults = new ArrayList<>();

            // � 预先获取formData映射，避免重复查询
            List<ProjectTaskFormDataDTO> formDataList = projectTaskFormDataService.findByTaskId(taskId);
            Map<String, Integer> dataKeyToRowNumMap = new HashMap<>();
            for (ProjectTaskFormDataDTO formData : formDataList) {
                if (formData.getDataKey() != null && formData.getRowNum() != null) {
                    dataKeyToRowNumMap.put(formData.getDataKey(), formData.getRowNum());
                }
            }

            // �📊 解析比对结果
            Object comparisonResult = result.getComparisonResult();
            if (comparisonResult instanceof Map) {
                Map<String, Object> resultMap = (Map<String, Object>) comparisonResult;

                // 处理每个Excel行的比对结果
                for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                    String resultKey = entry.getKey(); // 现在是 dataKey_rowNum 格式
                    Object rowResult = entry.getValue();

                    if (rowResult instanceof Map) {
                        Map<String, Object> rowData = (Map<String, Object>) rowResult;

                        // 🔍 解析新的键格式：dataKey_rowNum
                        String dataKey;
                        Integer rowNum;

                        if (resultKey.contains("_")) {
                            // 新格式：dataKey_rowNum
                            String[] parts = resultKey.split("_");
                            if (parts.length >= 2) {
                                dataKey = parts[0]; // 可能包含多个部分，如 123-023-00001
                                try {
                                    rowNum = Integer.parseInt(parts[parts.length - 1]); // 最后一部分是rowNum
                                    // 重新构建dataKey（除了最后的rowNum部分）
                                    if (parts.length > 2) {
                                        StringBuilder sb = new StringBuilder();
                                        for (int i = 0; i < parts.length - 1; i++) {
                                            if (i > 0) sb.append("_");
                                            sb.append(parts[i]);
                                        }
                                        dataKey = sb.toString();
                                    }
                                } catch (NumberFormatException e) {
                                    // 如果最后一部分不是数字，则整个作为dataKey
                                    dataKey = resultKey;
                                    rowNum = dataKeyToRowNumMap.get(dataKey);
                                }
                            } else {
                                dataKey = resultKey;
                                rowNum = dataKeyToRowNumMap.get(dataKey);
                            }
                        } else {
                            // 旧格式：纯dataKey
                            dataKey = resultKey;
                            rowNum = dataKeyToRowNumMap.get(dataKey);
                        }

                        System.out.println(String.format("🔍 解析结果键: %s -> dataKey: %s, rowNum: %d",
                            resultKey, dataKey, rowNum != null ? rowNum : -1));

                        // 检查每个档案要素的错误
                        processArchiveElementErrors(errorResults, taskId, taskConfigDTO.getId(),
                                                   dataKey, rowData, archiveElementsRule, dataKeyToRowNumMap, rowNum);
                    }
                }
            }

            // 💾 批量保存错误结果
            if (!errorResults.isEmpty()) {
                System.out.println("🔍🔍🔍 === 保存档案要素错误记录 === 🔍🔍🔍");
                System.out.println("错误记录总数: " + errorResults.size());

                // 🔍 详细打印每个错误记录
                errorResults.forEach(error -> {
                    System.out.println(String.format("保存错误记录: taskId=%s, dataKey=%s, fieldName=%s, ruleName=%s, errorRow=%d",
                        error.getTaskId(), error.getDataKey(), error.getFieldName(), error.getRuleName(), error.getErrorRow()));
                });

                // 🔍 按字段名分组统计
                Map<String, Long> fieldCounts = errorResults.stream()
                    .collect(Collectors.groupingBy(TaskErrorResultDO::getFieldName, Collectors.counting()));
                System.out.println("按字段名分组统计: " + fieldCounts);

                taskErrorResultService.saves(errorResults);
                System.out.println("保存档案要素检查错误 " + errorResults.size() + " 条");
            }

        } catch (Exception e) {
            System.err.println("保存档案要素检查错误失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理单行档案要素错误
     */
    private void processArchiveElementErrors(List<TaskErrorResultDO> errorResults,
                                           String taskId, String taskConfigId,
                                           String dataKey, Map<String, Object> rowData,
                                           PublicRuleDTO archiveElementsRule,
                                           Map<String, Integer> dataKeyToRowNumMap,
                                           Integer providedRowNum) {

        // 🔍 获取档案要素到Excel字段的映射（这个映射已经在executeArchiveElementsCheck中计算过了）
        // 但这里我们需要反向映射：从英文字段名到Excel列名
        Map<String, String> elementToExcelFieldMap = new HashMap<>();
        ProjectTaskConfigDTO taskConfigDTO = null; // 🔍 在更大的作用域中定义

        try {
            // 🔍 获取任务配置
            taskConfigDTO = projectTaskConfigService.findById(taskConfigId);

            // 🔍 解析规则模板配置获取字段映射
            String ruleValue = archiveElementsRule.getRuleValue();
            if (StringHelper.isNotEmpty(ruleValue)) {
                Map<String, Object> ruleConfig = JsonHelper.json2map(ruleValue);
                Map<String, Object> fieldMapping = (Map<String, Object>) ruleConfig.get("fieldMapping");

                if (fieldMapping != null) {
                    // 🔍 获取字段库映射 (字段ID -> Excel列名)
                    Map<String, String> fieldLibraryMap = taskConfigDTO.buildRuleKeyFieldMap();

                    // 🔍 构建档案要素到Excel字段的映射
                    for (Map.Entry<String, Object> entry : fieldMapping.entrySet()) {
                        String elementKey = entry.getKey();        // 如: "title"
                        String fieldId = (String) entry.getValue(); // 如: "field_id_123"

                        // 从字段库映射中获取Excel列名
                        String excelFieldName = fieldLibraryMap.get(fieldId);
                        if (StringHelper.isNotEmpty(excelFieldName)) {
                            elementToExcelFieldMap.put(elementKey, excelFieldName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("获取字段映射失败: " + e.getMessage());
        }

        // 🔍 如果没有配置，使用默认映射（使用实际的Excel列名）
        if (elementToExcelFieldMap.isEmpty()) {
            elementToExcelFieldMap.put("title", "卷内题名");        // ✅ 使用实际Excel列名
            elementToExcelFieldMap.put("responsible_party", "责任者"); // ✅ 正确
            elementToExcelFieldMap.put("document_number", "文号");    // ✅ 正确
            elementToExcelFieldMap.put("issue_date", "成文日期");     // ✅ 使用实际Excel列名
        }

        // 🔍 获取Excel行号 - 优先使用传入的rowNum，回退到映射查找
        Integer errorRow = providedRowNum;
        if (errorRow == null) {
            errorRow = dataKeyToRowNumMap.get(dataKey);
            if (errorRow == null) {
                System.err.println("未找到dataKey对应的rowNum: " + dataKey + "，使用默认行号2");
                errorRow = 2; // 默认为第2行（跳过标题行）
            }
        }

        System.out.println(String.format("🔍 确定Excel行号: dataKey=%s, providedRowNum=%s, finalRowNum=%d",
            dataKey, providedRowNum, errorRow));

        // 🔍 添加调试日志 - 查看rowData的完整内容
        System.out.println(String.format("rowData内容 - dataKey: %s, 数据: %s", dataKey, rowData));

        // 检查每个要素的错误
        for (Map.Entry<String, String> element : elementToExcelFieldMap.entrySet()) {
            String elementKey = element.getKey();
            String excelFieldName = element.getValue();

            Object elementResult = rowData.get(elementKey);
            System.out.println(String.format("查找要素 - elementKey: %s, excelFieldName: %s, elementResult: %s",
                elementKey, excelFieldName, elementResult));

            if (elementResult instanceof Map) {
                Map<String, Object> elementData = (Map<String, Object>) elementResult;

                Boolean hasError = (Boolean) elementData.get("has_error");
                String excelValue = (String) elementData.get("excel_value");
                String extractedValue = (String) elementData.get("extracted_value");
                Double similarity = (Double) elementData.get("similarity");

                // 🔍 添加调试日志
                System.out.println(String.format("档案要素检查 - dataKey: %s, 要素: %s, Excel值: [%s], 提取值: [%s], 相似度: %.2f, 有错误: %s",
                    dataKey, excelFieldName, excelValue, extractedValue, similarity != null ? similarity : 0.0, hasError));

                if (hasError != null && hasError) {
                    // 🚨 创建错误记录 - 直接使用Excel字段名作为fieldName，因为Excel导出时期望的就是Excel列名
                    // 从日志分析：keyValueMaps = {卷内题名=卷内题名}，所以chineseFieldName就是Excel列名
                    TaskErrorResultDO errorResult = TaskErrorResultDO.builder()
                        .taskId(taskId)
                        .taskConfigId(taskConfigId)
                        .dataKey(dataKey)
                        .fieldName(excelFieldName) // 🔍 直接使用Excel字段名，这样Excel导出时能正确匹配
                        .ruleName(archiveElementsRule.getRuleAliasName()) // 🔍 使用规则模板中定义的规则名称
                        .ruleType("archiveElements")
                        .errorType(ErrorResultType.RULE.getNum()) // 使用RULE类型
                        .errorRow(errorRow) // 🔍 设置Excel行号
                        .aiCheck(1) // 标记为AI检查
                        .build();

                    System.out.println(String.format("🔍 创建档案要素错误记录 - taskId: %s, fieldName: %s, ruleName: %s, ruleType: %s, errorType: %d, errorRow: %d",
                        taskId, excelFieldName, archiveElementsRule.getRuleAliasName(), "archiveElements", ErrorResultType.RULE.getNum(), errorRow));

                    String suggestion = (String) elementData.get("suggestion");

                    String errorDescription = String.format(
                        "%s不匹配：Excel值[%s] vs 提取值[%s]，相似度%.2f",
                        excelFieldName, excelValue, extractedValue, similarity != null ? similarity : 0.0
                    );

                    errorResult.setErrorFileValue(errorDescription);
                    errorResult.setErrorCoordinate(suggestion); // 将建议存储在坐标字段中

                    errorResults.add(errorResult);
                }
            }
        }
    }

    /**
     * 从规则模板配置中获取档案要素到Excel字段的映射关系
     */
    private Map<String, String> getElementToExcelFieldMapping(PublicRuleDTO archiveElementsRule, ProjectTaskConfigDTO taskConfigDTO) {
        Map<String, String> elementToExcelFieldMap = new HashMap<>();

        try {
            // 🔍 解析规则模板配置
            String ruleValue = archiveElementsRule.getRuleValue();
            System.out.println("🔍 规则模板配置 ruleValue: " + ruleValue);

            if (StringHelper.isNotEmpty(ruleValue)) {
                Map<String, Object> ruleConfig = JsonHelper.json2map(ruleValue);
                System.out.println("🔍 解析后的规则配置: " + ruleConfig);

                Map<String, Object> fieldMapping = (Map<String, Object>) ruleConfig.get("fieldMapping");
                System.out.println("🔍 字段映射配置: " + fieldMapping);

                if (fieldMapping != null) {
                    // 🔍 获取字段库映射 (字段ID -> Excel列名)
                    // 注意：应该使用 ruleMapping 而不是 ruleKeyFields
                    System.out.println("🔍 taskConfigDTO.getRuleMapping(): " + taskConfigDTO.getRuleMapping());

                    // 🔍 构建反向映射：字段ID -> Excel列名
                    Map<String, String> fieldIdToExcelNameMap = new HashMap<>();
                    List<KeyValueDTO> ruleMappingList = taskConfigDTO.buildRuleMapping();
                    if (ruleMappingList != null) {
                        for (KeyValueDTO mapping : ruleMappingList) {
                            String excelColumnName = mapping.getKey();    // Excel列名，如"卷内题名"
                            String fieldId = mapping.getValue();          // 字段ID，如"1a0p824p00z0p5svjj"
                            fieldIdToExcelNameMap.put(fieldId, excelColumnName);
                            System.out.println(String.format("🔍 构建反向映射 - fieldId: %s -> excelColumnName: %s", fieldId, excelColumnName));
                        }
                    }
                    System.out.println("🔍 字段ID到Excel列名映射: " + fieldIdToExcelNameMap);

                    // 🔍 构建档案要素到Excel字段的映射
                    for (Map.Entry<String, Object> entry : fieldMapping.entrySet()) {
                        String elementKey = entry.getKey();        // 如: "title"
                        String fieldId = (String) entry.getValue(); // 如: "field_id_123"
                        System.out.println(String.format("🔍 处理映射 - elementKey: %s, fieldId: %s", elementKey, fieldId));

                        // 从字段库映射中获取Excel列名
                        String excelFieldName = fieldIdToExcelNameMap.get(fieldId);
                        System.out.println(String.format("🔍 字段库查找结果 - fieldId: %s -> excelFieldName: %s", fieldId, excelFieldName));

                        if (StringHelper.isNotEmpty(excelFieldName)) {
                            elementToExcelFieldMap.put(elementKey, excelFieldName);
                            System.out.println(String.format("✅ 映射成功 - %s -> %s (字段ID: %s)", elementKey, excelFieldName, fieldId));
                        } else {
                            System.out.println(String.format("❌ 映射失败 - 字段ID %s 在字段库中未找到对应的Excel列名", fieldId));
                        }
                    }
                } else {
                    System.out.println("❌ fieldMapping 为空");
                }
            } else {
                System.out.println("❌ ruleValue 为空");
            }

            // 🔍 如果没有配置或配置为空，使用默认映射（使用实际的Excel列名）
            if (elementToExcelFieldMap.isEmpty()) {
                System.out.println("使用默认档案要素字段映射");
                elementToExcelFieldMap.put("title", "卷内题名");        // ✅ 使用实际Excel列名
                elementToExcelFieldMap.put("responsible_party", "责任者"); // ✅ 正确
                elementToExcelFieldMap.put("document_number", "文号");    // ✅ 正确
                elementToExcelFieldMap.put("issue_date", "成文日期");     // ✅ 使用实际Excel列名
            }

        } catch (Exception e) {
            System.err.println("解析档案要素字段映射失败: " + e.getMessage());
            e.printStackTrace();

            // 🔍 异常情况下使用默认映射（使用实际的Excel列名）
            elementToExcelFieldMap.put("title", "题名");        // ✅ 使用实际Excel列名
            elementToExcelFieldMap.put("responsible_party", "责任者"); // ✅ 正确
            elementToExcelFieldMap.put("document_number", "文号");    // ✅ 正确
            elementToExcelFieldMap.put("issue_date", "成文日期");     // ✅ 使用实际Excel列名
        }

        return elementToExcelFieldMap;
    }

    /**
     * 档案要素检查结果数据模型
     */
    private static class ArchiveElementsCheckResult {
        private boolean success;
        private String taskId;
        private Object comparisonResult;
        private String error;

        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        public Object getComparisonResult() { return comparisonResult; }
        public void setComparisonResult(Object comparisonResult) { this.comparisonResult = comparisonResult; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }

    /**
     * 检查是否需要图像分件处理
     */
    private ImageGroupingResult checkImageGroupingNeeded(List<ProjectTaskFormDataDTO> formDataList,
                                                        ProjectTaskConfigDTO taskConfigDTO) {
        System.out.println("🔍🔍🔍 === 开始检测图像分件方式 === 🔍🔍🔍");

        // 🔍 从字段库配置中查找关键字段
        Map<String, String> fieldMapping = taskConfigDTO.buildRuleMappingMap();

        System.out.println("📋 字段映射信息:");
        if (fieldMapping == null || fieldMapping.isEmpty()) {
            System.out.println("  ❌ 字段映射为空，使用单件处理模式");
            return new ImageGroupingResult(ImageGroupingType.SINGLE_PIECE);
        } else {
            System.out.println("  ✅ 字段映射数量: " + fieldMapping.size());
            fieldMapping.forEach((key, value) ->
                System.out.println("    " + key + " -> " + value));
        }

        // 🔍 检查是否存在分件相关字段
        boolean hasStartPage = containsFieldValue(fieldMapping, "首页号")||containsFieldValue(fieldMapping, "起始页号");
        boolean hasEndPage = containsFieldValue(fieldMapping, "尾页号")||containsFieldValue(fieldMapping, "终止页号");
        boolean hasPageCount = containsFieldValue(fieldMapping, "页数");
        boolean hasPageRange = containsFieldValue(fieldMapping, "起止页号")||containsFieldValue(fieldMapping, "起讫页号")||containsFieldValue(fieldMapping, "首尾页号");

        System.out.println("🔍 分件字段检测结果:");
        System.out.println("  首页号: " + (hasStartPage ? "✅ 存在" : "❌ 不存在"));
        System.out.println("  尾页号: " + (hasEndPage ? "✅ 存在" : "❌ 不存在"));
        System.out.println("  页数: " + (hasPageCount ? "✅ 存在" : "❌ 不存在"));
        System.out.println("  起止页号: " + (hasPageRange ? "✅ 存在" : "❌ 不存在"));

        // 🔍 创建结果对象
        ImageGroupingResult result;

        // 🔍 按优先级确定处理方式
        if (hasPageRange) {
            System.out.println("🎯 检测到起止页号字段，使用PAGE_RANGE模式");
            result = new ImageGroupingResult(ImageGroupingType.PAGE_RANGE);
            // 🔍 按优先级查找页号范围字段
            String pageRangeField = getFieldIdByMultipleValues(fieldMapping,
                "起止页号", "起讫页号", "首尾页号");
            result.setPageRangeField(pageRangeField);
            System.out.println("📋 使用页号范围字段: " + pageRangeField);
        } else if (hasStartPage && hasEndPage) {
            System.out.println("🎯 检测到首页号+尾页号字段，使用START_END_PAGE模式");
            result = new ImageGroupingResult(ImageGroupingType.START_END_PAGE);
            // 🔍 按优先级查找首页号字段
            String startPageField = getFieldIdByMultipleValues(fieldMapping,
                "首页号", "起始页号");
            // 🔍 按优先级查找尾页号字段
            String endPageField = getFieldIdByMultipleValues(fieldMapping,
                "尾页号", "终止页号");
            result.setStartPageField(startPageField);
            result.setEndPageField(endPageField);
            System.out.println("📋 使用首页号字段: " + startPageField);
            System.out.println("📋 使用尾页号字段: " + endPageField);
        } else if (hasStartPage && hasPageCount) {
            System.out.println("🎯 检测到首页号+页数字段，使用START_PAGE_COUNT模式");
            result = new ImageGroupingResult(ImageGroupingType.START_PAGE_COUNT);
            // 🔍 按优先级查找首页号字段
            String startPageField = getFieldIdByMultipleValues(fieldMapping,
                "首页号", "起始页号");
            String pageCountField = getFieldIdByValue(fieldMapping, "页数");
            result.setStartPageField(startPageField);
            result.setPageCountField(pageCountField);
            System.out.println("📋 使用首页号字段: " + startPageField);
            System.out.println("📋 使用页数字段: " + pageCountField);
        } else {
            System.out.println("🎯 未检测到分件相关字段，使用单件处理模式");
            result = new ImageGroupingResult(ImageGroupingType.SINGLE_PIECE);
        }

        result.setFieldMapping(fieldMapping);
        return result;
    }

    /**
     * 检查字段映射中是否包含指定的字段值
     */
    private boolean containsFieldValue(Map<String, String> fieldMapping, String targetFieldName) {
        return fieldMapping.values().stream()
                .anyMatch(fieldName -> fieldName != null && fieldName.contains(targetFieldName));
    }

    /**
     * 根据字段值反向查找字段ID
     */
    private String getFieldIdByValue(Map<String, String> fieldMapping, String targetFieldName) {
        return fieldMapping.entrySet().stream()
                .filter(entry -> entry.getValue() != null && entry.getValue().contains(targetFieldName))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据多个字段值按优先级查找字段ID
     * 按传入参数的顺序进行优先级匹配
     */
    private String getFieldIdByMultipleValues(Map<String, String> fieldMapping, String... targetFieldNames) {
        for (String targetFieldName : targetFieldNames) {
            String fieldId = getFieldIdByValue(fieldMapping, targetFieldName);
            if (fieldId != null) {
                System.out.println(String.format("🔍 找到字段匹配: %s -> %s", targetFieldName, fieldId));
                return fieldId;
            }
        }
        System.out.println("❌ 未找到匹配的字段: " + String.join(", ", targetFieldNames));
        return null;
    }

    /**
     * 在内存中处理图像分件（不存数据库）
     */
    private List<ProjectTaskImageDataDTO> processImageGroupingInMemory(
            List<ProjectTaskFormDataDTO> formDataList,
            List<ProjectTaskImageDataDTO> imageDataList,
            ProjectTaskConfigDTO taskConfigDTO,
            ImageGroupingResult groupingResult) {

        System.out.println("🚀🚀🚀 === 开始内存图像分件处理 === 🚀🚀🚀");
        System.out.println("📋 分件类型: " + groupingResult.getGroupingType());
        System.out.println("📋 Excel条目数量: " + formDataList.size());
        System.out.println("📋 图像数据数量: " + imageDataList.size());

        try {
            // 🔍 根据分件类型处理
            switch (groupingResult.getGroupingType()) {
                case START_END_PAGE:
                    return processStartEndPageGrouping(formDataList, imageDataList, groupingResult);
                case START_PAGE_COUNT:
                    return processStartPageCountGrouping(formDataList, imageDataList, groupingResult);
                case PAGE_RANGE:
                    return processPageRangeGrouping(formDataList, imageDataList, groupingResult);
                default:
                    System.out.println("🔍 单件处理模式，返回原始图像数据");
                    return imageDataList;
            }
        } catch (Exception e) {
            System.err.println("❌ 图像分件处理异常: " + e.getMessage());
            e.printStackTrace();
            System.out.println("🔄 回退到原始图像数据");
            return imageDataList;
        }
    }

    /**
     * 处理首页号+尾页号分件模式
     */
    private List<ProjectTaskImageDataDTO> processStartEndPageGrouping(
            List<ProjectTaskFormDataDTO> formDataList,
            List<ProjectTaskImageDataDTO> imageDataList,
            ImageGroupingResult groupingResult) {

        System.out.println("📄 处理首页号+尾页号分件模式");

        String startPageField = groupingResult.getStartPageField();
        String endPageField = groupingResult.getEndPageField();

        System.out.println("📋 首页号字段ID: " + startPageField);
        System.out.println("📋 尾页号字段ID: " + endPageField);

        if (startPageField == null || endPageField == null) {
            System.err.println("❌ 首页号或尾页号字段ID为空，无法进行分件处理");
            return imageDataList;
        }

        List<ProjectTaskImageDataDTO> result = new ArrayList<>();

        // 🔍 遍历Excel数据，为每个条目创建对应的图像组
        for (ProjectTaskFormDataDTO formData : formDataList) {
            try {
                // 🔍 解析Excel中的首页号和尾页号
                String startPageStr = getFieldValueFromFormData(formData, startPageField);
                String endPageStr = getFieldValueFromFormData(formData, endPageField);

                System.out.println(String.format("📋 Excel行%d: 首页号=%s, 尾页号=%s",
                    formData.getRowNum(), startPageStr, endPageStr));

                if (startPageStr != null && endPageStr != null) {
                    try {
                        int startPage = Integer.parseInt(startPageStr.trim());
                        int endPage = Integer.parseInt(endPageStr.trim());

                        if (startPage > 0 && endPage >= startPage) {
                            // 🔍 为这个页号范围创建图像数据组
                            ProjectTaskImageDataDTO imageGroup = createImageGroupForPageRange(
                                formData, imageDataList, startPage, endPage);

                            if (imageGroup != null) {
                                result.add(imageGroup);
                                System.out.println(String.format("✅ 创建图像组: 行%d, 页号%d-%d, 共%d页",
                                    formData.getRowNum(), startPage, endPage, (endPage - startPage + 1)));
                            }
                        } else {
                            System.err.println(String.format("❌ Excel行%d页号范围无效: %d-%d",
                                formData.getRowNum(), startPage, endPage));
                        }
                    } catch (NumberFormatException e) {
                        System.err.println(String.format("❌ Excel行%d页号解析失败: 首页号=%s, 尾页号=%s",
                            formData.getRowNum(), startPageStr, endPageStr));
                    }
                } else {
                    System.err.println(String.format("❌ Excel行%d页号字段为空: 首页号=%s, 尾页号=%s",
                        formData.getRowNum(), startPageStr, endPageStr));
                }
            } catch (Exception e) {
                System.err.println(String.format("❌ Excel行%d处理异常: %s", formData.getRowNum(), e.getMessage()));
            }
        }

        System.out.println(String.format("📊 首页号+尾页号分件完成: 输入%d个Excel条目，输出%d个图像组",
            formDataList.size(), result.size()));

        return result.isEmpty() ? imageDataList : result;
    }

    /**
     * 处理首页号+页数分件模式
     */
    private List<ProjectTaskImageDataDTO> processStartPageCountGrouping(
            List<ProjectTaskFormDataDTO> formDataList,
            List<ProjectTaskImageDataDTO> imageDataList,
            ImageGroupingResult groupingResult) {

        System.out.println("📄 处理首页号+页数分件模式");

        // 🔍 这里实现具体的分件逻辑
        // 暂时返回原始数据，后续可以根据实际需求完善
        System.out.println("⚠️ 首页号+页数分件逻辑待完善，暂时返回原始数据");
        return imageDataList;
    }

    /**
     * 处理起止页号分件模式
     */
    private List<ProjectTaskImageDataDTO> processPageRangeGrouping(
            List<ProjectTaskFormDataDTO> formDataList,
            List<ProjectTaskImageDataDTO> imageDataList,
            ImageGroupingResult groupingResult) {

        System.out.println("📄 处理起止页号分件模式");

        // 🔍 这里实现具体的分件逻辑
        // 暂时返回原始数据，后续可以根据实际需求完善
        System.out.println("⚠️ 起止页号分件逻辑待完善，暂时返回原始数据");
        return imageDataList;
    }

    /**
     * 从Excel表单数据中获取指定字段的值
     */
    private String getFieldValueFromFormData(ProjectTaskFormDataDTO formData, String fieldId) {
        try {
            if (formData.getTaskJson() != null) {
                // 🔍 解析JSON数据
                Map<String, Object> taskJson = JsonHelper.fromJson(formData.getTaskJson(), Map.class);
                if (taskJson != null && taskJson.containsKey(fieldId)) {
                    Object value = taskJson.get(fieldId);
                    return value != null ? value.toString() : null;
                }
            }
        } catch (Exception e) {
            System.err.println(String.format("❌ 解析Excel行%d字段%s失败: %s",
                formData.getRowNum(), fieldId, e.getMessage()));
        }
        return null;
    }

    /**
     * 为指定页号范围创建图像数据组
     */
    private ProjectTaskImageDataDTO createImageGroupForPageRange(
            ProjectTaskFormDataDTO formData,
            List<ProjectTaskImageDataDTO> imageDataList,
            int startPage, int endPage) {

        try {
            // 🔍 根据dataKey查找对应的原始图像数据
            ProjectTaskImageDataDTO matchingImage = null;
            for (ProjectTaskImageDataDTO imageData : imageDataList) {
                if (formData.getDataKey().equals(imageData.getDataKey())) {
                    matchingImage = imageData;
                    break;
                }
            }

            // 🔍 如果没找到匹配的图像，使用第一个作为模板
            ProjectTaskImageDataDTO template = matchingImage != null ? matchingImage : imageDataList.get(0);

            if (template != null) {
                ProjectTaskImageDataDTO imageGroup = new ProjectTaskImageDataDTO();
                imageGroup.setTaskId(template.getTaskId());
                imageGroup.setTaskConfigId(template.getTaskConfigId());
                imageGroup.setDataKey(formData.getDataKey());
                imageGroup.setPartNumber(formData.getPartNumber());

                // 🔍 使用匹配的图像路径，如果没有匹配则构建路径
                String imagePath;
                if (matchingImage != null) {
                    imagePath = matchingImage.getImageFilePath();
                    System.out.println(String.format("✅ 找到匹配图像: dataKey=%s -> path=%s",
                        formData.getDataKey(), imagePath));
                } else {
                    // 🔍 根据dataKey构建图像路径
                    String basePath = template.getImageFilePath();
                    // 提取基础路径并替换最后的dataKey部分
                    String[] pathParts = formData.getDataKey().split("-");
                    if (pathParts.length >= 3) {
                        String newPath = basePath.replaceAll("\\\\[^\\\\]+\\\\[^\\\\]+\\\\[^\\\\]+$",
                            "\\\\" + pathParts[0] + "\\\\" + pathParts[1] + "\\\\" + pathParts[2]);
                        imagePath = newPath;
                        System.out.println(String.format("🔧 构建图像路径: dataKey=%s -> path=%s",
                            formData.getDataKey(), imagePath));
                    } else {
                        imagePath = template.getImageFilePath();
                        System.out.println(String.format("⚠️ 无法构建路径，使用模板: dataKey=%s -> path=%s",
                            formData.getDataKey(), imagePath));
                    }
                }

                imageGroup.setImageFilePath(imagePath);
                imageGroup.setImageCount(endPage - startPage + 1);
                imageGroup.setCreateTime(template.getCreateTime());

                // 🔍 构建页号范围的图像名称列表
                List<String> imageNames = new ArrayList<>();
                for (int page = startPage; page <= endPage; page++) {
                    imageNames.add(String.format("page_%d.jpg", page));
                }
                imageGroup.setImageNames(imageNames);

                System.out.println(String.format("📋 创建图像组: dataKey=%s, 页号%d-%d, 图像数量=%d, 路径=%s",
                    formData.getDataKey(), startPage, endPage, imageGroup.getImageCount(), imagePath));

                return imageGroup;
            }
        } catch (Exception e) {
            System.err.println(String.format("❌ 创建图像组失败: dataKey=%s, 页号%d-%d, 错误=%s",
                formData.getDataKey(), startPage, endPage, e.getMessage()));
            e.printStackTrace();
        }

        return null;
    }
}
