package com.deeppaas.web;


import com.deeppaas.ExactnessConfig;
import com.deeppaas.common.model.RestModel;
import com.deeppaas.work.WorkService;
import com.yh.scofd.agent.ConvertException;
import com.yh.scofd.agent.HTTPAgent;
import com.yh.scofd.agent.wrapper.PackException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/exactness/work")
public class WorkApi {

    @Autowired
    private WorkService workService;
    /**
     * 启动
     * */
    @GetMapping("/start")
    public RestModel start(String taskId) {
        workService.start(taskId);
        return RestModel.success("正在质检中，请稍候...");
    }



    /**
     * 终止
     * */
    @GetMapping("/stop")
    public RestModel stop(String taskId) {
        workService.stop(taskId);
        return RestModel.success("已终止！");
    }

    /**
     * 暂停
     * */
    @GetMapping("/suspend")
    public RestModel suspend(String taskId) {
        workService.suspend(taskId);
        return RestModel.success("已暂停！");
    }
    /**
     * 继续
     * */
    @GetMapping("/continue")
    public RestModel continueTask(String taskId) {
        workService.continueTask(taskId);
        return RestModel.success("继续！");
    }

}
