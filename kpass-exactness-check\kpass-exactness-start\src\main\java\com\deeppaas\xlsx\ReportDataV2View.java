package com.deeppaas.xlsx;

import com.deeppaas.FileEnum;
import com.deeppaas.KeyValueDTO;
import com.deeppaas.common.helper.*;
import com.deeppaas.common.web.view.BaseXlsxView;
import com.deeppaas.result.entity.TaskErrorResultDO;
import com.deeppaas.result.enums.ErrorResultType;
import com.deeppaas.rule.enums.RuleImageEnums;
import com.deeppaas.task.config.dto.ProjectTaskConfigDTO;
import com.deeppaas.task.data.dto.ProjectTaskImageDataDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class ReportDataV2View extends BaseXlsxView {
    @Override
    protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fileName = "导出报告_" + DateHelper.getNowDateStr() + ".xlsx";
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        // SheetName
        List<TaskErrorResultDO> list = (List<TaskErrorResultDO>) model.get("list");
        List<ProjectTaskConfigDTO> taskConfigDTOs = (List<ProjectTaskConfigDTO>) model.get("taskConfigDTO");


        List<ProjectTaskImageDataDTO> imageData = (List<ProjectTaskImageDataDTO>) model.get("imageData");

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        for (int i = 0; i < taskConfigDTOs.size(); i++) {
            ProjectTaskConfigDTO taskConfigDTO = taskConfigDTOs.get(i);
            List<TaskErrorResultDO> taskError = list.stream().filter(item -> Objects.equals(item.getTaskConfigId(), taskConfigDTO.getId())).collect(Collectors.toList());
            if (Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.A.getNum()) || Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.B.getNum())) {
                buildFile(taskConfigDTO, taskError, workbook, cellStyle);
                buildFileMate(taskConfigDTO, taskError, workbook);
            }
            if (Objects.equals(taskConfigDTO.getRuleConfigType(), FileEnum.C.getNum())) {
                buildImage(taskConfigDTO, taskError, workbook, imageData);
                buildImageDetails(taskConfigDTO, taskError, workbook, imageData);
            }

        }


    }

    private void buildImageDetails(ProjectTaskConfigDTO taskConfigDTO, List<TaskErrorResultDO> taskError, Workbook workbook, List<ProjectTaskImageDataDTO> imageData) {

        Sheet sheet = workbook.createSheet("图像错误详情" + "-" + taskConfigDTO.getId());
        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue("序号");
        cell = row.createCell(1);
        cell.setCellValue("文件夹位置");
        cell = row.createCell(2);
        cell.setCellValue("分类");
        cell = row.createCell(3);
        cell.setCellValue("错误类型");
        cell = row.createCell(4);
        cell.setCellValue("错误值");
        for (int i = 0; i < taskError.size(); i++) {
            row = sheet.createRow(i + 1);
            cell = row.createCell(0);
            cell.setCellValue(i+1);
            TaskErrorResultDO taskErrorResultDO = taskError.get(i);
            cell = row.createCell(1);
            cell.setCellValue(taskErrorResultDO.getDataKey());
            cell = row.createCell(2);
            cell.setCellValue(taskErrorResultDO.getRuleType());
            cell = row.createCell(3);
            cell.setCellValue(taskErrorResultDO.getRuleName());
            cell = row.createCell(4);
            cell.setCellValue(taskErrorResultDO.getErrorFileValue());
        }

    }

    private void buildImage(ProjectTaskConfigDTO taskConfigDTO, List<TaskErrorResultDO> taskError, Workbook workbook, List<ProjectTaskImageDataDTO> imageData) {
        int imageCount = imageData.stream().mapToInt(ProjectTaskImageDataDTO::getImageCount).sum();
        Sheet sheet = workbook.createSheet("图像检测报告");
        Row row = sheet.createRow(0);
        Cell cell = row.createCell(0);
        cell.setCellValue("分类");
        cell = row.createCell(1);
        cell.setCellValue("错误类型/统计项");
        cell = row.createCell(2);
        cell.setCellValue("数量");
        Map<String, List<TaskErrorResultDO>> taskErrorMap = taskError.stream().collect(Collectors.groupingBy(TaskErrorResultDO::getRuleName));
        int i = 1;
        for (String key : taskErrorMap.keySet()) {
            RuleImageEnums enums = RuleImageEnums.find(key);
            List<TaskErrorResultDO> lists = taskErrorMap.get(key);
            row = sheet.createRow(i++);
            cell = row.createCell(0);
            cell.setCellValue(enums != null ? enums.getTypeName() : "");
            cell = row.createCell(1);
            cell.setCellValue(key);
            cell = row.createCell(2);
            cell.setCellValue(lists.size());

        }
        row = sheet.createRow(i++);
        cell = row.createCell(1);
        cell.setCellValue("错误率");
        cell = row.createCell(2);

        BigDecimal a = new BigDecimal(Double.valueOf(taskError.stream().map(TaskErrorResultDO::getFieldName).collect(Collectors.toSet()).size()).toString());
        BigDecimal b = new BigDecimal(Double.valueOf(imageCount).toString());

        cell.setCellValue(a.divide(b, 2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue()+"%");

        row = sheet.createRow(i++);
        cell = row.createCell(1);
        cell.setCellValue("图片总数");
        cell = row.createCell(2);
        cell.setCellValue(imageCount);
    }



    private void buildFileMate(ProjectTaskConfigDTO taskConfigDTO, List<TaskErrorResultDO> list, Workbook workbook) {
        String filePath = taskConfigDTO.getFilePath();
        File file = new File(filePath);
        ExcelReader excelReader = ExcelReader.of(file);
        //总条数
        List<String[]> datas = excelReader.getData(taskConfigDTO.getSheetName(), 1);
        String sheetName = FileHelper.getFileName(file);
        Sheet sheet = workbook.createSheet(sheetName + "-" + "汇总表");
        List<TaskErrorResultDO> ruleErrorLists = list.stream().filter(item -> Objects.equals(item.getErrorType(), ErrorResultType.FILE.getNum())).collect(Collectors.toList());

        Row row = sheet.createRow(0);
        Cell cell = row.createCell(1);

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cell.setCellValue("规则错误汇总");
        cell.setCellStyle(cellStyle);
        //第二行
        row = sheet.createRow(2);
        cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GOLD.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cell = row.createCell(1);
        cell.setCellValue("结构");
        cell.setCellStyle(cellStyle);
        //第三行
        cell = row.createCell(3);
        cell.setCellValue("格式");
        cell.setCellStyle(cellStyle);
        cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        row = sheet.createRow(3);
        cell = row.createCell(1);
        cell.setCellValue("项");
        cell.setCellStyle(cellStyle);
        cell = row.createCell(2);
        cell.setCellValue("缺失");
        cell.setCellStyle(cellStyle);
        cell = row.createCell(3);
        cell.setCellValue("数量");
        cell.setCellStyle(cellStyle);
        cell = row.createCell(4);
        cell.setCellValue("错误率");
        cell.setCellStyle(cellStyle);
        int i = 4;
        Map<String, List<TaskErrorResultDO>> ruleMap = ruleErrorLists.stream().collect(Collectors.groupingBy(TaskErrorResultDO::getFieldName));

        CellRangeAddress region = new CellRangeAddress(0, ruleMap.keySet().size() + 50, 0, 0);
        sheet.addMergedRegion(region);
        for (String key : ruleMap.keySet()) {
            List<TaskErrorResultDO> value = ruleMap.get(key);
            row = sheet.createRow(i++);
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(key);
            Cell cell2 = row.createCell(3);
            int valueCount=value.stream().map(TaskErrorResultDO::getErrorRow).collect(Collectors.toSet()).size();
            cell2.setCellValue(valueCount);
            BigDecimal a = new BigDecimal(Double.valueOf(valueCount).toString());
            BigDecimal b = new BigDecimal(Double.valueOf(datas.size()).toString());
            Cell cell3 = row.createCell(4);
            cell3.setCellValue(a.divide(b,2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue()+"%");
        }
        row = sheet.createRow(ruleMap.keySet().size() + 5);
        Cell cell1 = row.createCell(1);
        cell1.setCellValue("错误总条数");
        cell1 = row.createCell(3);
        // 🔍 修复：错误总条数应该是所有错误的总和，而不是去重后的行数
        cell1.setCellValue(ruleErrorLists.size());
//        cell1 = row.createCell(4);
//        BigDecimal a = new BigDecimal(Double.valueOf(ruleErrorLists.size()).toString());
//        BigDecimal b = new BigDecimal(Double.valueOf(datas.size()).toString());
//        cell1.setCellValue((a.divide(b, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue()+"%"));

        //合并
        style(sheet);
        region = new CellRangeAddress(0, ruleMap.keySet().size() + 50, 5, 6);
        sheet.addMergedRegion(region);
        region = new CellRangeAddress(ruleMap.keySet().size()+6, ruleMap.keySet().size() + 50, 1, 4);
        sheet.addMergedRegion(region);

        List<TaskErrorResultDO> logicErrorList = list.stream().filter(item -> Objects.equals(item.getErrorType(), ErrorResultType.RULE.getNum())).collect(Collectors.toList());

        // 🔍 判断是否包含档案要素检查，如果是则需要调整总条目数计算
        boolean hasArchiveElements = logicErrorList.stream()
            .anyMatch(error -> "archiveElements".equals(error.getRuleType()));

        int totalItems = datas.size(); // 默认使用Excel行数

        if (hasArchiveElements) {
            // 🔍 档案要素检查：需要根据用户选择的要素数量调整总条目数
            // 🔍 修复：应该获取用户实际选中的要素总数，而不是只统计有错误的要素种类数

            // 🔍 临时解决方案：使用默认的4个要素
            // TODO: 后续可以考虑从Controller传递实际的选中要素数量
            long elementCount = 4; // 默认4个要素：题名、责任者、文号、成文日期

            totalItems = datas.size() * (int) elementCount;

            System.out.println(String.format("🔍 档案要素Excel导出错误率计算:"));
            System.out.println(String.format("  Excel行数: %d", datas.size()));
            System.out.println(String.format("  用户选中的要素总数(默认): %d", elementCount));
            System.out.println(String.format("  总条目数: %d", totalItems));
            System.out.println(String.format("  错误数量: %d", logicErrorList.size()));

            // 🔍 同时显示有错误的要素种类数用于对比
            long errorElementCount = logicErrorList.stream()
                .filter(error -> "archiveElements".equals(error.getRuleType()))
                .map(TaskErrorResultDO::getFieldName)
                .distinct()
                .count();
            System.out.println(String.format("  有错误的要素种类数: %d", errorElementCount));
        }

        buildLogic(taskConfigDTO, logicErrorList, workbook, sheet, totalItems);
    }

    private void buildLogic(ProjectTaskConfigDTO taskConfigDTO, List<TaskErrorResultDO> ruleErrorLists, Workbook workbook, Sheet sheet, int count) {
        // 🔍 添加调试日志
        System.out.println(String.format("🔍 buildLogic方法 - 传入的count参数: %d", count));
        System.out.println(String.format("🔍 buildLogic方法 - 错误记录总数: %d", ruleErrorLists.size()));

        int rowNum = 6;
        Row row = sheet.getRow(0);
        if (row == null)
            row = sheet.createRow(0);
        Cell cell = row.createCell(rowNum + 1);

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cell.setCellValue("逻辑错误汇总");
        cell.setCellStyle(cellStyle);
        //第2行
        row = sheet.getRow(2);
        if (row == null)
            row = sheet.createRow(2);
        cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cell = row.createCell(rowNum + 1);
        cell.setCellValue("项");
        cell.setCellStyle(cellStyle);
        cell = row.createCell(rowNum + 2);
        cell.setCellValue("数量");
        cell.setCellStyle(cellStyle);
        cell = row.createCell(rowNum + 3);
        cell.setCellValue("错误率");
        cell.setCellStyle(cellStyle);
        int i = 2;
        Map<String, List<TaskErrorResultDO>> ruleMap = ruleErrorLists.stream().collect(Collectors.groupingBy(TaskErrorResultDO::getRuleName));

        for (String key : ruleMap.keySet()) {
            List<TaskErrorResultDO> value = ruleMap.get(key);

         //   int countValue=value.stream().map(TaskErrorResultDO::getErrorRow).collect(Collectors.toSet()).size();

            i = i + 1;
            row = sheet.getRow(i);
            if (row == null)
                row = sheet.createRow(i);
            Cell cell1 = row.createCell(rowNum + 1);
            cell1.setCellValue(key);
            Cell cell2 = row.createCell(rowNum + 2);

            cell2.setCellValue(value.size());
            BigDecimal a = new BigDecimal(Double.valueOf(value.size()).toString());
            BigDecimal b = new BigDecimal(Double.valueOf(count).toString());

            // 🔍 添加调试日志
            double errorRate = a.divide(b,2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).doubleValue();
            System.out.println(String.format("🔍 规则[%s]错误率计算: %d / %d = %.2f%%", key, value.size(), count, errorRate));

            Cell cell3 = row.createCell(rowNum + 3);
            cell3.setCellValue((int)errorRate + "%");
        }
        row = sheet.getRow(ruleMap.keySet().size() + 4);
        if (row == null)
            row = sheet.createRow(ruleMap.keySet().size() + 4);
        Cell cell1 = row.createCell(rowNum + 1);
        cell1.setCellValue("错误总条数");
        cell1 = row.createCell(rowNum + 2);
        // 🔍 修复：错误总条数应该是所有错误的总和，而不是去重后的行数
        cell1.setCellValue(ruleErrorLists.size());
        cell1 = row.createCell(rowNum + 3);
//        BigDecimal a = new BigDecimal(Double.valueOf(ruleErrorLists.size()).toString());
//        BigDecimal b = new BigDecimal(Double.valueOf(count).toString());
//        cell1.setCellValue((a.divide(b, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).intValue()+"%"));

        //合并
        style2(sheet);
        CellRangeAddress region = new CellRangeAddress(0, ruleMap.keySet().size() + 50, 10, 50);
        sheet.addMergedRegion(region);
        region = new CellRangeAddress(ruleMap.keySet().size()+5, ruleMap.keySet().size() + 50, 7, 9);
        sheet.addMergedRegion(region);
    }

    private void buildFile(ProjectTaskConfigDTO taskConfigDTO, List<TaskErrorResultDO> taskError, Workbook workbook, CellStyle cellStyle) {
        String ruleMappingStr = taskConfigDTO.getRuleMapping();
        if (ruleMappingStr != null) {
            List<KeyValueDTO> keyValueDTOS = JsonHelper.readToLists(ruleMappingStr, KeyValueDTO.class);
            Map<String, String> keyValueMaps = keyValueDTOS.stream().collect(Collectors.toMap(KeyValueDTO::getKey, KeyValueDTO::getLabel));

            //找到错误数据
            //按照行分组 - 🔍 过滤掉errorRow为null的记录
            Map<Integer, List<TaskErrorResultDO>> taskErrorMap = taskError.stream()
                .filter(item -> item.getErrorRow() != null) // 过滤掉errorRow为null的记录
                .collect(Collectors.groupingBy(TaskErrorResultDO::getErrorRow));
            String filePath = taskConfigDTO.getFilePath();
            File file = new File(filePath);
            ExcelReader excelReader = ExcelReader.of(file);
            String sheetName = FileHelper.getFileName(file);

            Sheet sheet = workbook.createSheet(sheetName);
            Drawing drawing = sheet.createDrawingPatriarch();
            List<String> titles = excelReader.getHeaders(taskConfigDTO.getSheetName());
            List<String[]> datas = excelReader.getData(taskConfigDTO.getSheetName(), titles.size());
            int filedCount = datas.size();
            Row row = sheet.createRow(0);
            for (int j = 0; j < titles.size(); j++) {
                row.createCell(j).setCellValue(titles.get(j));
            }
            //填充非逻辑类规则
            List<TaskErrorResultDO> logicTaskError = taskError.stream().filter(item -> Objects.equals(item.getErrorType(), ErrorResultType.RULE.getNum())).collect(Collectors.toList());
            List<String> logicRules = new ArrayList<>(logicTaskError.stream().map(TaskErrorResultDO::getRuleName).collect(Collectors.toSet()));
            for (int j = 0; j < logicRules.size(); j++) {
                Cell title = row.createCell(j + titles.size());
                title.setCellValue(logicRules.get(j));
                cellStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                title.setCellStyle(cellStyle);
            }
            //错误规则列
            System.out.println("🔍 Excel导出 - 开始处理错误规则列，总行数: " + filedCount);
            System.out.println("🔍 Excel导出 - taskErrorMap内容: " + taskErrorMap.keySet());

            for (int j = 0; j < filedCount; j++) {
                String[] strings = datas.get(j);
                row = sheet.createRow(j + 1);
                int excelRowNum = j + 2; // Excel行号（从2开始，因为第1行是标题）
                List<TaskErrorResultDO> rowTaskError = taskErrorMap.get(excelRowNum);

                System.out.println(String.format("🔍 Excel导出 - 处理第%d行数据，Excel行号: %d，错误数量: %d",
                    j, excelRowNum, rowTaskError != null ? rowTaskError.size() : 0));

                for (int k = 0; k < strings.length; k++) {
                    String titleName = titles.get(k);
                    System.out.println(String.format("🔍 Excel导出 - 处理列: %s，行号: %d", titleName, excelRowNum));

                    // 🔍 处理文件错误（原有逻辑）
                    List<TaskErrorResultDO> fileErrors = build(keyValueMaps, titleName, rowTaskError);
                    // 🔍 处理档案要素错误（新增逻辑）
                    List<TaskErrorResultDO> archiveElementErrors = buildArchiveElementErrors(keyValueMaps, titleName, rowTaskError);

                    // 🔍 合并所有错误
                    List<TaskErrorResultDO> allErrors = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(fileErrors)) {
                        allErrors.addAll(fileErrors);
                    }
                    if (!CollectionUtils.isEmpty(archiveElementErrors)) {
                        allErrors.addAll(archiveElementErrors);
                    }

                    Cell cell = row.createCell(k);
                    cell.setCellValue(strings[k]);
                    if (!CollectionUtils.isEmpty(allErrors)) {
                        cellStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
                        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, k, j + 1, k + 1, j + 2));

                        // 🔍 构建批注内容 - 区分文件错误和档案要素错误
                        StringBuilder commentText = new StringBuilder();

                        // 添加文件错误
                        if (!CollectionUtils.isEmpty(fileErrors)) {
                            commentText.append(fileErrors.stream().map(TaskErrorResultDO::getRuleName).collect(Collectors.joining(",")));
                        }

                        // 添加档案要素错误（格式：规则名称：换行 字段名不匹配：换行 提取值[xxx] 换行 相似度：相似度值）
                        if (!CollectionUtils.isEmpty(archiveElementErrors)) {
                            if (commentText.length() > 0) {
                                commentText.append("\n");
                            }
                            for (TaskErrorResultDO archiveError : archiveElementErrors) {
                                // 解析errorFileValue获取详细信息
                                String errorFileValue = archiveError.getErrorFileValue();
                                String fieldName = archiveError.getFieldName();

                                // 解析提取值和相似度
                                String extractedValue = "";
                                String similarity = "0.00";

                                if (errorFileValue != null) {
                                    // 从errorFileValue中提取信息
                                    // 格式：题名不匹配：Excel值[xxx] vs 提取值[xxx]，相似度0.00
                                    if (errorFileValue.contains("提取值[") && errorFileValue.contains("]")) {
                                        int startIdx = errorFileValue.indexOf("提取值[") + 4;
                                        int endIdx = errorFileValue.indexOf("]", startIdx);
                                        if (endIdx > startIdx) {
                                            extractedValue = errorFileValue.substring(startIdx, endIdx);
                                        }
                                    }

                                    if (errorFileValue.contains("相似度")) {
                                        int simIdx = errorFileValue.indexOf("相似度") + 3;
                                        String simStr = errorFileValue.substring(simIdx).trim();
                                        if (simStr.length() > 0) {
                                            similarity = simStr;
                                        }
                                    }
                                }

                                // 按用户要求的格式构建批注
                                commentText.append(archiveError.getRuleName()).append(":\n");
                                commentText.append(fieldName).append("不匹配：\n");
                                commentText.append("提取值[").append(extractedValue).append("]\n");
                                commentText.append("相似度").append(similarity).append("\n");
                            }
                        }

                        comment.setString(new XSSFRichTextString(commentText.toString()));
                        cell.setCellStyle(cellStyle);
                        cell.setCellComment(comment);
                    }

                }
                //加入非逻辑类数据
                for (int k = 0; k < logicRules.size(); k++) {
                    String ruleName = logicRules.get(k);
                    if (rowTaskError != null) {
                        TaskErrorResultDO taskErrorResultDO = rowTaskError.stream().filter(item -> Objects.equals(ruleName, item.getRuleName())).findFirst().orElse(null);
                        if (taskErrorResultDO != null) {
                            Cell cell = row.createCell(k + titles.size());
                            cell.setCellValue(taskErrorResultDO.getErrorFileValue());
                            cellStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
                            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            cell.setCellStyle(cellStyle);
                        }

                    }
                }


            }

        }

    }


    private List<TaskErrorResultDO> build(Map<String, String> keyValueMaps, String titleName, List<TaskErrorResultDO> taskError) {
        if (CollectionUtils.isEmpty(taskError))
            return null;
        //找到标准字段名称
        String name = keyValueMaps.get(titleName);
        if (StringHelper.isEmpty(name)) {
            return null;
        }
        return taskError.stream().filter(item -> Objects.equals(item.getFieldName(), name) && Objects.equals(item.getErrorType(), ErrorResultType.FILE.getNum())).collect(Collectors.toList());

    }

    /**
     * 构建档案要素错误列表 - 专门处理档案要素检查错误
     */
    private List<TaskErrorResultDO> buildArchiveElementErrors(Map<String, String> keyValueMaps, String titleName, List<TaskErrorResultDO> taskError) {
        if (CollectionUtils.isEmpty(taskError))
            return null;

        System.out.println(String.format("🔍 Excel导出字段匹配 - titleName: %s", titleName));
        System.out.println(String.format("🔍 keyValueMaps内容: %s", keyValueMaps));

        // 通过keyValueMaps找到对应的中文字段名
        String chineseFieldName = keyValueMaps.get(titleName);
        System.out.println(String.format("🔍 keyValueMaps查找结果 - titleName: %s -> chineseFieldName: %s", titleName, chineseFieldName));

        if (StringHelper.isEmpty(chineseFieldName)) {
            System.out.println(String.format("❌ chineseFieldName为空，跳过字段: %s", titleName));
            return null;
        }

        // 🔍 查找档案要素错误 - 匹配中文字段名和RULE类型
        System.out.println(String.format("🔍 开始匹配档案要素错误 - 目标字段名: %s", chineseFieldName));
        System.out.println(String.format("🔍 当前行所有错误记录数量: %d", taskError != null ? taskError.size() : 0));

        if (taskError != null) {
            for (TaskErrorResultDO error : taskError) {
                System.out.println(String.format("🔍 错误记录详情 - fieldName: %s, errorType: %d, ruleType: %s, ruleName: %s",
                    error.getFieldName(), error.getErrorType(), error.getRuleType(), error.getRuleName()));
            }
        }

        List<TaskErrorResultDO> matchingErrors = new ArrayList<>();
        if (taskError != null) {
            matchingErrors = taskError.stream()
                .filter(item -> {
                    boolean fieldMatch = Objects.equals(item.getFieldName(), chineseFieldName);
                    boolean typeMatch = Objects.equals(item.getErrorType(), ErrorResultType.RULE.getNum());
                    boolean ruleMatch = "archiveElements".equals(item.getRuleType());

                    System.out.println(String.format("🔍 错误匹配检查 - fieldName: %s, errorType: %s, ruleType: %s | 匹配结果: field=%s, type=%s, rule=%s",
                        item.getFieldName(), item.getErrorType(), item.getRuleType(), fieldMatch, typeMatch, ruleMatch));

                    return fieldMatch && typeMatch && ruleMatch;
                })
                .collect(Collectors.toList());
        }

        System.out.println(String.format("🔍 找到匹配的档案要素错误数量: %d", matchingErrors.size()));
        return matchingErrors;
    }


    private void style(Sheet sheet) {
        CellRangeAddress region = new CellRangeAddress(0, 1, 1, 4);
        sheet.addMergedRegion(region);
        region = new CellRangeAddress(2, 2, 1, 2);
        sheet.addMergedRegion(region);

        region = new CellRangeAddress(2, 2, 3, 4);
        sheet.addMergedRegion(region);


    }

    private void style2(Sheet sheet) {
        CellRangeAddress region = new CellRangeAddress(0, 1, 7, 9);
        sheet.addMergedRegion(region);

    }



}
