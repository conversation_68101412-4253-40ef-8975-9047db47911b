"""
信息提取API - 对外提供的主要接口
"""
import os
import time
import yaml
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from ..core.device_manager import DeviceManager
from ..core.model_manager import ModelManager
from ..core.pipeline_manager import PipelineManager
from ..core.config_manager import ConfigManager
from ..core.model_pool import get_model_pool

class IntelligentExtractionAPI:
    """智能信息提取API"""
    
    def __init__(self, config_path: Optional[str] = None, **kwargs):
        """
        初始化API
        
        Args:
            config_path: 配置文件路径
            **kwargs: 额外配置参数
        """
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config_manager = ConfigManager(config_path, **kwargs)
        self.config = self.config_manager.get_config()
        
        # 初始化核心组件
        self.device_manager = None
        self.model_manager = None
        self.pipeline_manager = None

        # 使用全局模型池
        self.model_pool = get_model_pool(config_path)

        self._initialized = False
        
        # 如果配置了自动初始化，则立即初始化
        if self.config.get('auto_initialize', True):
            self.initialize()
    
    def initialize(self):
        """初始化所有组件"""
        if self._initialized:
            self.logger.warning("API已经初始化，跳过重复初始化")
            return
        
        try:
            self.logger.info("开始初始化智能信息提取API...")
            
            # 初始化设备管理器
            self.device_manager = DeviceManager(self.config.get('device', {}))
            
            # 初始化模型管理器
            self.model_manager = ModelManager(
                self.device_manager,
                self.config  # 传递完整配置，让ModelManager自己提取models部分
            )
            
            # 初始化流水线管理器
            self.pipeline_manager = PipelineManager(
                self.device_manager,
                self.model_manager,
                self.config.get('pipeline', {})
            )
            
            self._initialized = True
            self.logger.info("智能信息提取API初始化完成")
            
        except Exception as e:
            self.logger.error(f"API初始化失败: {e}")
            raise
    
    def extract(self, 
                document_path: str, 
                key_list: List[str],
                sync: bool = True,
                options: Optional[Dict[str, Any]] = None) -> Union[Dict[str, Any], str]:
        """
        提取文档中的关键信息
        
        Args:
            document_path: 文档路径
            key_list: 要提取的关键信息列表
            sync: 是否同步执行
            options: 额外选项
            
        Returns:
            同步模式返回提取结果，异步模式返回任务ID
        """
        self._ensure_initialized()
        
        # 验证输入
        self._validate_inputs(document_path, key_list)
        
        if options is None:
            options = {}
        
        # 合并默认选项
        default_options = self.config.get('extraction', {}).get('default_options', {})
        options = {**default_options, **options}
        
        try:
            if sync:
                return self.pipeline_manager.extract_sync(document_path, key_list, options)
            else:
                return self.pipeline_manager.extract_async(document_path, key_list, options)
                
        except Exception as e:
            self.logger.error(f"信息提取失败: {e}")
            raise
    
    def extract_archive_elements(self,
                                document_path: str,
                                sync: bool = True,
                                custom_keys: Optional[List[str]] = None) -> Union[Dict[str, Any], str]:
        """
        提取档案要素（预定义的档案关键信息）
        
        Args:
            document_path: 档案文档路径
            sync: 是否同步执行
            custom_keys: 自定义要素列表，如果不提供则使用默认档案要素
            
        Returns:
            同步模式返回提取结果，异步模式返回任务ID
        """
        # 默认档案要素
        default_archive_keys = ["题名", "责任者", "文号", "成文日期"]
        
        key_list = custom_keys if custom_keys else default_archive_keys
        
        # 档案专用选项
        archive_options = {
            'domain': 'archive',
            'use_archive_rules': True,
            'enable_date_parsing': True,
            'enable_document_number_parsing': True
        }
        
        return self.extract(document_path, key_list, sync, archive_options)

    def extract_archive_folder(self,
                              folder_path: str,
                              sync: bool = True,
                              custom_keys: Optional[List[str]] = None) -> Union[Dict[str, Any], str]:
        """
        提取档案文件夹中的要素（多页档案统一提取）

        Args:
            folder_path: 档案文件夹路径
            sync: 是否同步执行
            custom_keys: 自定义要素列表，如果不提供则使用默认档案要素

        Returns:
            同步模式返回提取结果，异步模式返回任务ID
        """
        self._ensure_initialized()

        # 验证文件夹
        if not os.path.exists(folder_path):
            raise ValueError(f"文件夹不存在: {folder_path}")

        if not os.path.isdir(folder_path):
            raise ValueError(f"路径不是文件夹: {folder_path}")

        # 获取文件夹中的图片文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []

        for ext in image_extensions:
            image_files.extend(Path(folder_path).glob(f"*{ext}"))
            image_files.extend(Path(folder_path).glob(f"*{ext.upper()}"))

        if not image_files:
            raise ValueError(f"文件夹中未找到图片文件: {folder_path}")

        # 按文件名排序，确保处理顺序一致
        image_files.sort(key=lambda x: x.name)

        self.logger.info(f"档案文件夹包含 {len(image_files)} 个图片文件")

        # 默认档案要素
        default_archive_keys = ["题名", "责任者", "文号", "成文日期"]
        key_list = custom_keys if custom_keys else default_archive_keys

        # 档案专用选项
        archive_options = {
            'domain': 'archive',
            'use_archive_rules': True,
            'enable_date_parsing': True,
            'enable_document_number_parsing': True,
            'is_multi_page': True,  # 标记为多页档案
            'page_count': len(image_files)
        }

        if sync:
            # 同步处理多页档案
            start_time = time.time()

            try:
                # 使用第一个图片文件作为主文件，其他作为辅助页面
                main_image = str(image_files[0])
                auxiliary_images = [str(f) for f in image_files[1:]] if len(image_files) > 1 else []

                # 调用流水线管理器处理多页档案
                result = self.pipeline_manager.process_multi_page_archive(
                    main_image, auxiliary_images, key_list, archive_options
                )

                processing_time = time.time() - start_time

                return {
                    'results': result,
                    'processing_time': processing_time,
                    'page_count': len(image_files),
                    'processed_files': [f.name for f in image_files]
                }

            except Exception as e:
                self.logger.error(f"档案文件夹处理失败: {e}")
                raise
        else:
            # 异步处理
            task_id = self.pipeline_manager.submit_multi_page_archive_task(
                [str(f) for f in image_files], key_list, archive_options
            )
            return task_id

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取异步任务状态"""
        self._ensure_initialized()
        return self.pipeline_manager.get_task_status(task_id)
    
    def wait_for_result(self, task_id: str, timeout: Optional[float] = None) -> Dict[str, Any]:
        """等待异步任务结果"""
        self._ensure_initialized()
        return self.pipeline_manager.wait_for_result(task_id, timeout)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消异步任务"""
        self._ensure_initialized()
        return self.pipeline_manager.cancel_task(task_id)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        self._ensure_initialized()
        
        return {
            'initialized': self._initialized,
            'device_info': {
                'mode': self.device_manager.current_mode,
                'gpu_count': len(self.device_manager.available_gpus),
                'memory_usage': self.device_manager.monitor_memory_usage()
            },
            'model_status': self.model_manager.get_model_status(),
            'performance_stats': self.pipeline_manager.get_performance_stats()
        }
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文档格式"""
        return self.config.get('supported_formats', [
            '.jpg', '.jpeg', '.png', '.bmp', '.tiff',
            '.pdf', '.doc', '.docx'
        ])
    
    def validate_document(self, document_path: str) -> Dict[str, Any]:
        """验证文档是否可以处理"""
        if not os.path.exists(document_path):
            return {'valid': False, 'error': '文件不存在'}
        
        file_ext = Path(document_path).suffix.lower()
        supported_formats = self.get_supported_formats()
        
        if file_ext not in supported_formats:
            return {
                'valid': False, 
                'error': f'不支持的文件格式: {file_ext}',
                'supported_formats': supported_formats
            }
        
        # 检查文件大小
        max_file_size = self.config.get('max_file_size', 50 * 1024 * 1024)  # 50MB
        file_size = os.path.getsize(document_path)
        
        if file_size > max_file_size:
            return {
                'valid': False,
                'error': f'文件过大: {file_size / (1024*1024):.1f}MB, 最大支持: {max_file_size / (1024*1024):.1f}MB'
            }
        
        return {'valid': True, 'file_size': file_size, 'format': file_ext}
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config_manager.update_config(new_config)
        self.config = self.config_manager.get_config()
        self.logger.info("配置已更新")
    
    def reload_models(self):
        """重新加载模型"""
        self._ensure_initialized()
        
        self.logger.info("开始重新加载模型...")
        
        # 清理现有模型
        self.model_manager.cleanup()
        
        # 重新初始化模型管理器
        self.model_manager = ModelManager(
            self.device_manager,
            self.config  # 传递完整配置
        )
        
        # 更新流水线管理器的模型管理器引用
        self.pipeline_manager.model_manager = self.model_manager
        
        self.logger.info("模型重新加载完成")
    
    def cleanup(self):
        """清理所有资源"""
        if not self._initialized:
            return
        
        self.logger.info("开始清理API资源...")
        
        try:
            if self.pipeline_manager:
                self.pipeline_manager.cleanup()
            
            if self.model_manager:
                self.model_manager.cleanup()
            
            if self.device_manager:
                self.device_manager.clear_cache()
            
            self._initialized = False
            self.logger.info("API资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
    
    def _ensure_initialized(self):
        """确保API已初始化"""
        if not self._initialized:
            raise RuntimeError("API未初始化，请先调用 initialize() 方法")
    
    def _validate_inputs(self, document_path: str, key_list: List[str]):
        """验证输入参数"""
        # 验证文档
        validation_result = self.validate_document(document_path)
        if not validation_result['valid']:
            raise ValueError(f"文档验证失败: {validation_result['error']}")
        
        # 验证关键词列表
        if not key_list or not isinstance(key_list, list):
            raise ValueError("key_list必须是非空列表")
        
        if len(key_list) > 20:  # 限制单次提取的关键词数量
            raise ValueError("单次提取的关键词数量不能超过20个")
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self._initialized:
            self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass


# 便捷函数
def create_extraction_api(config_path: Optional[str] = None, **kwargs) -> IntelligentExtractionAPI:
    """创建信息提取API实例"""
    return IntelligentExtractionAPI(config_path, **kwargs)


def extract_archive_elements(document_path: str, 
                           custom_keys: Optional[List[str]] = None,
                           config_path: Optional[str] = None) -> Dict[str, Any]:
    """快速提取档案要素的便捷函数"""
    with create_extraction_api(config_path) as api:
        return api.extract_archive_elements(document_path, sync=True, custom_keys=custom_keys)
