import { Checkbox, DatePicker, Form, Input, Modal, Select, message, Card, Row, Col, Slider, Typography } from 'antd'
import React, { useEffect, useState, useCallback } from 'react'
import FormItem from 'antd/es/form/FormItem'
import './RuleAdd.css'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  useMutation,
  useQuery,
} from 'react-query'
import 'dayjs/locale/zh-cn'
import locale from 'antd/es/date-picker/locale/zh_CN'
import dayjs from 'dayjs'
import { getFieldPage } from '../../api/fieldLibrary'
import { extractResponse } from '../../api/util'
import { ruleSave } from '../../api/rule'
import { PageData } from '../../api/types'

type RuleAddType = {
  open: boolean
  close: () => void
  library: RuleMould
  refetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined
  ) => Promise<QueryObserverResult<PageData<RuleMouldConfig>, unknown>>
  activeRule: RuleMouldConfig | undefined
}

const ruleNameList = [
  {
    label: '空值检查',
    value: 'empty',
  },
  {
    label: '值域检查',
    value: 'valueRange',
  },
  {
    label: '正则检查',
    value: 'standard',
  },
  {
    label: '重复性检查',
    value: 'repeat',
  },
  {
    label: '比对检查',
    value: 'complete',
  },
  {
    label: '首页号检查',
    value: 'startNumber',
  },

  {
    label: '数量一致性检查',
    value: 'fieldImage',
  },
  {
    label: '起止页码检查',
    value: 'startPage',
  },
  {
    label: '身份证号准确性检查',
    value: 'idNumber',
  },

  {
    label: '日期准确性检查',
    value: 'date',
  },
  {
    label: '档案要素正确性检查',
    value: 'archiveElements',
  },
]

const codeType = ['empty', 'valueRange', 'standard']

const ruleNameShowMap = {
  empty: false,
  valueRange: true,
  standard: true,
  repeat: false,
  complete: false,
  startNumber: false,
  fieldImage: false,
  startPage: false,
  date: false,
  idNumber: false,
  archiveElements: true,
}

type RuleNameType =
  | 'empty'
  | 'valueRange'
  | 'standard'
  | 'repeat'
  | 'complete'
  | 'startNumber'
  | 'fieldImage'
  | 'startPage'
  | 'date'
  | 'idNumber'
  | 'archiveElements'

type ruleNameType = 'valueRange' | 'standard' | 'archiveElements'

const ruleNameMap = {
  valueRange: '标准域',
  standard: '检查公式',
  // complete: '检查公式',
  date: '选择日期',
  archiveElements: '档案要素配置',
}

const defaultValue = {
  ruleAliasName: '',
  ruleCode: 'empty',
  ruleDescribe: '是否为空',
  ruleValue: '',
  ruleFields: [],
}

export default function RuleAdd({
  open,
  close,
  library,
  refetch,
  activeRule,
}: RuleAddType) {
  const form = Form.useForm()[0]
  const [ruleName, setRuleName] = useState<RuleNameType>('empty')
  const [isAll, setIsAll] = useState(false)
  const { RangePicker } = DatePicker
  const { data } = useQuery(
    ['getFieldPage', library],

    extractResponse(() =>
      getFieldPage(1, 99999999, {
        search_EQ_libraryId: library.libraryId,
      })
    ),

    {
      onSuccess(res) {
        console.log(res?.list)
      },
    }
  )

  const ruleSaveMutation = useMutation(ruleSave, {
    onSuccess(res) {
      refetch()
      close()
      message.success(res.data.message)
    },
  })

  useEffect(() => {
    if (open) {
      if (activeRule) {
        let date
        if (activeRule.ruleCode === 'date') {
          try {
            const value = JSON.parse(activeRule.ruleValue || '[]')
            if (value && value.length >= 2) {
              date = [dayjs(value[0]), dayjs(value[1])]
            }
          } catch (e) {
            console.warn('Failed to parse date rule value:', activeRule.ruleValue)
          }
        }

        // 立即设置表单值，然后通知子组件
        form.setFieldsValue({
          ...activeRule,
          ruleValue:
            activeRule.ruleCode === 'date' ? date : activeRule.ruleValue,
        })
        console.log('🔍 设置表单值 - activeRule:', activeRule)
        console.log('🔍 设置表单值 - ruleValue:', activeRule.ruleCode === 'date' ? date : activeRule.ruleValue)

        // 确保表单值设置完成后再进行后续操作
        setTimeout(() => {
          console.log('🔍 表单值设置完成，触发更新')
        }, 10)

        setRuleName(activeRule?.ruleCode as RuleNameType)
      } else {
        setRuleName('empty')
        form.setFieldsValue(defaultValue)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, activeRule])

  // useEffect(() => {
  //   console.log(data?.list)
  //   if (!data?.list || !form.getFieldValue('ruleFields')) {
  //     return
  //   }

  // }, [data?.list, form.getFieldValue, form])

  function checkAllField(value: boolean) {
    const list: string[] = []
    if (value) {
      data?.list.forEach(item => {
        list.push(item.id)
      })
      form.setFieldValue('ruleFields', list)
    } else {
      form.setFieldValue('ruleFields', [])
    }
  }

  function submit() {
    let values: any = {}
    if (activeRule) {
      values = { ...activeRule, ...form.getFieldsValue() }
    } else {
      values = { ...form.getFieldsValue() }
    }

    values.ruleName = ruleNameList.find(
      rule => rule.value === values.ruleCode
    )?.label

    // 档案要素规则特殊处理
    if (values.ruleCode === 'archiveElements') {
      // 解析档案要素配置
      let archiveConfig = { selectedElements: [], fieldMapping: {} }
      try {
        archiveConfig = JSON.parse(values.ruleValue || '{}')
      } catch (e) {
        console.error('解析档案要素配置失败:', e)
      }

      // 设置字段ID列表和字段名称列表
      const fieldIds = Object.values(archiveConfig.fieldMapping || {}).filter(Boolean)
      const fieldNames: string[] = []

      fieldIds.forEach((fieldId: any) => {
        const field = data?.list?.find((item: FieldType) => item.id === fieldId)
        if (field) {
          fieldNames.push(field.fieldName)
        }
      })

      values.ruleFields = fieldIds
      values.ruleFieldsName = fieldNames.length > 0 ? fieldNames : ['档案要素检查']
    } else if (data) {
      const arr: string[] = []
      values.ruleFields.forEach((rule: string) => {
        const field = data.list.find((item: FieldType) => item.id === rule)
        if (field) {
          arr.push(field.fieldName)
        }
      })
      values.ruleFieldsName = arr
    }
    if (activeRule) {
      values.id = activeRule.id
    }
    values.libraryId = library.id
    values.ruleType = 3
    if (codeType.indexOf(values.ruleCode) > -1) {
      values.ruleType = 1
    }
    if (values.ruleValue && values.ruleCode === 'date') {
      values.ruleValue = JSON.stringify([
        dayjs(values.ruleValue[0]).format('YYYY-MM-DD'),
        dayjs(values.ruleValue[1]).format('YYYY-MM-DD'),
      ])
    }

    console.log(values)
    ruleSaveMutation.mutate(values)
  }
  return (
    <Modal
      title="新建规则"
      open={open}
      width={800}
      onCancel={() => {
        close()
      }}
      onOk={() => {
        form.submit()
      }}
    >
      <Form
        form={form}
        onFinish={submit}
        labelCol={{ span: ruleName === 'archiveElements' ? 6 : 10 }}
        style={{ width: ruleName === 'archiveElements' ? '100%' : '380px' }}
      >
        <FormItem
          label="规则名称"
          name="ruleAliasName"
          rules={[{ required: true, message: '请输入规则名称' }]}
        >
          <Input maxLength={40} placeholder="请输入规则名称" />
        </FormItem>
        <FormItem label="规则分类" name="ruleCode">
          <Select
            placeholder="请选择规则分类"
            options={ruleNameList}
            value={ruleName}
            onChange={e => {
              setRuleName(e)
              form.setFieldValue('ruleValue', '')
              if (e === 'empty') {
                form.setFieldValue('ruleDescribe', '是否为空')
              }
              if (e === 'repeat') {
                form.setFieldValue('ruleDescribe', '是否重复')
              }
              if (e === 'archiveElements') {
                form.setFieldValue('ruleDescribe', '档案要素正确性检查')
                // 设置默认的档案要素配置
                form.setFieldValue('ruleValue', JSON.stringify({
                  selectedElements: ['title', 'responsible_party', 'document_number', 'issue_date'],
                  fieldMapping: {},
                  confidence_threshold: 0.5,
                  similarity_threshold: 0.9
                }))
              }
            }}
          />
        </FormItem>
        {ruleName === 'empty' || ruleName === 'repeat' ? (
          <FormItem
            label="规则描述"
            name="ruleDescribe"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input disabled maxLength={40} />
          </FormItem>
        ) : null}
        {ruleNameShowMap[ruleName] ? (
          <FormItem
            label={ruleNameMap[ruleName as ruleNameType]}
            name="ruleValue"
          >
            <Input />
          </FormItem>
        ) : null}

        {ruleName === 'date' ? (
          <FormItem
            label={ruleNameMap[ruleName as ruleNameType]}
            name="ruleValue"
          >
            <RangePicker className="w-[300px]" locale={locale} />
          </FormItem>
        ) : null}

        {ruleName === 'archiveElements' ? (
          <ArchiveElementsConfig form={form} data={data} activeRule={activeRule} />
        ) : null}

        {ruleName !== 'archiveElements' ? (
          <FormItem label="参数设置" name="ruleFields">
            <Select
              options={data?.list}
              fieldNames={{ label: 'fieldName', value: 'id' }}
              placeholder="请选择字段"
              mode="multiple"
              // showSearch={true}
              filterOption={(inputValue, option) => {
                if (option && option.fieldName.indexOf(inputValue) === -1) {
                  return false
                } else {
                  return true
                }
              }}
              dropdownRender={menu => (
                <>
                  <div className="px-2 py-2">
                    <Checkbox
                      type="text"
                      checked={isAll}
                      onClick={() => {
                        setIsAll(!isAll)
                        checkAllField(!isAll)
                      }}
                    >
                      全选
                    </Checkbox>
                  </div>
                  {menu}
                </>
              )}
              onChange={e => {
                if (data?.list.length === e.length) {
                  setIsAll(true)
                } else {
                  setIsAll(false)
                }
              }}
            />
          </FormItem>
        ) : null}
      </Form>
    </Modal>
  )
}

// 档案要素配置组件
function ArchiveElementsConfig({ form, data, activeRule }: { form: any, data: any, activeRule?: RuleMouldConfig }) {
  const { Text } = Typography

  // 档案要素定义
  const archiveElements = [
    { key: 'title', label: '题名检查' },
    { key: 'responsible_party', label: '责任者检查' },
    { key: 'document_number', label: '文号检查' },
    { key: 'issue_date', label: '成文日期检查' }
  ]

  // 状态管理 - 初始状态为空，等待从表单加载
  const [selectedElements, setSelectedElements] = useState<string[]>([])
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({})
  const [usedFields, setUsedFields] = useState<string[]>([])
  const [isInitialized, setIsInitialized] = useState(false)

  // 重置状态的函数
  const resetState = useCallback(() => {
    setSelectedElements([])
    setFieldMapping({})
    setUsedFields([])
    setIsInitialized(false)
  }, [])

  // 获取可用字段列表（从字段库）
  const getAvailableFields = () => {
    if (!data?.list) return []
    return data.list.map((field: FieldType) => ({
      label: field.fieldName,
      value: field.id
    }))
  }

  // 获取未使用的字段选项
  const getUnusedFieldOptions = (currentElementKey?: string) => {
    const availableFields = getAvailableFields()
    const currentFieldId = fieldMapping[currentElementKey || '']

    return availableFields.filter(field =>
      !usedFields.includes(field.value) || field.value === currentFieldId
    )
  }

  // 更新配置并同步到表单
  const updateFormConfig = () => {
    const config = {
      selectedElements,
      fieldMapping,
      confidence_threshold: 0.5,
      similarity_threshold: 0.9
    }
    form.setFieldValue('ruleValue', JSON.stringify(config))
  }

  // 处理档案要素选择变化
  const handleElementsChange = (checkedValues: string[]) => {
    setSelectedElements(checkedValues)

    // 清理未选中要素的字段映射
    const newFieldMapping = { ...fieldMapping }
    Object.keys(newFieldMapping).forEach(elementKey => {
      if (!checkedValues.includes(elementKey)) {
        delete newFieldMapping[elementKey]
      }
    })
    setFieldMapping(newFieldMapping)

    // 更新已使用字段列表
    const newUsedFields = Object.values(newFieldMapping).filter(Boolean)
    setUsedFields(newUsedFields)
  }

  // 处理字段映射变化
  const handleFieldMappingChange = (elementKey: string, fieldId: string) => {
    const newFieldMapping = { ...fieldMapping }

    // 如果之前有映射，从已使用列表中移除
    if (newFieldMapping[elementKey]) {
      setUsedFields(prev => prev.filter(id => id !== newFieldMapping[elementKey]))
    }

    // 设置新映射
    newFieldMapping[elementKey] = fieldId
    setFieldMapping(newFieldMapping)

    // 更新已使用字段列表
    const newUsedFields = Object.values(newFieldMapping).filter(Boolean)
    setUsedFields(newUsedFields)
  }

  // 监听状态变化，同步到表单
  useEffect(() => {
    updateFormConfig()
  }, [selectedElements, fieldMapping])

  // 获取字段名称
  const getFieldName = (fieldId: string) => {
    const field = data?.list?.find((f: FieldType) => f.id === fieldId)
    return field ? field.fieldName : '未选择'
  }

  // 验证配置完整性
  const isConfigValid = () => {
    return selectedElements.length > 0 && selectedElements.every(key => fieldMapping[key])
  }

  // 初始化配置的函数
  const initializeConfig = useCallback(() => {
    console.log('🔍 初始化档案要素配置 - activeRule:', activeRule)

    // 如果有activeRule且是档案要素规则，直接使用其配置
    if (activeRule && activeRule.ruleCode === 'archiveElements' && activeRule.ruleValue) {
      try {
        const config = JSON.parse(activeRule.ruleValue)
        console.log('🔍 从activeRule解析配置:', config)

        // 设置选中要素
        if (config.selectedElements && Array.isArray(config.selectedElements)) {
          setSelectedElements(config.selectedElements)
          console.log('✅ 设置选中要素:', config.selectedElements)
        } else {
          setSelectedElements(['title', 'responsible_party', 'document_number', 'issue_date'])
        }

        // 设置字段映射
        if (config.fieldMapping && typeof config.fieldMapping === 'object') {
          setFieldMapping(config.fieldMapping)
          const usedFieldIds = Object.values(config.fieldMapping).filter(Boolean)
          setUsedFields(usedFieldIds as string[])
          console.log('✅ 设置字段映射:', config.fieldMapping)
        } else {
          setFieldMapping({})
          setUsedFields([])
        }

        setIsInitialized(true)
        return true // 成功加载
      } catch (e) {
        console.error('解析activeRule配置失败:', e)
      }
    }

    // 如果activeRule没有配置，尝试从表单获取
    const formRuleValue = form.getFieldValue('ruleValue')
    if (formRuleValue && formRuleValue !== '{}') {
      try {
        const config = JSON.parse(formRuleValue)
        console.log('🔍 从表单解析配置:', config)

        if (config.selectedElements && Array.isArray(config.selectedElements)) {
          setSelectedElements(config.selectedElements)
        } else {
          setSelectedElements(['title', 'responsible_party', 'document_number', 'issue_date'])
        }

        if (config.fieldMapping && typeof config.fieldMapping === 'object') {
          setFieldMapping(config.fieldMapping)
          const usedFieldIds = Object.values(config.fieldMapping).filter(Boolean)
          setUsedFields(usedFieldIds as string[])
        } else {
          setFieldMapping({})
          setUsedFields([])
        }

        setIsInitialized(true)
        return true // 成功加载
      } catch (e) {
        console.error('解析表单配置失败:', e)
      }
    }

    // 如果都没有配置，使用默认配置
    if (!isInitialized) {
      console.log('🔍 使用默认配置')
      setSelectedElements(['title', 'responsible_party', 'document_number', 'issue_date'])
      setFieldMapping({})
      setUsedFields([])
      setIsInitialized(true)
    }

    return false // 使用默认配置
  }, [activeRule, form, isInitialized])

  // 监听activeRule变化，立即初始化配置
  useEffect(() => {
    console.log('🔍 activeRule变化，重新初始化:', activeRule)
    // 重置初始化状态
    setIsInitialized(false)
    // 立即初始化配置
    initializeConfig()
  }, [activeRule, initializeConfig])

  // 组件挂载时初始化
  useEffect(() => {
    if (!isInitialized) {
      initializeConfig()
    }
  }, [initializeConfig, isInitialized])

  return (
    <Card
      size="small"
      title="档案要素配置"
      style={{
        marginBottom: 16
      }}
    >
      {/* 档案要素选择区域 */}
      <FormItem label="档案要素选择" style={{ marginBottom: 16 }}>
        <Checkbox.Group
          options={archiveElements.map(element => ({
            label: element.label,
            value: element.key
          }))}
          value={selectedElements}
          onChange={handleElementsChange}
        />
        <div style={{ marginTop: 8 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            选择需要检查的档案要素，至少选择一个
          </Text>
        </div>
      </FormItem>

      {/* 字段映射配置区域 */}
      {selectedElements.length > 0 && (
        <FormItem label="档案要素字段映射配置" style={{ marginBottom: 16 }}>
          <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16 }}>
            {selectedElements.map(elementKey => {
              const element = archiveElements.find(e => e.key === elementKey)
              const unusedOptions = getUnusedFieldOptions(elementKey)
              const currentFieldId = fieldMapping[elementKey]
              const currentFieldName = currentFieldId ? getFieldName(currentFieldId) : '请选择...'

              return (
                <Row key={elementKey} gutter={16} style={{ marginBottom: 12, alignItems: 'center' }}>
                  <Col span={6}>
                    <Text strong>{element?.label}</Text>
                  </Col>
                  <Col span={2} style={{ textAlign: 'center' }}>
                    <Text>→</Text>
                  </Col>
                  <Col span={10}>
                    <Select
                      placeholder="请选择字段库字段"
                      style={{ width: '100%' }}
                      value={currentFieldId}
                      onChange={(value) => handleFieldMappingChange(elementKey, value)}
                      options={unusedOptions}
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                    />
                  </Col>
                  <Col span={6}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {currentFieldId ? `已选择：${currentFieldName}` : '未选择'}
                    </Text>
                  </Col>
                </Row>
              )
            })}

            <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 4 }}>
              <Text style={{ fontSize: 12, color: '#666' }}>
                <strong>说明：</strong>
                <br />• 每个档案要素必须映射到一个字段库字段
                <br />• 每个字段库字段只能被一个档案要素使用
                <br />• 字段库字段和Excel列的映射关系在任务配置中完成
              </Text>
            </div>
          </div>
        </FormItem>
      )}

      {/* 验证提示 */}
      {selectedElements.length > 0 && (
        <div style={{ marginTop: 8 }}>
          {selectedElements.some(key => !fieldMapping[key]) && (
            <Text type="warning" style={{ fontSize: 12 }}>
              ⚠️ 请为所有选中的档案要素配置字段映射
            </Text>
          )}
        </div>
      )}
    </Card>
  )
}
