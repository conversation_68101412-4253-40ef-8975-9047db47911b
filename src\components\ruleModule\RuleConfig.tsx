import { Button, Modal, Table, message } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import React, { useEffect, useState } from 'react'
import { useMutation, useQuery } from 'react-query'
import classNames from 'classnames'
import { getRulePage, ruleDelById, ruleDelByIds } from '../../api/rule'
import { extractResponse } from '../../api/util'
import { useVersionContext } from '../../hooks/useVersion'
import RuleAdd from './RuleAdd'

type RuleConfigType = {
  library: RuleMould
}

// 可折叠文本组件
const CollapsibleText: React.FC<{ text: string; maxLength?: number }> = ({
  text,
  maxLength = 50
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  if (!text || text.length <= maxLength) {
    return <div className="break-words">{text}</div>
  }

  return (
    <div className="break-words">
      {isExpanded ? (
        <>
          {text}
          <button
            className="ml-2 text-blue-500 hover:text-blue-700 text-sm"
            onClick={() => setIsExpanded(false)}
          >
            收起
          </button>
        </>
      ) : (
        <>
          {text.substring(0, maxLength)}
          <button
            className="ml-1 text-blue-500 hover:text-blue-700 text-sm"
            onClick={() => setIsExpanded(true)}
          >
            ...展开
          </button>
        </>
      )}
    </div>
  )
}

export default function RuleConfig({ library }: RuleConfigType) {
  const [showRuleAdd, setShowRuleAdd] = useState(false)
  const { version } = useVersionContext()

  const [param, setParam] = useState({
    pageNo: 1,
    pageSize: 10,
    search_CONTAINS_fieldName: '',
    search_EQ_createUserId: undefined,
  })
  const [activeRule, setActiveRule] = useState<RuleMouldConfig>()
  const [activeRuleList, setActiveRuleList] = useState<string[]>([])

  const [isDelOpen, setIsDelOpen] = useState(false)

  const { data, refetch } = useQuery(
    ['getRulePage', library],
    extractResponse(() =>
      getRulePage(param.pageNo, param.pageSize, {
        ...param,
        search_EQ_libraryId: library?.id,
      })
    )
  )

  function xiChengText() {
    return version === 'xicheng' ? '!text-[22px]' : ''
  }

  useEffect(() => {
    if (param.pageNo) {
      refetch()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param.pageNo])

  const columns: ColumnsType<RuleMouldConfig> = [
    {
      title: '规则名称',
      width: 150,
      render: (item: RuleMouldConfig) => (
        <div className="max-w-[150px] break-words">{item.ruleAliasName}</div>
      ),
      key: 'ruleAliasName',
      className: xiChengText(),
    },
    {
      title: '规则分类',
      width: 120,
      render: (item: RuleMouldConfig) => (
        <div className="max-w-[120px] break-words">{item.ruleName}</div>
      ),
      key: 'ruleAliasName',
      className: xiChengText(),
    },
    {
      title: '规则介绍',
      width: 200, // 固定列宽，防止表格撑开
      render: (item: RuleMouldConfig) => (
        <div className="max-w-[200px]">
          <CollapsibleText
            text={item.ruleDescribe || item.ruleValue || ''}
            maxLength={50}
          />
        </div>
      ),
      key: 'ruleDescribe',
      className: xiChengText(),
    },
    {
      title: '字段',
      width: 160,
      render: (item: RuleMouldConfig) => (
        <div className="max-w-[160px]">
          <CollapsibleText
            text={item.ruleFieldsName?.join(',') || ''}
            maxLength={30}
          />
        </div>
      ),
      key: 'ruleFieldsName',
      className: xiChengText(),
    },
    {
      title: '创建人',
      width: 100,
      render: (item: RuleMouldConfig) => (
        <div className="max-w-[100px] break-words">{item.createUserName}</div>
      ),
      key: 'createUserName',
      className: xiChengText(),
    },
    {
      title: '创建时间',
      width: 120,
      render: (item: RuleMouldConfig) => (
        <div className="max-w-[120px] break-words">{item.createTime}</div>
      ),
      key: 'createTime',
      className: xiChengText(),
    },
    {
      title: '操作',
      width: 120,
      fixed: 'right' as const,
      className: xiChengText(),
      render: (item: RuleMouldConfig) => (
        <div>
          <Button
            type="link"
            className={classNames(' !text-primary-default', xiChengText())}
            onClick={() => {
              setActiveRule(item)
              setShowRuleAdd(true)
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            className={classNames(' !text-primary-default', xiChengText())}
            onClick={() => {
              setActiveRule(item)
              setIsDelOpen(true)
            }}
          >
            删除
          </Button>
        </div>
      ),
    },
  ]

  const ruleDelByIdMutation = useMutation(ruleDelById, {
    onSuccess(res) {
      message.success(res.data.message)
      setIsDelOpen(false)
      setActiveRule(undefined)
      refetch()
    },
  })

  // ruleDelByIds
  const ruleDelByIdsMutation = useMutation(ruleDelByIds, {
    onSuccess(res) {
      message.success(res.data.message)
      setIsDelOpen(false)
      refetch()
    },
  })

  const paginationObj = {
    total: data?.total,

    onChange: (page: number) => {
      setParam(p => ({ ...p, pageNo: page }))
    },
    // pageSizeOptions: ['5', '10', '50'],
    showSizeChanger: false,
    current: data?.pageNo,
  }

  return (
    <div>
      <div className="flex">
        <Button
          className="ml-auto"
          onClick={() => {
            setShowRuleAdd(true)
          }}
        >
          新建
        </Button>
        <Button
          className="ml-4"
          onClick={() => {
            if (activeRuleList.length > 0) {
              setIsDelOpen(true)
            }
          }}
        >
          删除
        </Button>
      </div>
      <Table
        rowSelection={{
          type: 'checkbox',
          onChange: item => {
            console.log(item)
            setActiveRuleList(item as string[])
          },
        }}
        columns={columns}
        dataSource={data?.list}
        className="mt-4"
        rowKey={item => item.id}
        pagination={paginationObj}
        scroll={{ x: 'max-content' }}
        size="middle"
      />

      <RuleAdd
        open={showRuleAdd}
        close={() => {
          setShowRuleAdd(false)
          setActiveRule(undefined)
        }}
        library={library}
        refetch={refetch}
        activeRule={activeRule}
      />
      <Modal
        title="确认删除"
        open={isDelOpen}
        onOk={() => {
          if (activeRule) {
            ruleDelByIdMutation.mutate(activeRule?.id!)
          } else {
            ruleDelByIdsMutation.mutate(activeRuleList)
          }
        }}
        onCancel={() => {
          setActiveRule(undefined)
          setIsDelOpen(false)
        }}
      >
        <p>{activeRule ? '是否确认删除此规则?' : '是否确认删除这些规则'}</p>
      </Modal>
    </div>
  )
}
