#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型池管理器 - 常驻内存模型管理
"""
import asyncio
import logging
import threading
import time
from typing import Dict, Any, Optional, Union
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import queue

from .config_manager import ConfigManager
from ..models.ocr_subprocess import OCRSubprocessRunner
from ..models.rapidocr_engine import RapidOCREngine
from ..models.embedding_models import QwenEmbeddingModel, BGEEmbeddingModel
from ..models.llm_models import QwenLLMModel


class ModelPool:
    """模型池 - 管理常驻内存的模型实例"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()

        # 模型实例缓存
        self._models: Dict[str, Any] = {}
        self._model_locks: Dict[str, threading.Lock] = {}
        self._model_usage_count: Dict[str, int] = {}
        self._last_used: Dict[str, float] = {}

        # OCR引擎池 (支持子进程和RapidOCR两种模式)
        self._ocr_pool: Optional[Union[OCRSubprocessRunner, RapidOCREngine]] = None
        self._ocr_lock = threading.Lock()

        # OCR引擎类型配置
        self._ocr_engine_type = self.config.get('ocr', {}).get('engine_type', 'rapidocr')  # 'subprocess' 或 'rapidocr'

        # 线程池
        self._executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="ModelPool")

        # 初始化标志
        self._initialized = False
        self._init_lock = threading.Lock()

        self.logger.info("模型池初始化完成")
    
    def initialize(self):
        """初始化模型池"""
        with self._init_lock:
            if self._initialized:
                return
            
            self.logger.info("开始初始化模型池...")
            
            # 预加载关键模型
            try:
                # 1. 初始化OCR进程池
                self._init_ocr_pool()
                
                # 2. 预加载embedding模型
                self._preload_embedding_model()

                # 3. 预加载LLM模型（确保预热）
                self._preload_llm_model()

                # 4. 预加载印章检测模型（如果启用）
                self._preload_seal_detection_model()

                self._initialized = True
                self.logger.info("模型池初始化成功")
                
            except Exception as e:
                self.logger.error(f"模型池初始化失败: {e}")
                raise
    
    def _init_ocr_pool(self):
        """初始化OCR引擎池"""
        try:
            if self._ocr_engine_type == 'rapidocr':
                self.logger.info("初始化RapidOCR引擎...")

                # 获取OCR配置
                ocr_config = self.config.get('ocr', {})
                device_id = ocr_config.get('device_id', 0)

                # 创建RapidOCR引擎
                self._ocr_pool = RapidOCREngine(device_id=device_id, config=ocr_config)

                self.logger.info("RapidOCR引擎初始化完成")

            else:
                # 传统子进程模式（向后兼容）
                self.logger.info("初始化OCR子进程池...")
                self._ocr_pool = OCRSubprocessRunner()

                # 预热OCR进程
                test_image = Path("test_image.png")
                if test_image.exists():
                    self.logger.info("预热OCR进程...")
                    result = self._ocr_pool.recognize(
                        image_path=str(test_image),
                        confidence_threshold=0.5,
                        use_cpu=True  # 使用CPU模式确保稳定性
                    )
                    if result.get('success'):
                        self.logger.info(f"OCR进程预热成功，检测到{len(result.get('results', []))}个文本块")
                    else:
                        self.logger.warning(f"OCR进程预热失败: {result.get('error')}")

        except Exception as e:
            self.logger.error(f"OCR引擎池初始化失败: {e}")
            raise
    
    def _preload_embedding_model(self):
        """预加载embedding模型"""
        try:
            embedding_config = self.config.get('models', {}).get('embedding', {})
            model_name = embedding_config.get('model_name', 'qwen3-embedding-0.6b:f16')
            
            self.logger.info(f"预加载embedding模型: {model_name}")
            
            if 'qwen' in model_name.lower():
                model = QwenEmbeddingModel(
                    model_size="0.6b",
                    max_length=embedding_config.get('config', {}).get('max_length', 32768),
                    batch_size=embedding_config.get('config', {}).get('batch_size', 4)
                )
            else:
                model = BGEEmbeddingModel(
                    model_size="m3",
                    max_length=embedding_config.get('config', {}).get('max_length', 512),
                    batch_size=embedding_config.get('config', {}).get('batch_size', 8)
                )
            
            self._models['embedding'] = model
            self._model_locks['embedding'] = threading.Lock()
            self._model_usage_count['embedding'] = 0
            self._last_used['embedding'] = time.time()
            
            # 预热模型
            test_texts = ["档案管理", "文件整理"]
            embeddings = model.encode(test_texts)
            self.logger.info(f"Embedding模型预热成功，维度: {embeddings.shape}")
            
        except Exception as e:
            self.logger.error(f"Embedding模型预加载失败: {e}")
            raise
    
    def _preload_llm_model(self):
        """预加载LLM模型"""
        try:
            llm_config = self.config.get('models', {}).get('llm', {})
            model_name = llm_config.get('model_name', 'qwen3:4b')

            self.logger.info(f"预加载LLM模型: {model_name}")

            if 'qwen' in model_name.lower():
                # 从实际的模型名称中提取大小
                if ':1.7b' in model_name.lower() or '1.7b' in model_name.lower():
                    model_size = "1.7b"
                elif ':3b' in model_name.lower() or '3b' in model_name.lower():
                    model_size = "3b"
                elif ':4b' in model_name.lower() or '4b' in model_name.lower():
                    model_size = "4b"
                elif ':7b' in model_name.lower() or '7b' in model_name.lower():
                    model_size = "7b"
                else:
                    model_size = "1.7b"  # 默认使用1.7b（最轻量级）
                
                model = QwenLLMModel(
                    model_size=model_size,
                    temperature=llm_config.get('config', {}).get('temperature', 0.1),
                    top_p=llm_config.get('config', {}).get('top_p', 0.9),
                )
                
                self._models['llm'] = model
                self._model_locks['llm'] = threading.Lock()
                self._model_usage_count['llm'] = 0
                self._last_used['llm'] = time.time()
                
                # 预热模型 - 使用简短提示词测试
                test_response = model.generate("只返回以下4个字：预热成功", 
                                               num_predict=10,
                                               enable_thinking=False)  # 确保关闭thinking mode, Ollama服务只需要在promp最后接上/no_think)
                self.logger.info(f"LLM模型预热: {test_response.content[:50]}...")
            
        except Exception as e:
            self.logger.error(f"LLM模型预加载失败: {e}")
            # LLM预加载失败不影响整体服务
    
    def get_ocr_model(self) -> Union[OCRSubprocessRunner, RapidOCREngine]:
        """获取OCR模型实例"""
        if not self._initialized:
            self.initialize()

        with self._ocr_lock:
            if self._ocr_pool is None:
                self._init_ocr_pool()
            return self._ocr_pool
    
    def get_embedding_model(self) -> Union[QwenEmbeddingModel, BGEEmbeddingModel]:
        """获取embedding模型实例"""
        if not self._initialized:
            self.initialize()
        
        model_key = 'embedding'
        
        if model_key not in self._models:
            self._preload_embedding_model()
        
        with self._model_locks[model_key]:
            self._model_usage_count[model_key] += 1
            self._last_used[model_key] = time.time()
            return self._models[model_key]
    
    def get_llm_model(self) -> Optional[QwenLLMModel]:
        """获取LLM模型实例"""
        if not self._initialized:
            self.initialize()
        
        model_key = 'llm'
        
        if model_key not in self._models:
            try:
                self._preload_llm_model()
            except Exception as e:
                self.logger.warning(f"LLM模型加载失败: {e}")
                return None
        
        if model_key in self._models:
            with self._model_locks[model_key]:
                self._model_usage_count[model_key] += 1
                self._last_used[model_key] = time.time()
                return self._models[model_key]
        
        return None
    
    def get_seal_detection_model(self):
        """获取印章检测模型"""
        if not self._initialized:
            self.initialize()

        model_key = 'seal_detection'

        # 检查是否启用印章检测
        stamp_config = self.config.get('image_preprocessing', {}).get('stamp_processing', {})
        if not stamp_config.get('enabled', False):
            self.logger.debug("印章检测功能未启用")
            return None

        if model_key not in self._models:
            try:
                self._preload_seal_detection_model()
            except Exception as e:
                self.logger.warning(f"印章检测模型加载失败: {e}")
                return None

        if model_key in self._models:
            with self._model_locks.get(model_key, threading.Lock()):
                self._model_usage_count[model_key] = self._model_usage_count.get(model_key, 0) + 1
                self._last_used[model_key] = time.time()
                return self._models[model_key]

        return None

    def get_model_stats(self) -> Dict[str, Any]:
        """获取模型使用统计"""
        stats = {}
        for model_key in self._models:
            stats[model_key] = {
                'usage_count': self._model_usage_count.get(model_key, 0),
                'last_used': self._last_used.get(model_key, 0),
                'loaded': True
            }
        
        # OCR统计
        stats['ocr'] = {
            'loaded': self._ocr_pool is not None,
            'usage_count': 0,  # OCR使用计数需要单独实现
            'last_used': time.time() if self._ocr_pool else 0
        }
        
        return stats

    def _preload_seal_detection_model(self):
        """预加载印章检测模型"""
        try:
            # 检查是否启用印章检测
            stamp_config = self.config.get('image_preprocessing', {}).get('stamp_processing', {})

            if not stamp_config.get('enabled', False):
                self.logger.info("印章检测功能未启用，跳过预加载")
                return

            model_name = stamp_config.get('model_name', 'PP-OCRv4_mobile_seal_det')
            confidence_threshold = stamp_config.get('confidence_threshold', 0.8)
            self.logger.info(f"预加载印章检测模型: {model_name}")

            # 直接创建印章检测模型实例
            from ..models.seal_models import create_seal_detection_model

            # 构建配置字典传递给create_seal_detection_model
            seal_config = {
                'enabled': True,
                'model_name': model_name,
                'confidence_threshold': confidence_threshold
            }

            model_instance = create_seal_detection_model(seal_config)

            if model_instance and model_instance.load_model():
                # 按照model_pool标准模式存储
                model_key = 'seal_detection'
                self._models[model_key] = model_instance
                self._model_locks[model_key] = threading.Lock()
                self._model_usage_count[model_key] = 0
                self._last_used[model_key] = time.time()

                self.logger.info(f"印章检测模型预加载成功: {model_name}")
            else:
                self.logger.warning(f"印章检测模型预加载失败: {model_name}")

        except Exception as e:
            self.logger.error(f"印章检测模型预加载失败: {e}")
            # 不抛出异常，允许系统继续运行

    def cleanup(self):
        """清理资源"""
        self.logger.info("清理模型池资源...")
        
        # 清理OCR进程
        if self._ocr_pool:
            try:
                self._ocr_pool.cleanup()
            except Exception as e:
                self.logger.error(f"OCR进程清理失败: {e}")
        
        # 清理模型实例
        self._models.clear()
        self._model_locks.clear()
        
        # 关闭线程池
        if self._executor:
            self._executor.shutdown(wait=True)
        
        self.logger.info("模型池资源清理完成")


# 全局模型池实例
_global_model_pool: Optional[ModelPool] = None
_pool_lock = threading.Lock()


def get_model_pool(config_path: Optional[str] = None) -> ModelPool:
    """获取全局模型池实例"""
    global _global_model_pool
    
    with _pool_lock:
        if _global_model_pool is None:
            _global_model_pool = ModelPool(config_path)
        return _global_model_pool


def cleanup_model_pool():
    """清理全局模型池"""
    global _global_model_pool
    
    with _pool_lock:
        if _global_model_pool:
            _global_model_pool.cleanup()
            _global_model_pool = None
