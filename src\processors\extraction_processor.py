"""
提取处理器 - 负责使用LLM进行信息提取
"""
import logging
from typing import Dict, Any, Optional


class ExtractionProcessor:
    """提取处理器"""
    
    def __init__(self, device_manager, model_manager):
        self.device_manager = device_manager
        self.model_manager = model_manager
        self.logger = logging.getLogger(__name__)
        
    def extract_with_llm(self, key: str, fused_info: Dict[str, Any], options: Dict[str, Any]) -> str:
        """
        使用LLM进行信息提取
        
        Args:
            key: 要提取的关键词
            fused_info: 融合后的信息
            options: 提取选项
            
        Returns:
            提取的值
        """
        try:
            self.logger.info(f"开始LLM提取，关键词: {key}")
            
            # 获取LLM模型
            llm_model = self.model_manager.get_llm_model()
            
            # 构建提取提示词
            prompt = self._build_extraction_prompt(key, fused_info, options)
            
            # 执行LLM推理
            response = llm_model.generate(prompt, options.get('llm_options', {}))
            
            # 处理响应
            extracted_value = self._process_llm_response(response, key, options)
            
            self.logger.info(f"LLM提取完成，关键词: {key}, 结果: {extracted_value[:100]}...")
            return extracted_value
            
        except Exception as e:
            self.logger.error(f"LLM提取失败，关键词: {key}, 错误: {e}")
            return ""
    
    def _build_extraction_prompt(self, key: str, fused_info: Dict[str, Any], options: Dict[str, Any]) -> str:
        """构建提取提示词"""
        # 获取文档类型
        domain = options.get('domain', 'general')
        is_multi_page = options.get('is_multi_page', False)
        
        # 基础提示词模板
        if domain == 'archive':
            prompt_template = self._get_archive_prompt_template(key, is_multi_page)
        else:
            prompt_template = self._get_general_prompt_template(key, is_multi_page)
        
        # 填充信息
        full_text = fused_info.get('full_text', '')
        relevant_contexts = fused_info.get('relevant_contexts', [])
        
        # 构建上下文部分
        context_text = ""
        if relevant_contexts:
            context_text = "\n相关上下文：\n" + "\n".join(f"- {ctx}" for ctx in relevant_contexts[:3])
        
        # 多页信息提示
        multi_page_hint = ""
        if is_multi_page:
            page_count = options.get('page_count', 1)
            multi_page_hint = f"\n注意：这是一个包含{page_count}页的档案文档，请综合所有页面的信息进行提取。"
        
        # 组装最终提示词
        prompt = prompt_template.format(
            key=key,
            full_text=full_text[:2000],  # 限制文本长度
            context_text=context_text,
            multi_page_hint=multi_page_hint
        )
        
        return prompt
    
    def _get_archive_prompt_template(self, key: str, is_multi_page: bool) -> str:
        """获取档案领域的提示词模板"""
        if key in ['题名', 'title']:
            return """请从以下档案文档中提取题名信息。题名通常是文档的标题或主要内容描述。

文档内容：
{full_text}
{context_text}
{multi_page_hint}

请直接返回提取的题名，不要包含其他解释。如果找不到明确的题名，请返回空字符串。"""

        elif key in ['责任者', 'responsible_party']:
            return """请从以下档案文档中提取责任者信息。责任者通常是文档的作者、发文单位或负责人。

文档内容：
{full_text}
{context_text}
{multi_page_hint}

请直接返回提取的责任者，不要包含其他解释。如果找不到明确的责任者，请返回空字符串。"""

        elif key in ['文号', 'document_number']:
            return """请从以下档案文档中提取文号信息。文号通常是文档的编号、发文字号或标识符。

文档内容：
{full_text}
{context_text}
{multi_page_hint}

请直接返回提取的文号，不要包含其他解释。如果找不到明确的文号，请返回空字符串。"""

        elif key in ['成文日期', 'issue_date', 'date']:
            return """请从以下档案文档中提取成文日期信息。成文日期通常是文档的创建日期、发文日期或签署日期。

文档内容：
{full_text}
{context_text}
{multi_page_hint}

请直接返回提取的日期，格式为YYYY-MM-DD。如果找不到明确的日期，请返回空字符串。"""

        else:
            return """请从以下档案文档中提取"{key}"相关的信息。

文档内容：
{full_text}
{context_text}
{multi_page_hint}

请直接返回提取的{key}信息，不要包含其他解释。如果找不到相关信息，请返回空字符串。"""
    
    def _get_general_prompt_template(self, key: str, is_multi_page: bool) -> str:
        """获取通用提示词模板"""
        return """请从以下文档中提取"{key}"相关的信息。

文档内容：
{full_text}
{context_text}
{multi_page_hint}

请直接返回提取的{key}信息，不要包含其他解释。如果找不到相关信息，请返回空字符串。"""
    
    def _process_llm_response(self, response: str, key: str, options: Dict[str, Any]) -> str:
        """处理LLM响应"""
        try:
            # 清理响应文本
            cleaned_response = response.strip()
            
            # 移除常见的无关前缀
            prefixes_to_remove = [
                "根据文档内容，",
                "提取的信息是：",
                "答案是：",
                "结果：",
                f"{key}是：",
                f"{key}："
            ]
            
            for prefix in prefixes_to_remove:
                if cleaned_response.startswith(prefix):
                    cleaned_response = cleaned_response[len(prefix):].strip()
            
            # 移除引号
            if cleaned_response.startswith('"') and cleaned_response.endswith('"'):
                cleaned_response = cleaned_response[1:-1]
            if cleaned_response.startswith("'") and cleaned_response.endswith("'"):
                cleaned_response = cleaned_response[1:-1]
            
            # 特殊处理日期格式
            if key in ['成文日期', 'issue_date', 'date']:
                cleaned_response = self._normalize_date_format(cleaned_response)
            
            return cleaned_response
            
        except Exception as e:
            self.logger.warning(f"响应处理失败: {e}")
            return response.strip() if response else ""
    
    def _normalize_date_format(self, date_str: str) -> str:
        """标准化日期格式"""
        import re
        
        if not date_str:
            return ""
        
        # 匹配各种日期格式
        date_patterns = [
            r'(\d{4})[年\-/](\d{1,2})[月\-/](\d{1,2})[日]?',
            r'(\d{4})\.(\d{1,2})\.(\d{1,2})',
            r'(\d{1,2})[月\-/](\d{1,2})[日]?[,，]\s*(\d{4})',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                groups = match.groups()
                if len(groups) == 3:
                    if len(groups[0]) == 4:  # 年在前
                        year, month, day = groups
                    else:  # 年在后
                        month, day, year = groups
                    
                    try:
                        return f"{int(year):04d}-{int(month):02d}-{int(day):02d}"
                    except ValueError:
                        continue
        
        return date_str
