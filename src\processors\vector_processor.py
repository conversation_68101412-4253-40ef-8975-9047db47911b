"""
向量处理器 - 负责文本向量化和语义检索
"""
import logging
import numpy as np
from typing import Dict, Any, List, Optional


class VectorProcessor:
    """向量处理器"""
    
    def __init__(self, device_manager, model_manager):
        self.device_manager = device_manager
        self.model_manager = model_manager
        self.logger = logging.getLogger(__name__)
        
    def build_vectors(self, structured_info: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建文档向量数据库
        
        Args:
            structured_info: 结构化文档信息
            options: 处理选项
            
        Returns:
            向量数据库
        """
        try:
            self.logger.info("开始构建向量数据库")
            
            # 获取嵌入模型
            embedding_model = self.model_manager.get_embedding_model()
            
            # 提取文本内容
            text_content = structured_info.get('text', '')
            if not text_content:
                self.logger.warning("文档无文本内容，返回空向量数据库")
                return {'vectors': [], 'texts': [], 'metadata': {}}
            
            # 文本分段
            text_segments = self._segment_text(text_content, options)
            
            # 生成向量
            vectors = []
            for segment in text_segments:
                try:
                    vector = embedding_model.encode(segment)
                    vectors.append(vector)
                except Exception as e:
                    self.logger.warning(f"向量生成失败: {segment[:50]}..., 错误: {e}")
                    # 使用零向量作为fallback
                    vectors.append(np.zeros(768))  # 默认向量维度
            
            vector_db = {
                'vectors': vectors,
                'texts': text_segments,
                'metadata': {
                    'document_path': structured_info.get('document_path'),
                    'page_index': options.get('page_index', 0),
                    'total_segments': len(text_segments),
                    'vector_dim': len(vectors[0]) if vectors else 0
                }
            }
            
            self.logger.info(f"向量数据库构建完成，文本段数: {len(text_segments)}")
            return vector_db
            
        except Exception as e:
            self.logger.error(f"向量构建失败: {e}")
            raise
    
    def semantic_search(self, query: str, vector_db: Dict[str, Any], options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        语义检索
        
        Args:
            query: 查询文本
            vector_db: 向量数据库
            options: 检索选项
            
        Returns:
            相关文本段列表
        """
        try:
            self.logger.info(f"开始语义检索: {query}")
            
            if not vector_db.get('vectors') or not vector_db.get('texts'):
                self.logger.warning("向量数据库为空，返回空结果")
                return []
            
            # 获取嵌入模型
            embedding_model = self.model_manager.get_embedding_model()
            
            # 查询向量化
            query_vector = embedding_model.encode(query)
            
            # 计算相似度
            similarities = []
            for i, doc_vector in enumerate(vector_db['vectors']):
                similarity = self._cosine_similarity(query_vector, doc_vector)
                similarities.append((i, similarity))
            
            # 排序并获取top-k
            top_k = options.get('top_k', 5)
            similarities.sort(key=lambda x: x[1], reverse=True)
            top_results = similarities[:top_k]
            
            # 构建结果
            results = []
            for idx, score in top_results:
                if score > options.get('min_similarity', 0.1):  # 过滤低相似度结果
                    results.append({
                        'text': vector_db['texts'][idx],
                        'similarity': float(score),
                        'index': idx,
                        'metadata': vector_db.get('metadata', {})
                    })
            
            self.logger.info(f"语义检索完成，返回 {len(results)} 个相关结果")
            return results
            
        except Exception as e:
            self.logger.error(f"语义检索失败: {e}")
            return []
    
    def _segment_text(self, text: str, options: Dict[str, Any]) -> List[str]:
        """文本分段"""
        max_length = options.get('max_segment_length', 512)
        overlap = options.get('segment_overlap', 50)
        
        if len(text) <= max_length:
            return [text]
        
        segments = []
        start = 0
        while start < len(text):
            end = min(start + max_length, len(text))
            segment = text[start:end]
            segments.append(segment)
            
            if end >= len(text):
                break
            start = end - overlap
        
        return segments
    
    def _cosine_similarity(self, vec1, vec2) -> float:
        """计算余弦相似度"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
        except Exception:
            return 0.0
