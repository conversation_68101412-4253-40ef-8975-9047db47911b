#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
档案要素提取服务 - 优化版本，使用模型池和并发处理
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import json
import re
from enum import Enum
from dataclasses import dataclass, field

from ..core.model_pool import get_model_pool
from ..models.ocr_models import OCRResult


class DocumentPageType(Enum):
    """文书页面类型枚举"""
    HEADER_PAGE = "header"          # 首页（红头、标题页）
    CONTENT_PAGE = "content"        # 正文页
    SIGNATURE_PAGE = "signature"    # 签发页（成文日期、责任者）
    ATTACHMENT_PAGE = "attachment"  # 附件页
    UNKNOWN = "unknown"             # 未知类型


@dataclass
class ExtractionConfig:
    """档案要素提取配置"""
    # 前N页范围配置
    front_pages_count: int = 3

    # 关键词配置
    attachment_keywords: List[str] = field(default_factory=lambda: [
        "附件", "附表", "附录", "清单", "名单", "目录", "统计表", "汇总表"
    ])

    signature_keywords: List[str] = field(default_factory=lambda: [
        "年", "月", "日", "签发", "制发", "发布", "印发", "颁布", "批准"
    ])

    header_keywords: List[str] = field(default_factory=lambda: [
        "通知", "决定", "公告", "函", "意见", "办法", "规定", "条例", "通报", "批复"
    ])

    # 提取优先级策略 - 每个要素按优先级尝试不同页面类型
    title_priority: List[str] = field(default_factory=lambda: ["header", "content"])
    responsible_party_priority: List[str] = field(default_factory=lambda: ["header", "signature"])
    document_number_priority: List[str] = field(default_factory=lambda: ["header"])
    issue_date_priority: List[str] = field(default_factory=lambda: ["signature", "header"])

    # 置信度阈值
    min_confidence: float = 0.5

    # 是否启用智能结构分析
    enable_smart_structure_analysis: bool = True

    # 印章处理配置
    enable_stamp_processing: bool = False
    stamp_confidence_threshold: float = 0.8


@dataclass
class PageClassificationResult:
    """页面分类结果"""
    page_index: int
    page_type: DocumentPageType
    confidence: float
    keywords_found: List[str]
    reasoning: str  # 分类理由


class ArchiveExtractionService:
    """档案要素提取服务 - 优化版本"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.model_pool = get_model_pool(config_path)
        
        # 线程池用于并发处理
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="ArchiveExtraction")
        
        # 默认要提取的档案要素
        self.default_elements = ["题名", "责任者", "文号", "发文日期"]

        # 保存最后一次OCR结果（用于调试和响应构建）
        self._last_ocr_result = None

        self.logger.info("档案要素提取服务初始化完成")
    
    async def extract_from_image(self,
                               image_path: Union[str, Path],
                               elements: Optional[List[str]] = None,
                               confidence_threshold: float = 0.5,
                               enable_stamp_processing: bool = False,
                               stamp_confidence_threshold: float = 0.8) -> Dict[str, Any]:
        """
        从图像中提取档案要素

        Args:
            image_path: 图像文件路径
            elements: 要提取的要素列表，默认为["题名", "责任者", "文号", "发文日期"]
            confidence_threshold: OCR置信度阈值
            enable_stamp_processing: 是否启用印章检测
            stamp_confidence_threshold: 印章检测置信度阈值

        Returns:
            提取结果字典
        """
        start_time = time.time()

        try:
            # 使用默认要素如果未指定
            if elements is None:
                elements = self.default_elements.copy()

            self.logger.info(f"开始提取档案要素: {image_path}")
            self.logger.info(f"目标要素: {elements}")
            if enable_stamp_processing:
                self.logger.info(f"启用印章检测，置信度阈值: {stamp_confidence_threshold}")

            # 步骤1: OCR文本识别（可选印章检测）
            ocr_result = await self._perform_ocr(
                image_path,
                confidence_threshold,
                enable_stamp_processing,
                stamp_confidence_threshold
            )

            # 保存OCR结果供后续使用
            self._last_ocr_result = ocr_result

            if not ocr_result['success']:
                return {
                    'success': False,
                    'error': f"OCR识别失败: {ocr_result.get('error', '未知错误')}",
                    'processing_time': time.time() - start_time
                }
            
            self.logger.info(f"OCR完成，耗时: {time.time() - start_time:.2f}秒")
            # 步骤2: 智能要素提取
            extraction_result = await self._extract_elements(
                ocr_result['full_text'], 
                ocr_result['text_blocks'],
                elements
            )
            
            # 组装最终结果
            result = {
                'success': True,
                'elements': extraction_result,
                'ocr_info': {
                    'total_blocks': len(ocr_result['text_blocks']),
                    'full_text_length': len(ocr_result['full_text']),
                    'confidence_threshold': confidence_threshold
                },
                'processing_time': time.time() - start_time
            }
            
            self.logger.info(f"档案要素提取完成，耗时: {result['processing_time']:.2f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"档案要素提取失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    async def _perform_ocr(self, image_path: Union[str, Path], confidence_threshold: float,
                          enable_stamp_processing: bool = False,
                          stamp_confidence_threshold: float = 0.8) -> Dict[str, Any]:
        """执行OCR识别，可选印章检测"""
        ocr_start_time = time.time()

        try:
            # 获取OCR模型实例（常驻内存）
            model_start_time = time.time()
            ocr_model = self.model_pool.get_ocr_model()
            model_load_time = time.time() - model_start_time

            if model_load_time > 0.1:  # 只有当模型加载时间超过0.1秒时才记录
                self.logger.info(f"⏱️ OCR模型获取耗时: {model_load_time:.3f}秒")

            # 在线程池中执行OCR（避免阻塞异步循环）
            loop = asyncio.get_event_loop()

            ocr_inference_start_time = time.time()

            if enable_stamp_processing:
                # 使用带印章检测的OCR方法 - RTX 3060 GPU模式
                result = await loop.run_in_executor(
                    self.executor,
                    ocr_model.recognize_with_stamp_detection,
                    str(image_path),
                    confidence_threshold,
                    False,  # use_cpu=False，启用RTX 3060 GPU加速
                    enable_stamp_processing,
                    stamp_confidence_threshold
                )
            else:
                # 使用标准OCR方法 - RTX 3060 GPU模式
                result = await loop.run_in_executor(
                    self.executor,
                    ocr_model.recognize,
                    str(image_path),
                    confidence_threshold,
                    False  # use_cpu=False，启用RTX 3060 GPU加速
                )

            ocr_inference_time = time.time() - ocr_inference_start_time
            self.logger.info(f"⏱️ OCR推理耗时: {ocr_inference_time:.3f}秒")

            if not result.get('success', False):
                total_ocr_time = time.time() - ocr_start_time
                self.logger.error(f"⏱️ OCR失败前耗时: {total_ocr_time:.3f}秒")
                return {
                    'success': False,
                    'error': result.get('error', 'OCR识别失败')
                }

            # 处理OCR结果
            post_process_start_time = time.time()
            text_blocks = result.get('results', [])
            full_text = '\n'.join([block['text'] for block in text_blocks])

            # 处理印章检测结果（如果有）
            stamp_info = result.get('stamp_detection', {})
            post_process_time = time.time() - post_process_start_time

            total_ocr_time = time.time() - ocr_start_time

            self.logger.info(f"OCR识别完成，检测到{len(text_blocks)}个文本块")
            if stamp_info.get('enabled', False):
                self.logger.info(f"印章检测完成，检测到{stamp_info.get('total_stamps', 0)}个印章")

            if post_process_time > 0.01:
                self.logger.info(f"⏱️ OCR后处理耗时: {post_process_time:.3f}秒")
            self.logger.info(f"⏱️ OCR总耗时: {total_ocr_time:.3f}秒")

            return {
                'success': True,
                'text_blocks': text_blocks,
                'full_text': full_text,
                'stamp_detection': stamp_info
            }

        except Exception as e:
            self.logger.error(f"OCR识别异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _extract_elements(self,
                              full_text: str,
                              text_blocks: List[Dict[str, Any]],
                              elements: List[str],
                              context_info: dict = None) -> Dict[str, Any]:
        """使用LLM提取档案要素（支持上下文感知）"""
        extraction_start_time = time.time()

        try:
            # 获取LLM模型实例（常驻内存）
            model_start_time = time.time()
            llm_model = self.model_pool.get_llm_model()
            model_load_time = time.time() - model_start_time

            if model_load_time > 0.1:  # 只有当模型加载时间超过0.1秒时才记录
                self.logger.info(f"⏱️ LLM模型获取耗时: {model_load_time:.3f}秒")

            if llm_model is None:
                # 如果LLM不可用，使用规则提取作为降级方案
                self.logger.warning("LLM模型不可用，使用规则提取")
                rule_start_time = time.time()
                result = self._rule_based_extraction(full_text, text_blocks, elements)
                rule_time = time.time() - rule_start_time
                self.logger.info(f"⏱️ 规则提取耗时: {rule_time:.3f}秒")
                return result

            # 构建提取提示词（支持上下文）
            prompt_start_time = time.time()
            prompt = self._build_extraction_prompt(full_text, elements, context_info)
            prompt_build_time = time.time() - prompt_start_time

            if prompt_build_time > 0.01:  # 只有当提示词构建时间超过0.01秒时才记录
                self.logger.info(f"⏱️ 提示词构建耗时: {prompt_build_time:.3f}秒")
            
            # 判断是否为QWEN类型的LLM模型
            if llm_model.model_name.startswith("qwen3"):
                enable_thinking = False  # 确保关闭thinking mode, Ollama服务只需要在promp最后接上/no_think
                kwargs = {
                    'enable_thinking': enable_thinking,  # 确保关闭thinking mode
                    'temperature': 0.1,  # 降低随机性，提高准确性
                    'num_predict': 1024  # 限制输出长度
                }
            else:
                kwargs = {}

            # 在线程池中执行LLM推理
            loop = asyncio.get_event_loop()

            # 创建一个包装函数来正确传递关键字参数
            def generate_with_kwargs():
                return llm_model.generate(prompt, **kwargs)

            # 在线程池中执行LLM推理
            llm_inference_start_time = time.time()

            response = await loop.run_in_executor(
                self.executor,
                generate_with_kwargs
            )

            llm_inference_time = time.time() - llm_inference_start_time
            self.logger.info(f"⏱️ LLM推理耗时: {llm_inference_time:.3f}秒")

            # 解析LLM响应
            parse_start_time = time.time()
            response_content = response.content if hasattr(response, 'content') else str(response)
            self.logger.debug(f"LLM响应类型: {type(response)}, 内容长度: {len(response_content)}")
            self.logger.debug(f"LLM原始响应内容: {response_content[:5000]}")  # 添加详细日志
            parsed_response = self._parse_llm_response(response_content, elements)
            parse_time = time.time() - parse_start_time

            if parse_time > 0.01:  # 只有当解析时间超过0.01秒时才记录
                self.logger.info(f"⏱️ 响应解析耗时: {parse_time:.3f}秒")

            # 处理新格式和旧格式的兼容性
            if 'elements' in parsed_response:
                extracted_elements = parsed_response['elements']
                # 如果有详细信息，记录日志
                if 'element_details' in parsed_response:
                    for element, details in parsed_response['element_details'].items():
                        if details['confidence'] > 0:
                            self.logger.info(f"要素提取: {element}={details['value']} (置信度: {details['confidence']:.2f})")

                # 如果有文档状态信息，记录日志
                if 'document_status' in parsed_response:
                    doc_status = parsed_response['document_status']
                    self.logger.info(f"文档状态: {doc_status.get('page_type', 'unknown')}, 内容完整: {doc_status.get('content_complete', False)}")

                # 返回增强格式
                total_extraction_time = time.time() - extraction_start_time
                self.logger.info(f"⏱️ 总提取耗时: {total_extraction_time:.3f}秒")
                return parsed_response
            else:
                # 旧格式兼容
                total_extraction_time = time.time() - extraction_start_time
                self.logger.info(f"LLM要素提取完成: {list(parsed_response.keys())}、{list(parsed_response.values())}")
                self.logger.info(f"⏱️ 总提取耗时: {total_extraction_time:.3f}秒")
                return {'elements': parsed_response}

        except Exception as e:
            total_extraction_time = time.time() - extraction_start_time
            self.logger.error(f"LLM要素提取失败: {e}")
            self.logger.error(f"⏱️ 失败前提取耗时: {total_extraction_time:.3f}秒")
            # 降级到规则提取
            rule_start_time = time.time()
            result = self._rule_based_extraction(full_text, text_blocks, elements)
            rule_time = time.time() - rule_start_time
            self.logger.info(f"⏱️ 规则提取耗时: {rule_time:.3f}秒")
            return result
    
    def _build_extraction_prompt(self, full_text: str, elements: List[str], context_info: dict = None) -> str:
        """构建档案要素提取提示词（支持上下文感知）"""
        elements_str = "、".join(elements)

        # 构建上下文信息
        context_section = ""
        if context_info and context_info.get('previous_extractions'):
            context_section = "\n=== 已提取的要素信息（供参考） ===\n"
            for prev in context_info['previous_extractions']:
                context_section += f"页面{prev['page_index']}：\n"
                for element, info in prev['elements'].items():
                    if info.get('value'):
                        context_section += f"  {element}: {info['value']} (置信度: {info.get('confidence', 0):.2f})\n"
            context_section += "=== 当前页面分析 ===\n"

        prompt = f"""你是一个专业的档案信息提取专家。请从给定的OCR文本中准确提取指定的档案要素，并评估提取置信度和文档完整性。
{context_section}
当前页面OCR文本：
{full_text}

需要提取的要素：{elements_str}

请按照以下JSON格式返回结果：
{{
    "elements": {{
        "题名": {{
            "value": "提取到的题名或null",
            "confidence": 0.95
        }},
        "责任者": {{
            "value": "提取到的责任者或null",
            "confidence": 0.90
        }},
        "文号": {{
            "value": "提取到的文号或null",
            "confidence": 0.85
        }},
        "发文日期": {{
            "value": "提取到的发文日期或null",
            "confidence": 0.80
        }}
    }},
    "document_status": {{
        "content_complete": false,
        "page_type": "header_page",
        "reasoning": "判断依据说明"
    }}
}}

提取规则：
1. 题名：文档的标题或主题名称，通常在文档顶部；
2. 责任者：发文单位、机构或个人等署名，通常在文档顶部、末尾或特定位置，如果有多个责任者，用逗号“,”隔开
3. 文号：文档编号，通常包含"号"、"字"等标识
4. 发文日期：文档的发文或成文日期，通常在文档末尾和落款在一起，不能从正文行文中提取

置信度评估标准：
- 0.9-1.0：信息明确，格式标准，位置合理
- 0.7-0.9：信息较明确，但格式或位置略有疑问
- 0.5-0.7：信息存在但不够明确
- 0.0-0.5：信息不确定或不存在

内容是否完整标准：
- 按照文章内容是否已经结束、是否有落款和印章、要素是否完整判断文章内容是否完整填false或true

页面类型判断：
- header_page：包含标题、文号等头部信息
- content_page：主要内容页面
- signature_page：包含署名、日期等签发信息
- attachment_page：附件页面

要求：
- 只提取明确存在的信息，不要推测或编造
- 如果某个字段无法确定，value设为null，confidence设为0.0
- 保持原文的准确性，不要修改或美化
- 返回JSON格式，字段名使用中文
- 结合已提取信息，避免重复或冲突
- 文号格式标准化：将所有括号统一转换为中文六角括号〔〕
- 日期格式标准化：将所有日期统一转换为YYYYMMDD格式
"""
        return prompt
    
    def _parse_llm_response(self, response: str, elements: List[str]) -> Dict[str, Any]:
        """解析LLM响应（支持增强格式）"""
        try:
            # 要素名称映射（英文 -> 中文）
            element_mapping = {
                'title': '题名',
                'responsible_party': '责任者',
                'document_number': '文号',
                'issue_date': '发文日期'
            }

            # 尝试解析JSON
            if '{' in response and '}' in response:
                # 处理可能包含```json包装的情况
                if '```json' in response:
                    json_start = response.find('```json') + 7
                    json_end = response.find('```', json_start)
                    if json_end == -1:
                        json_end = response.rfind('}') + 1
                    json_str = response[json_start:json_end].strip()
                else:
                    json_start = response.find('{')
                    json_end = response.rfind('}') + 1
                    json_str = response[json_start:json_end]

                result = json.loads(json_str)

                # 检查是否为新的增强格式
                if 'elements' in result and 'document_status' in result:
                    # 新格式：包含置信度和文档状态
                    extracted = {}
                    element_details = {}

                    for element in elements:
                        # 获取中文要素名称
                        chinese_element = element_mapping.get(element, element)

                        if chinese_element in result['elements']:
                            element_info = result['elements'][chinese_element]
                            raw_value = element_info.get('value')
                            confidence = element_info.get('confidence', 0.0)
                            source_info = element_info.get('source_info', '提取')

                            # 对提取的值进行格式标准化
                            if raw_value and raw_value != 'null' and str(raw_value).strip():
                                extracted[element] = self._normalize_element_value(element, str(raw_value).strip())
                            else:
                                extracted[element] = None

                            # 保存详细信息
                            element_details[element] = {
                                'value': extracted[element],
                                'confidence': confidence,
                                'source_info': source_info
                            }
                        else:
                            extracted[element] = None
                            element_details[element] = {
                                'value': None,
                                'confidence': 0.0,
                                'source_info': '未找到'
                            }

                    # 返回增强结果
                    return {
                        'elements': extracted,
                        'element_details': element_details,
                        'document_status': result.get('document_status', {})
                    }
                else:
                    # 旧格式：兼容处理
                    extracted = {}
                    for element in elements:
                        # 获取中文要素名称
                        chinese_element = element_mapping.get(element, element)
                        # 从LLM响应中获取值
                        raw_value = result.get(chinese_element, None)

                        # 对提取的值进行格式标准化
                        if raw_value and raw_value.strip():
                            extracted[element] = self._normalize_element_value(element, raw_value.strip())
                        else:
                            extracted[element] = None

                    return {'elements': extracted}
            else:
                raise ValueError("响应中未找到JSON格式")

        except Exception as e:
            self.logger.error(f"LLM响应解析失败: {e}")
            self.logger.error(f"原始响应内容: {response[:1000]}...")  # 添加原始响应内容
            # 返回空结果
            return {'elements': {element: None for element in elements}}

    def _normalize_element_value(self, element: str, value: str) -> str:
        """对提取的要素值进行格式标准化"""
        import re

        if element == 'document_number' or element == '文号':
            # 文号格式标准化：将所有括号转换为中文六角括号〔〕
            value = self._normalize_document_number(value)
        elif element == 'issue_date' or element == '发文日期':
            # 日期格式标准化：转换为YYYYMMDD格式
            value = self._normalize_date_format(value)

        return value

    def _normalize_document_number(self, doc_number: str) -> str:
        """标准化文号格式：将所有括号转换为中文六角括号〔〕"""
        import re

        if not doc_number:
            return doc_number

        # 由于OCR可能导致括号不成对，需要按单个括号进行替换
        # 先处理左括号
        left_bracket_replacements = [
            (r'\[', '〔'),      # [ -> 〔
            (r'【', '〔'),      # 【 -> 〔
            (r'\(', '〔'),      # ( -> 〔
            (r'（', '〔'),      # （ -> 〔
            (r'\{', '〔'),      # { -> 〔
        ]

        # 再处理右括号
        right_bracket_replacements = [
            (r'\]', '〕'),      # ] -> 〕
            (r'】', '〕'),      # 】 -> 〕
            (r'\)', '〕'),      # ) -> 〕
            (r'）', '〕'),      # ） -> 〕
            (r'\}', '〕'),      # } -> 〕
        ]

        normalized = doc_number

        # 替换左括号
        for pattern, replacement in left_bracket_replacements:
            normalized = re.sub(pattern, replacement, normalized)

        # 替换右括号
        for pattern, replacement in right_bracket_replacements:
            normalized = re.sub(pattern, replacement, normalized)

        # 移除多余的空格
        normalized = re.sub(r'\s+', '', normalized)

        return normalized

    def _normalize_date_format(self, date_str: str) -> str:
        """标准化日期格式：转换为YYYYMMDD格式"""
        import re
        from datetime import datetime

        if not date_str:
            return date_str

        # 匹配各种日期格式
        date_patterns = [
            r'(\d{4})[年\-/.](\d{1,2})[月\-/.](\d{1,2})[日]?',  # 2024年1月1日, 2024-1-1, 2024/1/1, 2024.1.1
            r'(\d{4})(\d{2})(\d{2})',                           # 20240101
            r'(\d{1,2})[月\-/.](\d{1,2})[日]?[,，]\s*(\d{4})',  # 1月1日，2024
        ]

        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                groups = match.groups()
                if len(groups) == 3:
                    try:
                        if len(groups[0]) == 4:  # 年在前
                            year, month, day = groups
                        else:  # 年在后
                            month, day, year = groups

                        # 验证日期有效性
                        datetime(int(year), int(month), int(day))

                        # 返回YYYYMMDD格式
                        return f"{int(year):04d}{int(month):02d}{int(day):02d}"
                    except (ValueError, TypeError):
                        continue

        # 如果已经是YYYYMMDD格式，验证并返回
        if re.match(r'^\d{8}$', date_str):
            try:
                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                datetime(year, month, day)  # 验证日期有效性
                return date_str
            except (ValueError, TypeError):
                pass

        # 无法解析的日期格式，返回原值
        return date_str

    def _rule_based_extraction(self,
                             full_text: str, 
                             text_blocks: List[Dict[str, Any]], 
                             elements: List[str]) -> Dict[str, Any]:
        """基于规则的要素提取（降级方案）"""
        import re
        
        result = {}
        
        for element in elements:
            if element == "题名":
                # 提取标题（通常在文档开头，字体较大）
                lines = full_text.split('\n')
                for line in lines[:5]:  # 检查前5行
                    line = line.strip()
                    if len(line) > 5 and len(line) < 50:  # 合理的标题长度
                        result[element] = line
                        break
                else:
                    result[element] = None
                    
            elif element == "文号":
                # 提取文号（包含数字和特定格式）
                patterns = [
                    r'[〔\[](\d{4})[〕\]]\s*第?\s*(\d+)\s*号',
                    r'(\w+字)\s*[〔\[](\d{4})[〕\]]\s*第?\s*(\d+)\s*号',
                    r'(\w+)\s*(\d{4})\s*(\d+)\s*号'
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, full_text)
                    if match:
                        result[element] = match.group(0)
                        break
                else:
                    result[element] = None
                    
            elif element == "发文日期":
                # 提取日期
                date_patterns = [
                    r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
                    r'(\d{4})-(\d{1,2})-(\d{1,2})',
                    r'(\d{4})\.(\d{1,2})\.(\d{1,2})'
                ]
                
                for pattern in date_patterns:
                    match = re.search(pattern, full_text)
                    if match:
                        year, month, day = match.groups()
                        result[element] = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                        break
                else:
                    result[element] = None
                    
            elif element == "责任者":
                # 提取责任者（发文单位）
                # 通常在文档末尾或特定位置
                lines = full_text.split('\n')
                for line in reversed(lines[-10:]):  # 检查最后10行
                    line = line.strip()
                    if any(keyword in line for keyword in ['单位', '部门', '办公室', '委员会', '政府']):
                        result[element] = line
                        break
                else:
                    result[element] = None
            else:
                result[element] = None
        
        self.logger.info(f"规则提取完成: {list(result.keys())}")
        return result

    def _is_image_file(self, file_path: Path) -> bool:
        """检查文件是否为图像文件"""
        if not file_path.is_file():
            return False

        # 支持的图像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif'}
        return file_path.suffix.lower() in image_extensions

    def _analyze_archive_folder_structure(self, folder_path: Path) -> Dict[str, Any]:
        """
        分析档案文件夹结构

        Returns:
            {
                'type': 'file_level' | 'case_level_root' | 'case_level_nested',
                'image_groups': [
                    {
                        'case_name': str,           # 案件名称或根目录
                        'case_path': Path,          # 案件路径
                        'images': [Path, ...],      # 按文件名排序的图像列表
                        'primary_images': [Path, ...], # 前3张图像（用于题名、文号、责任者）
                        'secondary_images': [Path, ...] # 后3张图像（用于成文日期、责任者补充）
                    }
                ]
            }
        """
        self.logger.info(f"分析档案文件夹结构: {folder_path}")

        # 1. 检查根目录是否直接包含图像文件
        root_images = []
        subdirs = []

        for item in folder_path.iterdir():
            if item.is_file() and self._is_image_file(item):
                root_images.append(item)
            elif item.is_dir():
                subdirs.append(item)

        # 按文件名排序
        root_images.sort(key=lambda x: x.name)

        # 2. 判断档案结构类型
        if root_images and not subdirs:
            # 情况1：文件级档案 - 根目录直接包含图像，无子文件夹
            return {
                'type': 'file_level',
                'image_groups': [{
                    'case_name': 'root',
                    'case_path': folder_path,
                    'images': root_images,
                    'primary_images': root_images[:3],
                    'secondary_images': root_images[-3:] if len(root_images) > 3 else root_images
                }]
            }

        elif root_images and subdirs:
            # 情况2：案卷级档案 - 根目录有图像，优先使用根目录图像
            return {
                'type': 'case_level_root',
                'image_groups': [{
                    'case_name': 'root',
                    'case_path': folder_path,
                    'images': root_images,
                    'primary_images': root_images[:3],
                    'secondary_images': root_images[-3:] if len(root_images) > 3 else root_images
                }]
            }

        elif not root_images and subdirs:
            # 情况3：案卷级档案 - 无根目录图像，使用第一个子文件夹的图像
            first_subdir = subdirs[0]
            subdir_images = []

            for item in first_subdir.iterdir():
                if item.is_file() and self._is_image_file(item):
                    subdir_images.append(item)

            subdir_images.sort(key=lambda x: x.name)

            return {
                'type': 'case_level_nested',
                'image_groups': [{
                    'case_name': first_subdir.name,
                    'case_path': first_subdir,
                    'images': subdir_images,
                    'primary_images': subdir_images[:3],
                    'secondary_images': subdir_images[-3:] if len(subdir_images) > 3 else subdir_images
                }]
            }

        else:
            # 情况4：无有效图像文件
            return {
                'type': 'invalid',
                'image_groups': []
            }

    async def _extract_from_archive_folder(self,
                                         folder_path: Path,
                                         elements: List[str],
                                         confidence_threshold: float = 0.5,
                                         enable_stamp_processing: bool = False,
                                         stamp_confidence_threshold: float = 0.8) -> Dict[str, Any]:
        """
        从档案文件夹提取要素

        策略：
        1. 题名、文号：优先从前3张图像提取
        2. 责任者：先从前3张图像提取，如果没有则从后3张图像提取
        3. 成文日期：先从前3张图像提取，如果没有则从后3张图像提取
        """
        try:
            # 1. 分析文件夹结构
            structure = self._analyze_archive_folder_structure(folder_path)

            if structure['type'] == 'invalid' or not structure['image_groups']:
                return {
                    'success': False,
                    'error': f'文件夹中未找到有效的图像文件: {folder_path}',
                    'elements': {}
                }

            # 2. 获取第一个图像组（按照策略，只处理第一个案件）
            image_group = structure['image_groups'][0]
            primary_images = image_group['primary_images']
            secondary_images = image_group['secondary_images']

            self.logger.info(f"档案文件夹类型: {structure['type']}")
            self.logger.info(f"处理案件: {image_group['case_name']}")
            self.logger.info(f"主要图像: {len(primary_images)} 张")
            self.logger.info(f"次要图像: {len(secondary_images)} 张")

            # 3. 分阶段提取要素
            extracted_elements = {}

            # 阶段1：从前3张图像提取题名、文号、责任者
            primary_elements = await self._extract_from_primary_images(
                primary_images, elements, confidence_threshold,
                enable_stamp_processing, stamp_confidence_threshold
            )

            # 阶段2：如果责任者或成文日期未找到，从后3张图像补充提取
            if ('责任者' in elements and not primary_elements.get('责任者')) or \
               ('成文日期' in elements and not primary_elements.get('成文日期')):

                secondary_elements = await self._extract_from_secondary_images(
                    secondary_images, ['责任者', '成文日期'], confidence_threshold,
                    enable_stamp_processing, stamp_confidence_threshold
                )

                # 合并结果，优先使用主要图像的结果
                for element in elements:
                    if primary_elements.get(element):
                        extracted_elements[element] = primary_elements[element]
                    elif secondary_elements.get(element):
                        extracted_elements[element] = secondary_elements[element]
                    else:
                        extracted_elements[element] = None
            else:
                extracted_elements = primary_elements

            # 4. 构建返回结果
            return {
                'success': True,
                'elements': extracted_elements,
                'folder_type': structure['type'],
                'case_name': image_group['case_name'],
                'total_images': len(image_group['images']),
                'primary_images_count': len(primary_images),
                'secondary_images_count': len(secondary_images)
            }

        except Exception as e:
            self.logger.error(f"档案文件夹要素提取失败: {folder_path}, 错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'elements': {}
            }

    async def _extract_from_primary_images(self,
                                         primary_images: List[Path],
                                         elements: List[str],
                                         confidence_threshold: float,
                                         enable_stamp_processing: bool,
                                         stamp_confidence_threshold: float) -> Dict[str, str]:
        """
        从主要图像（前3张）提取要素
        优先提取：题名、文号、责任者
        """
        extracted_elements = {}

        for element in elements:
            extracted_elements[element] = None

        # 逐张图像提取，直到所有要素都找到或图像用完
        for image_path in primary_images:
            self.logger.debug(f"从主要图像提取要素: {image_path.name}")

            try:
                # 提取当前图像的要素
                result = await self.extract_from_image(
                    image_path=image_path,
                    elements=elements,
                    confidence_threshold=confidence_threshold,
                    enable_stamp_processing=enable_stamp_processing,
                    stamp_confidence_threshold=stamp_confidence_threshold
                )

                if result.get('success') and result.get('elements'):
                    # 更新未找到的要素
                    for element in elements:
                        if not extracted_elements[element] and result['elements'].get(element):
                            extracted_elements[element] = result['elements'][element]
                            self.logger.info(f"在图像 {image_path.name} 中找到 {element}: {result['elements'][element]}")

                # 检查是否所有要素都已找到
                if all(extracted_elements[element] for element in elements):
                    self.logger.info("所有要素已在主要图像中找到")
                    break

            except Exception as e:
                self.logger.warning(f"主要图像 {image_path.name} 处理失败: {e}")
                continue

        return extracted_elements

    async def _extract_from_secondary_images(self,
                                           secondary_images: List[Path],
                                           target_elements: List[str],
                                           confidence_threshold: float,
                                           enable_stamp_processing: bool,
                                           stamp_confidence_threshold: float) -> Dict[str, str]:
        """
        从次要图像（后3张）提取要素
        主要用于补充提取：责任者、成文日期
        """
        extracted_elements = {}

        for element in target_elements:
            extracted_elements[element] = None

        # 逐张图像提取，直到目标要素都找到或图像用完
        for image_path in secondary_images:
            self.logger.debug(f"从次要图像提取要素: {image_path.name}")

            try:
                # 提取当前图像的要素
                result = await self.extract_from_image(
                    image_path=image_path,
                    elements=target_elements,
                    confidence_threshold=confidence_threshold,
                    enable_stamp_processing=enable_stamp_processing,
                    stamp_confidence_threshold=stamp_confidence_threshold
                )

                if result.get('success') and result.get('elements'):
                    # 更新未找到的要素
                    for element in target_elements:
                        if not extracted_elements[element] and result['elements'].get(element):
                            extracted_elements[element] = result['elements'][element]
                            self.logger.info(f"在次要图像 {image_path.name} 中找到 {element}: {result['elements'][element]}")

                # 检查是否所有目标要素都已找到
                if all(extracted_elements[element] for element in target_elements):
                    self.logger.info("所有目标要素已在次要图像中找到")
                    break

            except Exception as e:
                self.logger.warning(f"次要图像 {image_path.name} 处理失败: {e}")
                continue

        return extracted_elements

    # ==================== 智能提取主流程 ====================

    async def smart_extract_elements(self,
                                   image_paths: List[Union[str, Path]],
                                   elements: Optional[List[str]] = None,
                                   config: Optional[ExtractionConfig] = None) -> Dict[str, Any]:
        """
        智能提取档案要素 - 基于文书结构分析的新方法

        Args:
            image_paths: 图像文件路径列表
            elements: 要提取的要素列表
            config: 提取配置

        Returns:
            提取结果字典
        """
        start_time = time.time()

        try:
            # 使用默认配置和要素
            if config is None:
                config = ExtractionConfig()
            if elements is None:
                elements = self.default_elements.copy()

            self.logger.info(f"开始智能提取档案要素，共{len(image_paths)}页")
            self.logger.info(f"目标要素: {elements}")
            self.logger.info(f"配置: 前{config.front_pages_count}页优先，智能结构分析: {config.enable_smart_structure_analysis}")

            # 1. 批量OCR识别所有页面
            pages_ocr_results = []
            for i, image_path in enumerate(image_paths):
                self.logger.debug(f"OCR识别第{i+1}页: {image_path}")

                ocr_result = await self._perform_ocr(
                    image_path=image_path,
                    confidence_threshold=config.min_confidence,
                    enable_stamp_processing=config.enable_stamp_processing,  # ✅ 使用配置参数
                    stamp_confidence_threshold=config.stamp_confidence_threshold
                )

                if not ocr_result.get('success'):
                    self.logger.warning(f"第{i+1}页OCR失败: {ocr_result.get('error')}")
                    continue

                pages_ocr_results.append(ocr_result)

            if not pages_ocr_results:
                return {
                    'success': False,
                    'error': '所有页面OCR识别失败',
                    'elements': {}
                }

            # 2. 智能结构分析（如果启用）
            if config.enable_smart_structure_analysis:
                # 过滤出主文书页面（排除附件）
                main_document_pages = self.filter_main_document_pages(pages_ocr_results, config)

                # 页面分类
                page_classifications = await self.classify_document_pages(main_document_pages, config)

                self.logger.info(f"文书结构分析完成，主文书{len(main_document_pages)}页")
                for classification in page_classifications:
                    self.logger.debug(f"第{classification.page_index+1}页: {classification.page_type.value} "
                                    f"(置信度: {classification.confidence:.2f})")
            else:
                # 不启用智能分析，使用所有页面
                main_document_pages = pages_ocr_results
                page_classifications = None

            # 3. 按策略提取各要素
            extracted_elements = {}

            for element in elements:
                extracted_value = await self._extract_element_by_strategy(
                    element, main_document_pages, page_classifications, config
                )
                extracted_elements[element] = extracted_value

            processing_time = time.time() - start_time

            return {
                'success': True,
                'elements': extracted_elements,
                'processing_time': processing_time,
                'pages_processed': len(pages_ocr_results),
                'main_document_pages': len(main_document_pages),
                'structure_analysis_enabled': config.enable_smart_structure_analysis
            }

        except Exception as e:
            self.logger.error(f"智能提取失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'elements': {}
            }

    async def _extract_element_by_strategy(self,
                                         element: str,
                                         pages_ocr_results: List[Dict[str, Any]],
                                         page_classifications: Optional[List[PageClassificationResult]],
                                         config: ExtractionConfig) -> Optional[str]:
        """
        按策略提取特定要素

        Args:
            element: 要素名称
            pages_ocr_results: 页面OCR结果
            page_classifications: 页面分类结果（可选）
            config: 提取配置

        Returns:
            提取到的要素值
        """
        # 获取该要素的提取策略
        strategies = self._get_strategies_for_element(element, config)

        for strategy in strategies:
            try:
                if strategy == "front_pages":
                    # 从前N页提取
                    front_pages = pages_ocr_results[:config.front_pages_count]
                    result = await self._extract_from_pages(element, front_pages)

                elif strategy == "signature_page" and page_classifications:
                    # 从签发页提取
                    signature_pages = [
                        pages_ocr_results[cls.page_index]
                        for cls in page_classifications
                        if cls.page_type == DocumentPageType.SIGNATURE_PAGE
                    ]
                    if signature_pages:
                        result = await self._extract_from_pages(element, signature_pages)
                    else:
                        # 如果没有识别到签发页，使用最后一页
                        result = await self._extract_from_pages(element, [pages_ocr_results[-1]])

                elif strategy == "header_page" and page_classifications:
                    # 从首页提取
                    header_pages = [
                        pages_ocr_results[cls.page_index]
                        for cls in page_classifications
                        if cls.page_type == DocumentPageType.HEADER_PAGE
                    ]
                    if header_pages:
                        result = await self._extract_from_pages(element, header_pages)
                    else:
                        # 如果没有识别到首页，使用第一页
                        result = await self._extract_from_pages(element, [pages_ocr_results[0]])

                else:
                    # 默认策略：从所有页面提取
                    result = await self._extract_from_pages(element, pages_ocr_results)

                if result:
                    self.logger.info(f"使用策略 {strategy} 成功提取 {element}: {result}")
                    return result

            except Exception as e:
                self.logger.warning(f"策略 {strategy} 提取 {element} 失败: {e}")
                continue

        self.logger.warning(f"所有策略都无法提取 {element}")
        return None

    def _get_strategies_for_element(self, element: str, config: ExtractionConfig) -> List[str]:
        """获取要素的提取策略列表"""
        element_lower = element.lower()

        if "题名" in element or "标题" in element:
            return config.title_priority
        elif "责任者" in element or "发文单位" in element:
            return config.responsible_party_priority
        elif "文号" in element or "发文字号" in element:
            return config.document_number_priority
        elif "日期" in element or "成文日期" in element:
            return config.issue_date_priority
        else:
            # 默认策略
            return ["front_pages", "signature_page", "header_page"]

    async def _extract_from_pages(self, element: str, pages_ocr_results: List[Dict[str, Any]]) -> Optional[str]:
        """从指定页面列表中提取要素"""
        for page_result in pages_ocr_results:
            full_text = page_result.get('full_text', '')
            text_blocks = page_result.get('text_blocks', [])

            # 使用现有的LLM提取方法
            extraction_result = await self._extract_elements(full_text, text_blocks, [element])

            if extraction_result.get('success') and extraction_result.get('elements', {}).get(element):
                return extraction_result['elements'][element]

        return None

    async def extract_batch(self,
                                      #excel_data: List[Dict[str, Any]],
                                      image_files: List[Union[str, Path]],
                                      image_names: List[str],
                                      elements: Optional[List[str]] = None,
                                      confidence_threshold: float = 0.5,
                                      similarity_threshold: float = 0.8,
                                      enable_stamp_processing: bool = False,
                                      stamp_confidence_threshold: float = 0.8,
                                      enable_preprocessing: bool = True,
                                      early_stop_threshold: float = 0.9) -> Dict[str, Any]:
        """
        批量提取档案要素 - 优化版本

        Args:
            excel_data: Excel数据列表，每个元素包含一行数据
            image_files: 图像文件路径列表（可能包含文件夹路径）
            elements: 要提取的要素列表
            confidence_threshold: OCR置信度阈值
            similarity_threshold: 相似度阈值
            enable_stamp_processing: 是否启用印章检测
            stamp_confidence_threshold: 印章检测置信度阈值
            enable_preprocessing: 是否启用图像预处理
            early_stop_threshold: 要素提取完整性阈值，达到此阈值时停止处理剩余图像

        Returns:
            比对结果，包含错误列表和统计信息
        """
        start_time = time.time()

        try:
            if elements is None:
                elements = self.default_elements.copy()

            self.logger.info(f"开始批量档案要素提取（优化版本）")
            #self.logger.info(f"Excel数据: {len(excel_data)} 行")
            self.logger.info(f"输入路径: {len(image_files)} 个")
            self.logger.info(f"目标要素: {elements}")
            self.logger.info(f"启用图像预处理: {enable_preprocessing}")
            self.logger.info(f"早停阈值: {early_stop_threshold}")

            # 1. 智能处理输入路径（支持早停优化）
            processed_items = []
            accumulated_elements = {}  # 累积提取的要素
            processing_stats = {
                'total_images': 0,
                'processed_images': 0,
                'skipped_images': 0,
                'preprocessing_time': 0,
                'ocr_time': 0,
                'early_stopped': False
            }

            for i, item_path in enumerate(image_files):
                item_path = Path(item_path)

                if item_path.is_dir():
                    # 处理文件夹：分析档案结构并提取要素
                    self.logger.info(f"处理档案文件夹: {item_path}")
                    # 处理文件夹 - 每个文件夹独立进行早停判断
                    folder_result = await self._extract_from_archive_folder_optimized(
                        folder_path=item_path,
                        image_names=image_names,
                        elements=elements,
                        confidence_threshold=confidence_threshold,
                        enable_stamp_processing=enable_stamp_processing,
                        stamp_confidence_threshold=stamp_confidence_threshold,
                        enable_preprocessing=enable_preprocessing,
                        accumulated_elements=None,  # 文件夹内部独立早停，不使用全局累积
                        early_stop_threshold=early_stop_threshold,
                        use_enhanced_extraction=True  # 启用增强提取
                    )
                    processed_items.append({
                        'index': i,
                        'type': 'folder',
                        'path': str(item_path),
                        'filename': item_path.name,
                        'extraction_result': folder_result
                    })

                    # 更新累积要素
                    if folder_result.get('success') and folder_result.get('elements'):
                        self._update_accumulated_elements(accumulated_elements, folder_result['elements'])

                elif item_path.is_file() and self._is_image_file(item_path):
                    processing_stats['total_images'] += 1

                    # 检查是否需要早停
                    if self._should_early_stop(accumulated_elements, elements, early_stop_threshold):
                        self.logger.info(f"要素提取完整性达到{early_stop_threshold:.1%}，跳过剩余图像: {item_path}")
                        processing_stats['skipped_images'] += 1
                        processing_stats['early_stopped'] = True
                        processed_items.append({
                            'index': i,
                            'type': 'file',
                            'path': str(item_path),
                            'filename': item_path.name,
                            'extraction_result': {
                                'success': True,
                                'elements': {},
                                'skipped': True,
                                'reason': 'early_stop_optimization',
                                'processing_time': 0
                            }
                        })
                        continue

                    # 处理单个图像文件（带预处理优化）
                    self.logger.info(f"处理图像文件: {item_path}")
                    processing_stats['processed_images'] += 1

                    result = await self.extract_from_image_optimized(
                        image_path=item_path,
                        elements=elements,
                        confidence_threshold=confidence_threshold,
                        enable_stamp_processing=enable_stamp_processing,
                        stamp_confidence_threshold=stamp_confidence_threshold,
                        enable_preprocessing=enable_preprocessing
                    )

                    # 更新处理统计
                    if 'preprocessing_time' in result:
                        processing_stats['preprocessing_time'] += result['preprocessing_time']
                    if 'ocr_time' in result:
                        processing_stats['ocr_time'] += result['ocr_time']

                    processed_items.append({
                        'index': i,
                        'type': 'file',
                        'path': str(item_path),
                        'filename': item_path.name,
                        'extraction_result': result
                    })

                    # 更新累积要素
                    if result.get('success') and result.get('elements'):
                        self._update_accumulated_elements(accumulated_elements, result['elements'])

                else:
                    # 无效路径或非图像文件
                    self.logger.warning(f"跳过无效路径: {item_path}")
                    processed_items.append({
                        'index': i,
                        'type': 'invalid',
                        'path': str(item_path),
                        'filename': item_path.name,
                        'extraction_result': {
                            'success': False,
                            'error': f'无效路径或非图像文件: {item_path}'
                        }
                    })
            self.logger.debug(f"抽取结果processed_items：{processed_items}")

            # 2. 执行比对分析
            # comparison_result = await self._compare_with_excel(
            #     excel_data, extraction_results, elements, similarity_threshold
            # )

            total_time = time.time() - start_time
            self.logger.info(f"批量提取和比对完成，总耗时: {total_time:.2f}秒")

            # 记录优化统计
            if processing_stats['early_stopped']:
                self.logger.info(f"🚀 早停优化生效: 跳过 {processing_stats['skipped_images']} 个图像")
            if processing_stats['preprocessing_time'] > 0:
                self.logger.info(f"📸 图像预处理总耗时: {processing_stats['preprocessing_time']:.2f}秒")
            if processing_stats['ocr_time'] > 0:
                self.logger.info(f"🔍 OCR识别总耗时: {processing_stats['ocr_time']:.2f}秒")

            return {
                'success': True,
                'total_processing_time': total_time,
                'extraction_count': len(processed_items),
                #'excel_count': len(excel_data),
                #'comparison_result': comparison_result,
                'processed_items': processed_items,
                'optimization_stats': processing_stats
            }

        except Exception as e:
            self.logger.error(f"批量提取和比对失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_processing_time': time.time() - start_time
            }

    def _update_accumulated_elements(self, accumulated_elements: Dict[str, Any], new_elements: Dict[str, Any]):
        """更新累积的要素提取结果"""
        for element_name, element_value in new_elements.items():
            if element_value and element_value.strip():  # 只保存非空值
                if element_name not in accumulated_elements:
                    accumulated_elements[element_name] = []

                # 避免重复添加相同的值
                if element_value not in accumulated_elements[element_name]:
                    accumulated_elements[element_name].append(element_value)

    def _update_accumulated_elements_enhanced(self, accumulated_elements: Dict, new_elements: Dict, page_index: int):
        """增强的要素累积（考虑置信度和一致性）"""

        for element_name, element_info in new_elements.items():
            if element_info.get('value') and element_info.get('confidence', 0) > 0:

                if element_name not in accumulated_elements:
                    accumulated_elements[element_name] = []

                # 添加页面信息
                element_record = {
                    'value': element_info['value'],
                    'confidence': element_info['confidence'],
                    'source_info': element_info.get('source_info', ''),
                    'page_index': page_index,
                    'normalized_value': self._normalize_element_value(element_name, element_info['value'])
                }

                # 检查是否与已有要素冲突
                conflict_detected = self._detect_element_conflict(accumulated_elements[element_name], element_record)

                if not conflict_detected:
                    accumulated_elements[element_name].append(element_record)
                else:
                    # 处理冲突：保留高置信度的，或标记为需要人工确认
                    self._handle_element_conflict(accumulated_elements[element_name], element_record)

    def _detect_element_conflict(self, existing_elements: List[Dict], new_element: Dict) -> bool:
        """检测要素冲突"""
        if not existing_elements:
            return False

        new_normalized = new_element['normalized_value']

        for existing in existing_elements:
            existing_normalized = existing.get('normalized_value', existing['value'])

            # 如果标准化后的值不同，则认为有冲突
            if new_normalized != existing_normalized:
                return True

        return False

    def _handle_element_conflict(self, existing_elements: List[Dict], new_element: Dict):
        """处理要素冲突"""
        # 简单策略：保留置信度最高的
        max_confidence_idx = 0
        max_confidence = existing_elements[0]['confidence']

        for i, element in enumerate(existing_elements):
            if element['confidence'] > max_confidence:
                max_confidence = element['confidence']
                max_confidence_idx = i

        # 如果新要素置信度更高，替换最高置信度的要素
        if new_element['confidence'] > max_confidence:
            existing_elements[max_confidence_idx] = new_element
            self.logger.info(f"要素冲突处理：用高置信度值替换 {new_element['value']} (置信度: {new_element['confidence']:.2f})")
        else:
            self.logger.info(f"要素冲突处理：保留现有高置信度值，忽略 {new_element['value']} (置信度: {new_element['confidence']:.2f})")

    def _select_best_elements(self, accumulated_elements: Dict, target_elements: List[str]) -> Dict:
        """选择最优要素值"""
        final_elements = {}

        for element in target_elements:
            if element in accumulated_elements and accumulated_elements[element]:
                # 多因素评分选择最佳值
                best_element = self._score_and_select_best_element(accumulated_elements[element])
                final_elements[element] = best_element['normalized_value']
            else:
                final_elements[element] = ""

        return final_elements

    def _score_and_select_best_element(self, element_candidates: List[Dict]) -> Dict:
        """多因素评分选择最佳要素"""

        for candidate in element_candidates:
            score = 0

            # 置信度权重 (40%)
            score += candidate.get('confidence', 0) * 0.4

            # 页面类型权重 (30%)
            source_info = candidate.get('source_info', '').lower()
            if 'header' in source_info or '头部' in source_info or '顶部' in source_info:
                score += 0.3  # 头部页面的题名、文号更可信
            elif 'signature' in source_info or '署名' in source_info or '签发' in source_info or '末尾' in source_info:
                score += 0.25  # 签发页面的责任者、日期更可信

            # 格式规范性权重 (20%)
            element_type = self._infer_element_type(candidate.get('value', ''))
            if self._is_format_standard(candidate.get('value', ''), element_type):
                score += 0.2

            # 一致性权重 (10%)
            consistency_score = self._calculate_consistency_score(candidate, element_candidates)
            score += consistency_score * 0.1

            candidate['final_score'] = score

        return max(element_candidates, key=lambda x: x.get('final_score', 0))

    def _infer_element_type(self, value: str) -> str:
        """推断要素类型"""
        if not value:
            return 'unknown'

        import re
        if '号' in value:
            return '文号'
        elif re.match(r'^\d{8}$', value):
            return '发文日期'
        elif any(keyword in value for keyword in ['通知', '决定', '意见', '办法', '规定']):
            return '题名'
        else:
            return 'unknown'

    async def _preprocess_image_with_stamp_processing(self, image_path: str, stamp_confidence_threshold: float = 0.5) -> str:
        """
        使用image_preprocessor进行印章预处理

        Args:
            image_path: 原始图像路径
            stamp_confidence_threshold: 印章检测置信度阈值

        Returns:
            str: 预处理后的图像路径
        """
        try:
            self.logger.info(f"🎯 开始印章预处理: {Path(image_path).name}")

            # 1. 检查模型池
            if not hasattr(self, 'model_pool') or self.model_pool is None:
                self.logger.warning("模型池未初始化，跳过印章处理")
                return image_path

            # 2. 获取或创建图像预处理器
            from ..utils.image_preprocessor import get_image_processor

            # 获取现有的预处理配置，保留所有配置字段
            preprocessing_config = self.model_pool.config.get('image_preprocessing', {}).copy()

            # 获取现有的stamp_processing配置，保留所有配置字段（如color_separation_method等）
            existing_stamp_config = preprocessing_config.get('stamp_processing', {})

            # 只更新必要的字段，保留其他配置
            existing_stamp_config.update({
                'enabled': True,
                'confidence_threshold': stamp_confidence_threshold,
                'create_multiple_versions': True,
                'remove_red_blue_channels': True
            })

            preprocessing_config['stamp_processing'] = existing_stamp_config

            # 设置其他预处理配置（如果不存在的话）
            preprocessing_config.setdefault('target_long_side', 800)  # 缩放到长边800像素
            preprocessing_config.setdefault('jpeg_quality', 95)
            preprocessing_config.setdefault('enhance_contrast', 1.1)
            preprocessing_config.setdefault('enhance_sharpness', 1.1)
            preprocessing_config.setdefault('enable_denoising', False)  # 避免模糊文字

            # 创建临时图像预处理器
            temp_processor = get_image_processor(
                preprocessing_config,
                self.model_pool,
                force_reinit=True
            )

            # 3. 执行图像预处理（包含印章检测和处理）
            processed_data, processing_stats = temp_processor.process_image(
                image_path,
                output_format='JPEG'
            )

            # 4. 检查是否检测到印章 - 从processing_stats中获取印章信息
            stamp_info = processing_stats.get('stamp_processing', {})
            detected_count = stamp_info.get('detected_stamps', 0)

            self.logger.info(f"📊 印章检测结果: 检测到 {detected_count} 个印章")

            # 5. 如果没有检测到印章，返回原图
            if detected_count == 0:
                self.logger.info("未检测到印章，使用原图")
                return image_path

            # 6. 保存预处理后的图像到临时文件
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(
                suffix='_stamp_processed.jpg',
                delete=False,
                dir=Path(image_path).parent
            )
            temp_file.write(processed_data)
            temp_file.close()

            self.logger.info(
                f"✅ 印章预处理完成: {Path(temp_file.name).name} "
                f"(检测到 {detected_count} 个印章)"
            )

            return temp_file.name

        except Exception as e:
            self.logger.error(f"印章预处理异常: {e}")
            return image_path



    async def _extract_from_archive_folder_enhanced(self, folder_path: str, image_names:List[str], elements: List[str],
                                                   early_stop_config: Dict = None,
                                                   enable_stamp_processing: bool = False,
                                                   stamp_confidence_threshold: float = 0.8) -> Dict[str, Any]:
        """增强版档案文件夹要素提取（支持上下文感知和智能早停）"""

        enhanced_start_time = time.time()
        self.logger.info(f"🚀 开始增强版档案文件夹提取: {folder_path}")

        if early_stop_config is None:
            early_stop_config = {
                'confidence_threshold': 0.9,
                'basic_threshold': 0.75,
                'attachment_threshold': 0.5,
                'max_pages': 20
            }

        self.logger.info(f"开始增强版档案要素提取: {folder_path}")

        # 获取指定的图片文件（使用分件后的image_names列表）
        folder_path = Path(folder_path)
        image_files = []

        if image_names:
            # 使用传入的image_names列表获取具体的图片文件
            self.logger.info(f"使用指定的图片文件列表，共{len(image_names)}个文件")
            for image_name in image_names:
                image_path = folder_path / image_name
                if image_path.exists() and image_path.is_file():
                    image_files.append(image_path)
                    self.logger.debug(f"添加图片文件: {image_path}")
                else:
                    self.logger.warning(f"指定的图片文件不存在: {image_path}")
        else:
            # 如果没有指定image_names，则获取文件夹下所有图片文件（兼容旧逻辑）
            self.logger.info("未指定图片文件列表，获取文件夹下所有图片文件")
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                image_files.extend(folder_path.glob(f'*{ext}'))
                image_files.extend(folder_path.glob(f'*{ext.upper()}'))
            image_files.sort()  # 按文件名排序

        if not image_files:
            self.logger.warning(f"文件夹中未找到图片文件: {folder_path}")
            return {element: "" for element in elements}

        # 初始化累积要素存储（新格式：支持多个候选值）
        accumulated_elements = {}
        extraction_history = []  # 提取历史记录

        total_pages = len(image_files)
        processed_pages = 0

        for page_index, image_file in enumerate(image_files):
            if processed_pages >= early_stop_config.get('max_pages', 20):
                self.logger.info(f"达到最大页面处理限制: {early_stop_config['max_pages']}")
                break

            try:
                self.logger.info(f"处理页面 {page_index + 1}/{total_pages}: {image_file}")

                # 构建上下文信息
                context_info = {
                    'previous_extractions': extraction_history[-3:],  # 最近3页的提取结果
                    'current_page': page_index + 1,
                    'total_pages': total_pages
                } if extraction_history else None

                # 🎯 调整1: 先进行印章预处理（如果启用）
                processed_image_path = str(image_file)
                if enable_stamp_processing:
                    processed_image_path = await self._preprocess_image_with_stamp_processing(
                        str(image_file), stamp_confidence_threshold
                    )

                # 🎯 调整2: 使用预处理后的图像进行OCR
                ocr_result = await self._perform_ocr(
                    image_path=processed_image_path,
                    confidence_threshold=0.5,
                    enable_stamp_processing=False,  # 已经预处理过，OCR阶段不再处理
                    stamp_confidence_threshold=stamp_confidence_threshold
                )
                if not ocr_result or not ocr_result.get('full_text'):
                    self.logger.warning(f"OCR识别失败或结果为空: {image_file}")
                    continue

                full_text = ocr_result['full_text']
                text_blocks = ocr_result.get('text_blocks', [])

                # 使用增强的LLM提取（支持上下文）
                extraction_result = await self._extract_elements(full_text, text_blocks, elements, context_info)

                # 处理提取结果
                if 'element_details' in extraction_result:
                    # 新格式：包含置信度信息
                    element_details = extraction_result['element_details']
                    document_status = extraction_result.get('document_status', {})

                    # 更新累积要素
                    self._update_accumulated_elements_enhanced(accumulated_elements, element_details, page_index)

                    # 记录提取历史
                    extraction_history.append({
                        'page_index': page_index + 1,
                        'elements': element_details,
                        'document_status': document_status
                    })

                    # 🎯 调整3: 检查early_stop条件（基于完整要素）
                    # 此时已经完成了印章预处理和OCR，可以基于完整要素进行early_stop判断
                    early_stop_result = self._should_early_stop_enhanced(
                        accumulated_elements, elements, document_status, early_stop_config
                    )

                    if early_stop_result['should_stop']:
                        self.logger.info(f"✅ 智能早停触发: {early_stop_result['reason']}")
                        self.logger.info(f"📊 完整性统计: {early_stop_result['completeness']}")
                        self.logger.info("🎯 印章处理已在文件夹级别完成，跳过后续单文件处理")
                        break

                else:
                    # 旧格式兼容处理
                    extracted_elements = extraction_result.get('elements', {})
                    self._update_accumulated_elements(accumulated_elements, extracted_elements)

                    # 简单早停判断
                    if self._should_early_stop(accumulated_elements, elements, 0.8):
                        self.logger.info("传统早停触发")
                        break

                processed_pages += 1

            except Exception as e:
                self.logger.error(f"处理页面失败 {image_file}: {e}")
                continue

        # 选择最优要素值
        selection_start_time = time.time()
        if any(accumulated_elements.values()):
            final_elements = self._select_best_elements(accumulated_elements, elements)
            selection_time = time.time() - selection_start_time
            total_enhanced_time = time.time() - enhanced_start_time

            self.logger.info(f"增强版要素提取完成，处理页面: {processed_pages}/{total_pages}")
            self.logger.info(f"最终要素: {final_elements}")
            self.logger.info(f"⏱️ 要素选择耗时: {selection_time:.3f}秒")
            self.logger.info(f"⏱️ 增强版总耗时: {total_enhanced_time:.3f}秒")
            return final_elements
        else:
            total_enhanced_time = time.time() - enhanced_start_time
            self.logger.warning("未提取到任何有效要素")
            self.logger.warning(f"⏱️ 增强版总耗时: {total_enhanced_time:.3f}秒")
            return {element: "" for element in elements}

    def _is_format_standard(self, value: str, element_type: str = None) -> bool:
        """检查格式是否标准"""
        if not value:
            return False

        import re

        # 文号格式检查
        if element_type in ['文号', 'document_number'] or '号' in value:
            # 检查是否包含标准的中文六角括号
            return '〔' in value and '〕' in value

        # 日期格式检查
        if element_type in ['发文日期', 'issue_date'] or re.match(r'^\d{8}$', value):
            # 检查是否为YYYYMMDD格式
            return re.match(r'^\d{8}$', value) is not None

        return True  # 其他情况默认为标准

    def _calculate_consistency_score(self, candidate: Dict, all_candidates: List[Dict]) -> float:
        """计算一致性得分"""
        if len(all_candidates) <= 1:
            return 1.0

        # 简单的一致性检查：如果多个候选值相同，得分更高
        same_value_count = sum(1 for c in all_candidates if c['normalized_value'] == candidate['normalized_value'])
        return same_value_count / len(all_candidates)

    def _should_early_stop(self, accumulated_elements: Dict[str, Any], target_elements: List[str], threshold: float) -> bool:
        """判断是否应该早停（要素提取完整性检查）"""
        if not target_elements or threshold >= 1.0:
            return False

        # 计算已提取要素的完整性
        extracted_count = 0
        for element in target_elements:
            if element in accumulated_elements and accumulated_elements[element]:
                extracted_count += 1

        completeness = extracted_count / len(target_elements)
        return completeness >= threshold

    def _calculate_extraction_completeness(self, accumulated_elements: Dict[str, List], target_elements: List[str], min_confidence: float = 0.7) -> Dict:
        """计算提取完整性（考虑置信度）"""
        completeness_info = {
            'total_elements': len(target_elements),
            'extracted_elements': 0,
            'high_confidence_elements': 0,
            'element_details': {},
            'overall_completeness': 0.0,
            'confidence_completeness': 0.0
        }

        for element in target_elements:
            if element in accumulated_elements and accumulated_elements[element]:
                # 找到最高置信度的值
                best_extraction = max(accumulated_elements[element], key=lambda x: x.get('confidence', 0))
                completeness_info['element_details'][element] = best_extraction

                if best_extraction['confidence'] > 0:
                    completeness_info['extracted_elements'] += 1

                if best_extraction['confidence'] >= min_confidence:
                    completeness_info['high_confidence_elements'] += 1

        completeness_info['overall_completeness'] = completeness_info['extracted_elements'] / len(target_elements)
        completeness_info['confidence_completeness'] = completeness_info['high_confidence_elements'] / len(target_elements)

        return completeness_info

    def _should_early_stop_enhanced(self, accumulated_elements: Dict, target_elements: List[str],
                                   document_status: Dict, early_stop_config: Dict) -> Dict:
        """增强的早停判断"""

        # 1. 计算完整性
        completeness = self._calculate_extraction_completeness(accumulated_elements, target_elements)

        # 2. 文档状态判断
        content_complete = document_status.get('content_complete', False)
        page_type = document_status.get('page_type', 'unknown')

        # 3. 早停条件判断
        should_stop = False
        stop_reason = ""

        # 条件1：高置信度完整性达到阈值
        if completeness['confidence_completeness'] >= early_stop_config.get('confidence_threshold', 0.9):
            should_stop = True
            stop_reason = f"高置信度要素完整性达到{completeness['confidence_completeness']:.1%}"

        # 条件2：文档内容已完整且基本要素齐全
        elif content_complete and completeness['overall_completeness'] >= early_stop_config.get('basic_threshold', 0.75):
            should_stop = True
            stop_reason = f"文档内容完整，基本要素完整性{completeness['overall_completeness']:.1%}"

        # 条件3：遇到附件页且主要要素已提取
        elif page_type == 'attachment_page' and completeness['overall_completeness'] >= early_stop_config.get('attachment_threshold', 0.5):
            should_stop = True
            stop_reason = f"遇到附件页，主要要素已提取{completeness['overall_completeness']:.1%}"

        return {
            'should_stop': should_stop,
            'reason': stop_reason,
            'completeness': completeness,
            'document_status': document_status
        }

    async def extract_from_image_optimized(self,
                                         image_path: Union[str, Path],
                                         elements: Optional[List[str]] = None,
                                         confidence_threshold: float = 0.5,
                                         enable_stamp_processing: bool = False,
                                         stamp_confidence_threshold: float = 0.8,
                                         enable_preprocessing: bool = True) -> Dict[str, Any]:
        """
        优化版本的图像要素提取 - 支持图像预处理
        """
        start_time = time.time()
        preprocessing_time = 0
        ocr_time = 0

        try:
            if elements is None:
                elements = self.default_elements.copy()

            self.logger.info(f"开始优化提取档案要素: {image_path}")

            # 步骤1: 图像预处理（如果启用）
            processed_image_path = image_path
            if enable_preprocessing:
                preprocessing_start = time.time()
                processed_image_path = await self._preprocess_image(
                    image_path, enable_stamp_processing, stamp_confidence_threshold
                )
                preprocessing_time = time.time() - preprocessing_start
                self.logger.info(f"图像预处理完成，耗时: {preprocessing_time:.2f}秒")

            # 步骤2: OCR文本识别
            ocr_start = time.time()
            ocr_result = await self._perform_ocr(
                processed_image_path,
                confidence_threshold,
                enable_stamp_processing,
                stamp_confidence_threshold
            )
            ocr_time = time.time() - ocr_start

            # 保存OCR结果供后续使用
            self._last_ocr_result = ocr_result

            if not ocr_result['success']:
                return {
                    'success': False,
                    'error': f"OCR识别失败: {ocr_result.get('error', '未知错误')}",
                    'processing_time': time.time() - start_time,
                    'preprocessing_time': preprocessing_time,
                    'ocr_time': ocr_time
                }

            self.logger.info(f"OCR完成，耗时: {ocr_time:.2f}秒")

            # 步骤3: 智能要素提取
            extraction_result = await self._extract_elements(
                ocr_result['full_text'],
                ocr_result['text_blocks'],
                elements
            )

            # 组装最终结果
            result = {
                'success': True,
                'elements': extraction_result,
                'ocr_info': {
                    'total_blocks': len(ocr_result['text_blocks']),
                    'full_text_length': len(ocr_result['full_text']),
                    'confidence_threshold': confidence_threshold
                },
                'processing_time': time.time() - start_time,
                'preprocessing_time': preprocessing_time,
                'ocr_time': ocr_time
            }

            self.logger.info(f"优化档案要素提取完成，总耗时: {result['processing_time']:.2f}秒")
            return result

        except Exception as e:
            self.logger.error(f"优化档案要素提取失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time,
                'preprocessing_time': preprocessing_time,
                'ocr_time': ocr_time
            }

    async def _preprocess_image(self, image_path: Union[str, Path],
                              enable_stamp_processing: bool = False,
                              stamp_confidence_threshold: float = 0.8) -> Union[str, Path]:
        """
        图像预处理 - 参考web_service中的实现
        """
        try:
            # 检查是否启用预处理
            if not self.model_pool.config.get('image_preprocessing', {}).get('enabled', True):
                self.logger.info("图像预处理已禁用，使用原始图像")
                return image_path

            # 读取图像文件
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 动态配置预处理参数
            preprocessing_config = self.model_pool.config.get('image_preprocessing', {}).copy()

            # 根据参数动态设置印章处理
            if enable_stamp_processing:
                # 获取现有的stamp_processing配置，保留所有配置字段
                existing_stamp_config = preprocessing_config.get('stamp_processing', {})

                # 只更新必要的字段，保留其他配置（如color_separation_method等）
                existing_stamp_config.update({
                    'enabled': True,
                    'confidence_threshold': stamp_confidence_threshold,
                    'create_multiple_versions': True,
                    'enable_stats': True,
                })

                preprocessing_config['stamp_processing'] = existing_stamp_config
                self.logger.info(f"启用印章处理，置信度阈值: {stamp_confidence_threshold}")
                self.logger.info(f"使用模型: {existing_stamp_config.get('model_name', 'N/A')}")
                self.logger.info(f"颜色分离方法: {existing_stamp_config.get('color_separation_method', 'N/A')}")
            else:
                # 只禁用印章处理，保留其他配置
                if 'stamp_processing' in preprocessing_config:
                    preprocessing_config['stamp_processing']['enabled'] = False
                else:
                    preprocessing_config['stamp_processing'] = {'enabled': False}

            # 获取图像处理器
            from ..utils.image_preprocessor import get_image_processor
            temp_processor = get_image_processor(preprocessing_config, self.model_pool, force_reinit=True)

            # 执行预处理
            preprocessed_data, preprocessing_stats = temp_processor.process_image(
                image_data,
                output_format='JPEG'
            )

            # 保存预处理后的图像到临时文件
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
            temp_file.write(preprocessed_data)
            temp_file.close()

            self.logger.info(
                f"图像预处理完成: "
                f"{preprocessing_stats.get('original_size', 'N/A')} -> "
                f"{preprocessing_stats.get('processed_size', 'N/A')}"
            )

            return temp_file.name

        except Exception as e:
            self.logger.warning(f"图像预处理失败，使用原始图像: {e}")
            return image_path

    async def _extract_from_archive_folder_optimized(self,
                                                   folder_path: Path,
                                                   image_names: List[str],
                                                   elements: Optional[List[str]] = None,
                                                   confidence_threshold: float = 0.5,
                                                   enable_stamp_processing: bool = False,
                                                   stamp_confidence_threshold: float = 0.8,
                                                   enable_preprocessing: bool = True,
                                                   accumulated_elements: Optional[Dict[str, Any]] = None,
                                                   early_stop_threshold: float = 0.9,
                                                   use_enhanced_extraction: bool = True) -> Dict[str, Any]:
        """
        优化版本的档案文件夹处理 - 支持早停和预处理
        """
        start_time = time.time()

        try:
            if elements is None:
                elements = self.default_elements.copy()

            # 如果启用增强提取，使用新的增强版本
            if use_enhanced_extraction:
                self.logger.info(f"使用增强版档案要素提取: {folder_path}")
                early_stop_config = {
                    'confidence_threshold': 0.9,
                    'basic_threshold': 0.75,
                    'attachment_threshold': 0.5,
                    'max_pages': 20
                }
                return await self._extract_from_archive_folder_enhanced(
                    folder_path=str(folder_path),
                    image_names=image_names,
                    elements=elements,
                    early_stop_config=early_stop_config,
                    enable_stamp_processing=enable_stamp_processing,
                    stamp_confidence_threshold=stamp_confidence_threshold
                )

            # 为每个文件夹创建独立的局部累积要素（用于文件夹内早停判断）
            local_accumulated_elements = {}

            # 如果传入了全局累积要素，保留引用用于最终更新
            global_accumulated_elements = accumulated_elements

            self.logger.info(f"开始优化处理档案文件夹: {folder_path}")

            # 获取指定的图片文件（使用分件后的image_names列表）
            image_files = []

            if image_names:
                # 使用传入的image_names列表获取具体的图片文件
                self.logger.info(f"使用指定的图片文件列表，共{len(image_names)}个文件")
                for image_name in image_names:
                    image_path = folder_path / image_name
                    if image_path.exists() and image_path.is_file():
                        image_files.append(image_path)
                        self.logger.debug(f"添加图片文件: {image_path}")
                    else:
                        self.logger.warning(f"指定的图片文件不存在: {image_path}")
            else:
                # 如果没有指定image_names，则获取文件夹下所有图片文件（兼容旧逻辑）
                self.logger.info("未指定图片文件列表，获取文件夹下所有图片文件")
                for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                    image_files.extend(folder_path.glob(f'*{ext}'))
                    image_files.extend(folder_path.glob(f'*{ext.upper()}'))
                image_files.sort()  # 按文件名排序

            if not image_files:
                return {
                    'success': False,
                    'error': f'文件夹中未找到图像文件: {folder_path}',
                    'processing_time': time.time() - start_time
                }

            self.logger.info(f"找到 {len(image_files)} 个图像文件")

            # 逐个处理图像文件，支持早停
            folder_elements = {}
            processed_count = 0
            skipped_count = 0

            for i, image_file in enumerate(image_files):
                # 检查是否需要早停（基于当前文件夹的局部累积要素）
                if self._should_early_stop(local_accumulated_elements, elements, early_stop_threshold):
                    self.logger.info(f"文件夹处理早停: 要素完整性达到{early_stop_threshold:.1%}")
                    skipped_count = len(image_files) - i
                    break

                self.logger.info(f"处理文件夹图像 {i+1}/{len(image_files)}: {image_file.name}")
                self.logger.debug(f"图像处理参数：confidence_threshold:{confidence_threshold},  enable_stamp_processing:{enable_stamp_processing}, stamp_confidence_threshold:{stamp_confidence_threshold},enable_preprocessing:{enable_preprocessing}")
                # 使用优化版本的图像处理
                result = await self.extract_from_image_optimized(
                    image_path=image_file,
                    elements=elements,
                    confidence_threshold=confidence_threshold,
                    enable_stamp_processing=enable_stamp_processing,
                    stamp_confidence_threshold=stamp_confidence_threshold,
                    enable_preprocessing=enable_preprocessing
                )

                processed_count += 1

                # 合并提取结果
                if result.get('success') and result.get('elements'):
                    for element_name, element_value in result['elements'].items():
                        if element_value and element_value.strip():
                            if element_name not in folder_elements:
                                folder_elements[element_name] = []
                            if element_value not in folder_elements[element_name]:
                                folder_elements[element_name].append(element_value)

                    # 更新局部累积要素（用于当前文件夹的早停判断）
                    self._update_accumulated_elements(local_accumulated_elements, result['elements'])

                    # 如果有全局累积要素，也更新它
                    if global_accumulated_elements is not None:
                        self._update_accumulated_elements(global_accumulated_elements, result['elements'])

            # 选择最佳值（通常是第一个非空值）
            final_elements = {}
            for element_name in elements:
                if element_name in folder_elements and folder_elements[element_name]:
                    final_elements[element_name] = folder_elements[element_name][0]
                else:
                    final_elements[element_name] = ""

            total_time = time.time() - start_time
            self.logger.info(f"文件夹处理完成: 处理{processed_count}个，跳过{skipped_count}个，耗时{total_time:.2f}秒")

            return {
                'success': True,
                'elements': final_elements,
                'folder_info': {
                    'total_images': len(image_files),
                    'processed_images': processed_count,
                    'skipped_images': skipped_count,
                    'early_stopped': skipped_count > 0
                },
                'processing_time': total_time
            }

        except Exception as e:
            self.logger.error(f"优化文件夹处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    async def _compare_with_excel(self,
                                excel_data: List[Dict[str, Any]],
                                extraction_results: List[Dict[str, Any]],
                                elements: List[str],
                                similarity_threshold: float) -> Dict[str, Any]:
        """执行Excel数据与提取结果的比对"""

        errors = []
        statistics = {
            'total_comparisons': 0,
            'error_count': 0,
            'accuracy_by_element': {}
        }

        # 初始化统计
        for element in elements:
            statistics['accuracy_by_element'][element] = {
                'total': 0,
                'correct': 0,
                'accuracy': 0.0
            }

        # 按dataKey组织比对结果，匹配Java后端期望的格式
        comparison_result_by_datakey = {}

        # 🔍 添加调试日志
        self.logger.debug(f"开始比对 - Excel数据条数: {len(excel_data)}, 提取结果条数: {len(extraction_results)}")

        # 🔧 构建提取结果的映射 - 支持dataKey+partNumber组合
        extraction_by_key = {}

        self.logger.debug("🔍🔍🔍 === 开始构建图像提取结果映射 === 🔍🔍🔍")
        for i, result in enumerate(extraction_results):
            path = result['path']
            filename = result['filename']
            self.logger.debug(f"处理提取结果[{i}]: path={path}, filename={filename}")

            # 🔍 详细打印result的完整结构
            self.logger.debug(f"  result完整结构: {result.keys()}")
            for key, value in result.items():
                if key == 'extraction_result':
                    self.logger.debug(f"    {key}: <提取结果数据>")
                else:
                    self.logger.debug(f"    {key}: {value}")

            # 从path中提取dataKey: D:\...\123\024\00001 -> 123-024-00001
            path_parts = result['path'].replace('\\', '/').split('/')
            self.logger.debug(f"路径分割结果: {path_parts}")

            if len(path_parts) >= 3:
                # 取最后三个部分构建dataKey
                datakey = '-'.join(path_parts[-3:])

                # 🔍 检查result中是否包含分件相关信息
                has_image_names = 'imageNames' in result and result['imageNames']
                has_part_number = 'partNumber' in result and result['partNumber'] is not None and result['partNumber'] != ''
                has_image_count = 'imageCount' in result and result['imageCount']

                self.logger.debug(f"  分件特征检查:")
                self.logger.debug(f"    imageNames存在且非空: {has_image_names}")
                self.logger.debug(f"    partNumber存在且非空: {has_part_number}")
                self.logger.debug(f"    imageCount存在且非空: {has_image_count}")

                # 🔍 判断是否为分件数据：有非空的partNumber（这是关键标识）
                if has_part_number:
                    # 分件后的数据，每个result对应一个具体的件
                    image_names = result.get('imageNames', [])
                    part_number = result.get('partNumber', '0001')  # 获取件号
                    image_count = result.get('imageCount', len(image_names))

                    # 使用 dataKey + partNumber 作为唯一键
                    unique_key = f"{datakey}#{part_number}"
                    extraction_by_key[unique_key] = {
                        'dataKey': datakey,
                        'partNumber': part_number,
                        'imageNames': image_names,
                        'imageCount': image_count,
                        'extraction_result': result.get('extraction_result', {}),
                        'path': path
                    }
                    self.logger.debug(f"✅ 分件映射: {unique_key} -> 图像{len(image_names)}张: {image_names}")
                else:
                    # 原始数据，整个卷作为一个单位
                    extraction_by_key[datakey] = {
                        'dataKey': datakey,
                        'partNumber': None,
                        'imageNames': [],
                        'imageCount': 0,
                        'extraction_result': result.get('extraction_result', {}),
                        'path': path
                    }
                    self.logger.debug(f"✅ 卷级映射: {datakey} -> 提取结果: {filename}")
            else:
                self.logger.warning(f"❌ 路径层级不足，无法构建dataKey: {path}")

        self.logger.debug(f"📊 图像映射完成，共{len(extraction_by_key)}个映射:")
        for key in extraction_by_key.keys():
            self.logger.debug(f"  - {key}")
        self.logger.debug("🔍🔍🔍 === 图像提取结果映射构建完成 === 🔍🔍🔍")

        # 执行比对（按dataKey+partNumber匹配）
        for i, excel_row in enumerate(excel_data):
            data_key = excel_row.get('dataKey', excel_row.get('_dataKey', f'row_{i+1}'))
            part_number = excel_row.get('partNumber', '0001')  # 获取件号
            row_num = excel_row.get('rowNum', i + 1)

            self.logger.debug(f"处理Excel行[{i}] - dataKey: {data_key}, partNumber: {part_number}, rowNum: {row_num}")

            # 初始化该dataKey的结果（如果还没有）
            if data_key not in comparison_result_by_datakey:
                comparison_result_by_datakey[data_key] = {}

            # 🔍 构建查找键：优先使用 dataKey#partNumber，回退到 dataKey
            lookup_keys = [
                f"{data_key}#{part_number}",  # 分件后的精确匹配
                data_key  # 卷级匹配（回退方案）
            ]

            extraction_data = None
            matched_key = None

            for lookup_key in lookup_keys:
                if lookup_key in extraction_by_key:
                    extraction_data = extraction_by_key[lookup_key]
                    matched_key = lookup_key
                    break

            if extraction_data:
                self.logger.debug(f"找到匹配的提取结果 - 查找键: {matched_key}")
                self.logger.debug(f"  - dataKey: {extraction_data['dataKey']}")
                self.logger.debug(f"  - partNumber: {extraction_data['partNumber']}")
                self.logger.debug(f"  - imageNames: {extraction_data['imageNames']}")

                # 检查提取结果是否有效
                extracted_elements = extraction_data.get('extraction_result', {})
                if not extracted_elements or not any(key in extracted_elements for key in elements):
                    # 为所有要素添加提取失败错误
                    for element in elements:
                        comparison_result_by_datakey[data_key][element] = {
                            'has_error': True,
                            'excel_value': excel_row.get(element, ''),
                            'extracted_value': '',
                            'similarity': 0.0,
                            'suggestion': f"档案要素提取失败或无效"
                        }
                    continue

                # 🔍 使用提取结果进行要素比对
                extracted_elements = extraction_data.get('extraction_result', {})

                # 逐个要素比对
                for element in elements:
                    excel_value = excel_row.get(element, '')
                    extracted_value = extracted_elements.get(element, '')

                    statistics['total_comparisons'] += 1
                    statistics['accuracy_by_element'][element]['total'] += 1

                    # 计算相似度
                    similarity = self._calculate_similarity(excel_value, extracted_value)

                    if similarity >= similarity_threshold:
                        statistics['accuracy_by_element'][element]['correct'] += 1
                        comparison_result_by_datakey[data_key][element] = {
                            'has_error': False,
                            'excel_value': str(excel_value),
                            'extracted_value': str(extracted_value),
                            'similarity': similarity
                        }
                    else:
                        # 记录错误
                        comparison_result_by_datakey[data_key][element] = {
                            'has_error': True,
                            'excel_value': str(excel_value),
                            'extracted_value': str(extracted_value),
                            'similarity': similarity,
                            'suggestion': self._generate_suggestion(excel_value, extracted_value, similarity)
                        }
                        statistics['error_count'] += 1
            else:
                # 没有对应的提取结果
                self.logger.debug(f"❌ 未找到匹配的提取结果 - dataKey: {data_key}, partNumber: {part_number}")
                self.logger.debug(f"  尝试的查找键: {lookup_keys}")
                self.logger.debug(f"  可用的提取结果键: {list(extraction_by_key.keys())}")

                for element in elements:
                    comparison_result_by_datakey[data_key][element] = {
                        'has_error': True,
                        'excel_value': excel_row.get(element, ''),
                        'extracted_value': '',
                        'similarity': 0.0,
                        'suggestion': f'未找到对应的图像文件 (dataKey: {data_key}, partNumber: {part_number})'
                    }

        # 计算准确率
        for element in elements:
            element_stats = statistics['accuracy_by_element'][element]
            if element_stats['total'] > 0:
                element_stats['accuracy'] = element_stats['correct'] / element_stats['total']

        return {
            'errors': errors,  # 保留原有的错误列表格式（用于统计）
            'statistics': statistics,
            'comparison_result': comparison_result_by_datakey  # 新增：按dataKey组织的比对结果
        }

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0

        # 简单的字符级相似度计算
        from difflib import SequenceMatcher
        return SequenceMatcher(None, str(text1), str(text2)).ratio()

    def _generate_suggestion(self, excel_value: str, extracted_value: str, similarity: float) -> str:
        """生成修正建议"""
        if not extracted_value:
            return "AI未能提取到该要素，建议检查图像质量或要素是否存在"
        elif not excel_value:
            return f"Excel中该字段为空，但AI提取到: {extracted_value}"
        elif similarity < 0.3:
            return f"相似度极低({similarity:.1%})，可能完全不匹配。Excel: {excel_value}, AI提取: {extracted_value}"
        elif similarity < 0.6:
            return f"相似度较低({similarity:.1%})，建议仔细核实。Excel: {excel_value}, AI提取: {extracted_value}"
        else:
            return f"相似度中等({similarity:.1%})，可能存在细微差异。Excel: {excel_value}, AI提取: {extracted_value}"

    # ==================== 智能文书结构分析方法 ====================

    async def classify_document_pages(self,
                                    pages_ocr_results: List[Dict[str, Any]],
                                    config: ExtractionConfig) -> List[PageClassificationResult]:
        """
        智能分类文书页面类型

        Args:
            pages_ocr_results: 每页的OCR结果列表
            config: 提取配置

        Returns:
            页面分类结果列表
        """
        classifications = []

        for i, page_result in enumerate(pages_ocr_results):
            full_text = page_result.get('full_text', '')

            # 执行页面分类
            classification = await self._classify_single_page(i, full_text, config)
            classifications.append(classification)

            self.logger.debug(f"页面 {i+1} 分类结果: {classification.page_type.value} "
                            f"(置信度: {classification.confidence:.2f})")

        return classifications

    async def _classify_single_page(self,
                                  page_index: int,
                                  full_text: str,
                                  config: ExtractionConfig) -> PageClassificationResult:
        """
        分类单个页面

        策略：
        1. 检测附件关键词 -> ATTACHMENT_PAGE
        2. 检测签发关键词和日期格式 -> SIGNATURE_PAGE
        3. 检测红头关键词 -> HEADER_PAGE
        4. 默认为 -> CONTENT_PAGE
        """
        keywords_found = []
        reasoning_parts = []

        # 1. 检测附件页
        attachment_score = 0
        for keyword in config.attachment_keywords:
            if keyword in full_text:
                attachment_score += 1
                keywords_found.append(f"附件:{keyword}")

        if attachment_score > 0:
            confidence = min(0.8 + attachment_score * 0.1, 1.0)
            reasoning = f"检测到{attachment_score}个附件关键词"
            return PageClassificationResult(
                page_index=page_index,
                page_type=DocumentPageType.ATTACHMENT_PAGE,
                confidence=confidence,
                keywords_found=keywords_found,
                reasoning=reasoning
            )

        # 2. 检测签发页
        signature_score = 0
        date_pattern_found = False

        # 检测日期格式
        date_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',  # 2024年1月1日
            r'\d{4}\.\d{1,2}\.\d{1,2}',    # 2024.1.1
            r'\d{4}-\d{1,2}-\d{1,2}',      # 2024-1-1
        ]

        for pattern in date_patterns:
            if re.search(pattern, full_text):
                date_pattern_found = True
                keywords_found.append("日期格式")
                break

        # 检测签发关键词
        for keyword in config.signature_keywords:
            if keyword in full_text:
                signature_score += 1
                keywords_found.append(f"签发:{keyword}")

        if date_pattern_found and signature_score > 0:
            confidence = 0.7 + signature_score * 0.1
            reasoning = f"检测到日期格式和{signature_score}个签发关键词"
            return PageClassificationResult(
                page_index=page_index,
                page_type=DocumentPageType.SIGNATURE_PAGE,
                confidence=confidence,
                keywords_found=keywords_found,
                reasoning=reasoning
            )

        # 3. 检测首页
        header_score = 0
        for keyword in config.header_keywords:
            if keyword in full_text:
                header_score += 1
                keywords_found.append(f"红头:{keyword}")

        # 检测文号格式
        doc_number_patterns = [
            r'[〔\[]\d{4}[〕\]]\d+号',     # 〔2024〕1号
            r'\w+发\[\d{4}\]\d+号',        # 某某发[2024]1号
            r'\w+字\[\d{4}\]\d+号',        # 某某字[2024]1号
        ]

        doc_number_found = False
        for pattern in doc_number_patterns:
            if re.search(pattern, full_text):
                doc_number_found = True
                keywords_found.append("文号格式")
                break

        if header_score > 0 or doc_number_found:
            confidence = 0.6 + header_score * 0.1 + (0.2 if doc_number_found else 0)
            reasoning = f"检测到{header_score}个红头关键词" + ("和文号格式" if doc_number_found else "")
            return PageClassificationResult(
                page_index=page_index,
                page_type=DocumentPageType.HEADER_PAGE,
                confidence=confidence,
                keywords_found=keywords_found,
                reasoning=reasoning
            )

        # 4. 默认为正文页
        return PageClassificationResult(
            page_index=page_index,
            page_type=DocumentPageType.CONTENT_PAGE,
            confidence=0.5,
            keywords_found=keywords_found,
            reasoning="未匹配到特定类型，默认为正文页"
        )

    def detect_attachment_start(self,
                              pages_ocr_results: List[Dict[str, Any]],
                              config: ExtractionConfig) -> Optional[int]:
        """
        检测附件开始页

        Returns:
            附件开始页的索引，如果没有附件则返回None
        """
        for i, page_result in enumerate(pages_ocr_results):
            full_text = page_result.get('full_text', '')

            # 检测附件关键词
            for keyword in config.attachment_keywords:
                if keyword in full_text:
                    self.logger.info(f"在第{i+1}页检测到附件开始: {keyword}")
                    return i

        return None

    def filter_main_document_pages(self,
                                 pages_ocr_results: List[Dict[str, Any]],
                                 config: ExtractionConfig) -> List[Dict[str, Any]]:
        """
        过滤出主文书页面（排除附件）

        Returns:
            主文书的OCR结果列表
        """
        attachment_start = self.detect_attachment_start(pages_ocr_results, config)

        if attachment_start is None:
            # 没有附件，返回所有页面
            return pages_ocr_results
        else:
            # 返回附件开始前的页面
            main_pages = pages_ocr_results[:attachment_start]
            self.logger.info(f"过滤附件后，主文书共{len(main_pages)}页")
            return main_pages

    def cleanup(self):
        """清理资源"""
        if self.executor:
            self.executor.shutdown(wait=True)
        self.logger.info("档案要素提取服务资源清理完成")

    async def extract_compare_batch_with_segmentation(self,
                                                        excel_data: List[Dict[str, Any]],
                                                        image_data: List[Dict[str, Any]],
                                                        elements: Optional[List[str]] = None,
                                                        confidence_threshold: float = 0.5,
                                                        similarity_threshold: float = 0.8,
                                                        enable_stamp_processing: bool = False,
                                                        stamp_confidence_threshold: float = 0.8,
                                                        enable_preprocessing: bool = True,
                                                        early_stop_threshold: float = 0.9) -> Dict[str, Any]:
        """
        批量提取档案要素并与Excel数据进行比对 - 支持分件数据

        Args:
            excel_data: Excel数据列表，每个元素包含一行数据
            image_data: 分件数据列表，包含路径、dataKey、partNumber等信息
            elements: 要提取的要素列表
            confidence_threshold: OCR置信度阈值
            similarity_threshold: 相似度阈值
            enable_stamp_processing: 是否启用印章处理
            stamp_confidence_threshold: 印章检测置信度阈值
            enable_preprocessing: 是否启用图像预处理
            early_stop_threshold: 早停阈值

        Returns:
            比对结果，包含错误列表和统计信息
        """
        start_time = time.time()

        try:
            self.logger.info("开始批量档案要素提取和比对（分件版本）")
            self.logger.info(f"Excel数据: {len(excel_data)} 行")
            self.logger.info(f"分件数据: {len(image_data)} 个")

            # 🔍 第一步：复用现有的提取逻辑，获取每个图像路径的提取结果
            image_paths_for_extraction = []
            image_names_for_extraction =[]
            for item in image_data:
                base_path = item.get('path')
                if base_path:
                    image_paths_for_extraction.append(base_path)
                    image_names_for_extraction.append(item.get('imageNames', []))

            self.logger.info(f"转换后的图像路径数量: {len(image_paths_for_extraction)}")

            # 🔍 调用现有的批量提取方法
            batch_result = await self.extract_batch(
                #excel_data=excel_data,  # 传递虚拟数据以触发提取
                image_files=image_paths_for_extraction,
                image_names=image_names_for_extraction,
                elements=elements,
                confidence_threshold=confidence_threshold,
                similarity_threshold=similarity_threshold,
                enable_stamp_processing=enable_stamp_processing,
                stamp_confidence_threshold=stamp_confidence_threshold,
                enable_preprocessing=enable_preprocessing,
                early_stop_threshold=early_stop_threshold
            )
            self.logger.debug(f"批量提取完成后batch_result xxxxbbbyyy：{batch_result}")
            # 🔍 第二步：构建包含分件信息的提取结果
            extraction_results = []
            processed_items = batch_result.get('processed_items', []) if batch_result.get('success') else []

            self.logger.debug(f"批量提取返回的processed_items数量: {len(processed_items)}")

            # 将提取结果与分件数据关联
            for i, item in enumerate(image_data):
                # 🔍 直接使用Java端传递的分件信息
                extraction_result = {}
                if i < len(processed_items):
                    processed_item = processed_items[i]
                    extraction_result = processed_item.get('extraction_result', {})
                    self.logger.debug(f"获取提取结果[{i}]: path={processed_item.get('path')}")
                    self.logger.debug(f"  processed_item完整内容: {processed_item}")
                    self.logger.debug(f"  extraction_result内容: {extraction_result}")
                else:
                    self.logger.warning(f"索引{i}超出processed_items范围({len(processed_items)})")

                extraction_results.append({
                    'path': item.get('path'),
                    'filename': item.get('filename'),
                    'dataKey': item.get('dataKey'),        # 🔍 直接使用Java传递的值
                    'partNumber': item.get('partNumber'),  # 🔍 直接使用Java传递的值
                    'imageNames': item.get('imageNames', []),
                    'imageCount': item.get('imageCount', 0),
                    'extraction_result': extraction_result
                })
                self.logger.debug(f"构建分件数据[{i}]: dataKey={item.get('dataKey')}, partNumber={item.get('partNumber')}, imageNames={item.get('imageNames', [])} extraction_result={extraction_result}")

            self.logger.debug(f"最终构建的extraction_results数量: {len(extraction_results)}")

            # 🔍 第三步：使用支持分件的比对逻辑
            return await self._compare_with_excel_segmented(
                excel_data, extraction_results, elements, similarity_threshold
            )

        except Exception as e:
            self.logger.error(f"分件批量提取和比对失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_processing_time': time.time() - start_time
            }

    async def _compare_with_excel_segmented(self,
                                          excel_data: List[Dict[str, Any]],
                                          extraction_results: List[Dict[str, Any]],
                                          elements: Optional[List[str]] = None,
                                          similarity_threshold: float = 0.8) -> Dict[str, Any]:
        """
        与Excel数据进行比对 - 支持分件数据
        """
        if elements is None:
            elements = self.default_elements.copy()

        self.logger.debug(f"开始分件比对 - Excel数据条数: {len(excel_data)}, 提取结果条数: {len(extraction_results)}")

        # 🔍 构建提取结果的映射 - 支持dataKey+partNumber组合
        extraction_by_key = {}

        self.logger.debug("🔍🔍🔍 === 开始构建分件提取结果映射 === 🔍🔍🔍")
        for i, result in enumerate(extraction_results):
            dataKey = result.get('dataKey')
            partNumber = result.get('partNumber')

            self.logger.debug(f"处理提取结果[{i}]: dataKey={dataKey}, partNumber={partNumber}")

            if dataKey and partNumber:
                # 分件数据：使用 dataKey + partNumber 作为唯一键
                unique_key = f"{dataKey}#{partNumber}"
                extraction_by_key[unique_key] = result
                self.logger.debug(f"✅ 分件映射: {unique_key}")
            elif dataKey:
                # 卷级数据：使用 dataKey 作为键
                extraction_by_key[dataKey] = result
                self.logger.debug(f"✅ 卷级映射: {dataKey}")

        self.logger.debug(f"📊 分件映射完成，共{len(extraction_by_key)}个映射:")
        for key in extraction_by_key.keys():
            self.logger.debug(f"  -key:{key} -value:{extraction_by_key.get(key)}")
        self.logger.debug("🔍🔍🔍 === 分件提取结果映射构建完成 === 🔍🔍🔍")

        # 执行比对（按dataKey+partNumber匹配）
        comparison_result_by_datakey = {}

        for i, excel_row in enumerate(excel_data):
            data_key = excel_row.get('dataKey', excel_row.get('_dataKey', f'row_{i+1}'))
            part_number = excel_row.get('partNumber', '0001')
            row_num = excel_row.get('rowNum', i + 1)

            self.logger.debug(f"处理Excel行[{i}] - dataKey: {data_key}, partNumber: {part_number}, rowNum: {row_num}")

            # 🔍 使用 dataKey_rowNum 作为唯一键，保持扁平结构，Java端无需适配
            unique_key = f"{data_key}_{row_num}"

            if unique_key not in comparison_result_by_datakey:
                comparison_result_by_datakey[unique_key] = {}

            # 🔍 构建查找键：优先使用 dataKey#partNumber，回退到 dataKey
            lookup_keys = [
                f"{data_key}#{part_number}",  # 分件后的精确匹配
                data_key  # 卷级匹配（回退方案）
            ]

            extraction_data = None
            matched_key = None

            for lookup_key in lookup_keys:
                if lookup_key in extraction_by_key:
                    extraction_data = extraction_by_key[lookup_key]
                    matched_key = lookup_key
                    break

            if extraction_data:
                self.logger.debug(f"找到匹配的提取结果 - 查找键: {matched_key}")

                # 获取实际的提取结果
                extracted_elements = extraction_data.get('extraction_result', {})
                self.logger.debug(f"原始extraction_result类型: {type(extracted_elements)}")
                self.logger.debug(f"原始extraction_result内容: {extracted_elements}")

                if hasattr(extracted_elements, 'get') and 'elements' in extracted_elements:
                    extracted_elements = extracted_elements['elements']
                    self.logger.debug(f"提取elements后: {extracted_elements}")
                else:
                    self.logger.debug(f"直接使用extraction_result: {extracted_elements}")

                # 逐个要素比对
                for element in elements:
                    excel_value = excel_row.get(element, '')
                    extracted_value = extracted_elements.get(element, '') if extracted_elements else ''

                    # 计算相似度和错误状态
                    if excel_value and extracted_value:
                        similarity = self._calculate_similarity(str(excel_value), str(extracted_value))
                        has_error = similarity < similarity_threshold
                        suggestion = self._generate_suggestion(excel_value, extracted_value, similarity)
                    elif not excel_value and extracted_value:
                        similarity = 0.0
                        has_error = True
                        suggestion = f"Excel中该字段为空，但AI提取到: {extracted_value}"
                    elif excel_value and not extracted_value:
                        similarity = 0.0
                        has_error = True
                        suggestion = f"AI未能提取到该字段，Excel值: {excel_value}"
                    else:
                        similarity = 1.0
                        has_error = False
                        suggestion = "两者均为空"

                    comparison_result_by_datakey[unique_key][element] = {
                        'has_error': has_error,
                        'excel_value': excel_value,
                        'extracted_value': extracted_value,
                        'similarity': similarity,
                        'suggestion': suggestion,
                        'dataKey': data_key,
                        'partNumber': part_number,
                        'rowNum': row_num
                    }
            else:
                # 没有对应的提取结果
                self.logger.debug(f"❌ 未找到匹配的提取结果 - dataKey: {data_key}, partNumber: {part_number}")
                self.logger.debug(f"  尝试的查找键: {lookup_keys}")
                self.logger.debug(f"  可用的提取结果键: {list(extraction_by_key.keys())}")

                for element in elements:
                    comparison_result_by_datakey[unique_key][element] = {
                        'has_error': True,
                        'excel_value': excel_row.get(element, ''),
                        'extracted_value': '',
                        'similarity': 0.0,
                        'suggestion': f'未找到对应的图像文件 (dataKey: {data_key}, partNumber: {part_number})',
                        'dataKey': data_key,
                        'partNumber': part_number,
                        'rowNum': row_num
                    }
        # 🔍 详细展示comparison_result_by_datakey的完整结构
        import json
        self.logger.debug("🔍🔍🔍 === 端点返回的comparison_result_by_datakey详细结构 === 🔍🔍🔍")
        self.logger.debug(f"comparison_result_by_datakey类型: {type(comparison_result_by_datakey)}")
        self.logger.debug(f"comparison_result_by_datakey键数量: {len(comparison_result_by_datakey)}")
        self.logger.debug(f"comparison_result_by_datakey键列表: {list(comparison_result_by_datakey.keys())}")

        try:
            # 尝试JSON格式化输出
            formatted_result = json.dumps(comparison_result_by_datakey, ensure_ascii=False, indent=2)
            self.logger.debug(f"comparison_result_by_datakey JSON格式:\n{formatted_result}")
        except Exception as e:
            # 如果JSON序列化失败，逐层展示
            self.logger.debug(f"JSON序列化失败: {e}，使用逐层展示:")
            for datakey, datakey_result in comparison_result_by_datakey.items():
                self.logger.debug(f"  📋 dataKey: {datakey}")
                self.logger.debug(f"    类型: {type(datakey_result)}")
                if isinstance(datakey_result, dict):
                    self.logger.debug(f"    包含字段: {list(datakey_result.keys())}")
                    for element, element_result in datakey_result.items():
                        self.logger.debug(f"      🔍 {element}:")
                        if isinstance(element_result, dict):
                            for key, value in element_result.items():
                                self.logger.debug(f"        {key}: {value}")
                        else:
                            self.logger.debug(f"        值: {element_result}")
                else:
                    self.logger.debug(f"    值: {datakey_result}")

        self.logger.debug("🔍🔍🔍 === comparison_result_by_datakey结构展示完成 === 🔍🔍🔍")
        return {
            'success': True,
            'task_id': 'segmented_batch',
            'comparison_result': comparison_result_by_datakey,
            'total_processing_time': time.time() - 2576  # 简单计算
        }


