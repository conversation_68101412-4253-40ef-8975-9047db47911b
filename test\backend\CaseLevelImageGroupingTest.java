import java.util.*;

/**
 * 案卷级图像分组处理测试
 *
 * 测试目标：
 * 1. 验证图像分件方式检测逻辑
 * 2. 验证页号范围解析功能
 * 3. 验证图像分组处理流程
 * 4. 验证完整的案卷级处理工作流
 */
public class CaseLevelImageGroupingTest {

    public static void main(String[] args) {
        System.out.println("=== 案卷级图像分组处理测试 ===");

        // 测试1: 图像分件方式检测
        testImageGroupingTypeDetection();

        // 测试2: 页号范围解析
        testPageRangeParser();

        // 测试3: 图像分组逻辑
        testImageGroupingLogic();

        // 测试4: 完整工作流测试
        testCompleteWorkflow();

        System.out.println("=== 测试完成 ===");
    }
    
    /**
     * 测试图像分件方式检测
     */
    private static void testImageGroupingTypeDetection() {
        System.out.println("\n--- 测试1: 图像分件方式检测 ---");
        
        // 模拟字段映射配置
        Map<String, String> fieldMapping1 = new HashMap<>();
        fieldMapping1.put("field1", "首页号");
        fieldMapping1.put("field2", "尾页号");
        System.out.println("配置1 - 首页号+尾页号: " + detectGroupingType(fieldMapping1));
        
        Map<String, String> fieldMapping2 = new HashMap<>();
        fieldMapping2.put("field1", "首页号");
        fieldMapping2.put("field2", "页数");
        System.out.println("配置2 - 首页号+页数: " + detectGroupingType(fieldMapping2));
        
        Map<String, String> fieldMapping3 = new HashMap<>();
        fieldMapping3.put("field1", "起止页号");
        System.out.println("配置3 - 起止页号: " + detectGroupingType(fieldMapping3));
        
        Map<String, String> fieldMapping4 = new HashMap<>();
        fieldMapping4.put("field1", "题名");
        fieldMapping4.put("field2", "责任者");
        System.out.println("配置4 - 无分件字段: " + detectGroupingType(fieldMapping4));
    }
    
    /**
     * 测试页号范围解析
     */
    private static void testPageRangeParser() {
        System.out.println("\n--- 测试2: 页号范围解析 ---");
        
        // 测试首页号+尾页号模式
        System.out.println("首页号+尾页号模式:");
        testParseStartEndPage("1", "5");
        testParseStartEndPage("10", "15");
        testParseStartEndPage("", "5");  // 异常情况
        
        // 测试首页号+页数模式
        System.out.println("\n首页号+页数模式:");
        testParseStartPageCount("1", "5");
        testParseStartPageCount("10", "3");
        testParseStartPageCount("1", "");  // 异常情况
        
        // 测试页号范围模式
        System.out.println("\n页号范围模式:");
        testParsePageRange("1-5");
        testParsePageRange("10-15");
        testParsePageRange("1,3,5-7");
        testParsePageRange("invalid");  // 异常情况
    }
    
    /**
     * 测试图像分组逻辑
     */
    private static void testImageGroupingLogic() {
        System.out.println("\n--- 测试3: 图像分组逻辑 ---");

        // 模拟图像数据
        List<MockImageData> images = Arrays.asList(
            new MockImageData("image001.jpg", 1, "/path/image001.jpg"),
            new MockImageData("image002.jpg", 2, "/path/image002.jpg"),
            new MockImageData("image003.jpg", 3, "/path/image003.jpg"),
            new MockImageData("image004.jpg", 4, "/path/image004.jpg"),
            new MockImageData("image005.jpg", 5, "/path/image005.jpg"),
            new MockImageData("image006.jpg", 6, "/path/image006.jpg")
        );

        // 模拟Excel条目数据
        List<MockFormData> formData = Arrays.asList(
            new MockFormData("案卷001", "件001", 1, 3, 1),  // 第1-3页
            new MockFormData("案卷001", "件002", 4, 6, 2)   // 第4-6页
        );

        System.out.println("原始图像数量: " + images.size());
        System.out.println("Excel条目数量: " + formData.size());

        // 执行分组
        Map<String, List<MockImageData>> groupedImages = groupImagesByPageRange(images, formData);

        System.out.println("分组结果:");
        for (Map.Entry<String, List<MockImageData>> entry : groupedImages.entrySet()) {
            System.out.println(String.format("  %s: %d张图像", entry.getKey(), entry.getValue().size()));
            for (MockImageData img : entry.getValue()) {
                System.out.println(String.format("    - %s (页号: %d)", img.fileName, img.pageNumber));
            }
        }
    }

    /**
     * 测试完整工作流
     */
    private static void testCompleteWorkflow() {
        System.out.println("\n--- 测试4: 完整工作流测试 ---");

        // 模拟案卷级处理场景
        System.out.println("场景: 案卷包含4个文件，共15页图像");
        System.out.println("Excel配置: 首页号+尾页号模式");

        // 模拟Excel数据
        List<MockFormData> excelData = Arrays.asList(
            new MockFormData("2024-001", "001", 1, 3, 1),   // 文件1: 第1-3页
            new MockFormData("2024-001", "002", 4, 7, 2),   // 文件2: 第4-7页
            new MockFormData("2024-001", "003", 8, 12, 3),  // 文件3: 第8-12页
            new MockFormData("2024-001", "004", 13, 15, 4)  // 文件4: 第13-15页
        );

        // 模拟图像文件
        List<MockImageData> imageFiles = new ArrayList<>();
        for (int i = 1; i <= 15; i++) {
            imageFiles.add(new MockImageData(
                String.format("page%03d.jpg", i),
                i,
                String.format("/case/2024-001/page%03d.jpg", i)
            ));
        }

        System.out.println(String.format("输入: %d个Excel条目, %d张图像", excelData.size(), imageFiles.size()));

        // 执行分组处理
        Map<String, List<MockImageData>> result = groupImagesByPageRange(imageFiles, excelData);

        System.out.println("处理结果:");
        int totalGroupedImages = 0;
        for (Map.Entry<String, List<MockImageData>> entry : result.entrySet()) {
            List<MockImageData> groupImages = entry.getValue();
            totalGroupedImages += groupImages.size();
            System.out.println(String.format("  文件 %s: %d张图像 (页号: %d-%d)",
                entry.getKey(),
                groupImages.size(),
                groupImages.isEmpty() ? 0 : groupImages.get(0).pageNumber,
                groupImages.isEmpty() ? 0 : groupImages.get(groupImages.size()-1).pageNumber
            ));
        }

        System.out.println(String.format("总计: %d张图像被正确分组", totalGroupedImages));

        // 验证结果
        boolean success = (result.size() == 4) && (totalGroupedImages == 15);
        System.out.println("测试结果: " + (success ? "✅ 通过" : "❌ 失败"));
    }
    
    // === 辅助方法 ===
    
    private static String detectGroupingType(Map<String, String> fieldMapping) {
        boolean hasStartPage = fieldMapping.values().stream().anyMatch(v -> v != null && v.contains("首页号"));
        boolean hasEndPage = fieldMapping.values().stream().anyMatch(v -> v != null && v.contains("尾页号"));
        boolean hasPageCount = fieldMapping.values().stream().anyMatch(v -> v != null && v.contains("页数"));
        boolean hasPageRange = fieldMapping.values().stream().anyMatch(v -> v != null && v.contains("起止页号"));
        
        if (hasStartPage && hasEndPage) {
            return "START_END_PAGE";
        } else if (hasStartPage && hasPageCount) {
            return "START_PAGE_COUNT";
        } else if (hasPageRange) {
            return "PAGE_RANGE";
        } else {
            return "SINGLE_PIECE";
        }
    }
    
    private static void testParseStartEndPage(String startPage, String endPage) {
        try {
            if (startPage.isEmpty() || endPage.isEmpty()) {
                System.out.println(String.format("  解析失败: 首页号='%s', 尾页号='%s' - 参数为空", startPage, endPage));
                return;
            }
            int start = Integer.parseInt(startPage);
            int end = Integer.parseInt(endPage);
            System.out.println(String.format("  首页号=%d, 尾页号=%d, 页数=%d", start, end, end - start + 1));
        } catch (NumberFormatException e) {
            System.out.println(String.format("  解析失败: 首页号='%s', 尾页号='%s' - 数字格式错误", startPage, endPage));
        }
    }
    
    private static void testParseStartPageCount(String startPage, String pageCount) {
        try {
            if (startPage.isEmpty() || pageCount.isEmpty()) {
                System.out.println(String.format("  解析失败: 首页号='%s', 页数='%s' - 参数为空", startPage, pageCount));
                return;
            }
            int start = Integer.parseInt(startPage);
            int count = Integer.parseInt(pageCount);
            int end = start + count - 1;
            System.out.println(String.format("  首页号=%d, 页数=%d, 尾页号=%d", start, count, end));
        } catch (NumberFormatException e) {
            System.out.println(String.format("  解析失败: 首页号='%s', 页数='%s' - 数字格式错误", startPage, pageCount));
        }
    }
    
    private static void testParsePageRange(String pageRange) {
        try {
            if (pageRange.contains("-")) {
                String[] parts = pageRange.split("-");
                if (parts.length == 2) {
                    int start = Integer.parseInt(parts[0].trim());
                    int end = Integer.parseInt(parts[1].trim());
                    System.out.println(String.format("  页号范围='%s', 首页号=%d, 尾页号=%d, 页数=%d", 
                        pageRange, start, end, end - start + 1));
                } else {
                    System.out.println(String.format("  解析失败: 页号范围='%s' - 格式不正确", pageRange));
                }
            } else {
                System.out.println(String.format("  解析失败: 页号范围='%s' - 不包含范围分隔符", pageRange));
            }
        } catch (NumberFormatException e) {
            System.out.println(String.format("  解析失败: 页号范围='%s' - 数字格式错误", pageRange));
        }
    }
    
    private static Map<String, List<MockImageData>> groupImagesByPageRange(
            List<MockImageData> images, List<MockFormData> formData) {
        
        Map<String, List<MockImageData>> result = new HashMap<>();
        
        for (MockFormData form : formData) {
            String key = form.dataKey + "-" + form.partNumber;
            List<MockImageData> groupImages = new ArrayList<>();
            
            for (int pageNum = form.startPage; pageNum <= form.endPage; pageNum++) {
                for (MockImageData image : images) {
                    if (image.pageNumber == pageNum) {
                        groupImages.add(image);
                        break;
                    }
                }
            }
            
            result.put(key, groupImages);
        }
        
        return result;
    }
    
    // === 模拟数据类 ===
    
    static class MockImageData {
        String fileName;
        int pageNumber;
        String filePath;

        MockImageData(String fileName, int pageNumber, String filePath) {
            this.fileName = fileName;
            this.pageNumber = pageNumber;
            this.filePath = filePath;
        }
    }
    
    static class MockFormData {
        String dataKey;
        String partNumber;
        int startPage;
        int endPage;
        int rowNum;

        MockFormData(String dataKey, String partNumber, int startPage, int endPage, int rowNum) {
            this.dataKey = dataKey;
            this.partNumber = partNumber;
            this.startPage = startPage;
            this.endPage = endPage;
            this.rowNum = rowNum;
        }
    }
}
