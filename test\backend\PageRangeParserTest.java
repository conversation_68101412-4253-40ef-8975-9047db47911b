package test.backend;

import java.util.HashMap;
import java.util.Map;

/**
 * 页号范围解析器测试
 *
 * 测试ProjectTaskUploadImpl中的页号范围解析功能
 * 包括三种解析方式：首页号+尾页号、首页号+页数、起止页号格式
 */
public class PageRangeParserTest {

    /**
     * 创建模拟的Excel数据JSON
     */
    private static String createTaskJson(Map<String, Object> data) {
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (!first) json.append(",");
            json.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\"");
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    /**
     * 测试首页号+尾页号解析
     */
    public static void testStartEndPageParsing() {
        System.out.println("=== 测试首页号+尾页号解析 ===");

        // 测试用例1：正常情况
        Map<String, Object> testData1 = new HashMap<>();
        testData1.put("首页号", "1");
        testData1.put("尾页号", "5");
        String taskJson1 = createTaskJson(testData1);

        System.out.println("测试用例1 - 正常情况:");
        System.out.println("  Excel数据: " + taskJson1);
        System.out.println("  字段映射: 首页号, 尾页号");
        System.out.println("  预期结果: 页号范围[1-5], 共5页");
        System.out.println();

        // 测试用例2：包含文字的页号
        Map<String, Object> testData2 = new HashMap<>();
        testData2.put("首页号", "第10页");
        testData2.put("尾页号", "第15页");
        String taskJson2 = createTaskJson(testData2);

        System.out.println("测试用例2 - 包含文字:");
        System.out.println("  Excel数据: " + taskJson2);
        System.out.println("  字段映射: 首页号, 尾页号");
        System.out.println("  预期结果: 页号范围[10-15], 共6页");
        System.out.println();

        // 测试用例3：异常情况 - 尾页号小于首页号
        Map<String, Object> testData3 = new HashMap<>();
        testData3.put("首页号", "10");
        testData3.put("尾页号", "5");
        String taskJson3 = createTaskJson(testData3);

        System.out.println("测试用例3 - 异常情况:");
        System.out.println("  Excel数据: " + taskJson3);
        System.out.println("  字段映射: 首页号, 尾页号");
        System.out.println("  预期结果: 无效范围错误");
        System.out.println();
    }

    /**
     * 测试首页号+页数解析
     */
    public static void testStartPageCountParsing() {
        System.out.println("=== 测试首页号+页数解析 ===");

        // 测试用例1：正常情况
        Map<String, Object> testData1 = new HashMap<>();
        testData1.put("首页号", "1");
        testData1.put("页数", "10");
        String taskJson1 = createTaskJson(testData1);

        System.out.println("测试用例1 - 正常情况:");
        System.out.println("  Excel数据: " + taskJson1);
        System.out.println("  字段映射: 首页号, 页数");
        System.out.println("  预期结果: 页号范围[1-10], 共10页");
        System.out.println();

        // 测试用例2：从中间页开始
        Map<String, Object> testData2 = new HashMap<>();
        testData2.put("首页号", "20");
        testData2.put("页数", "5");
        String taskJson2 = createTaskJson(testData2);

        System.out.println("测试用例2 - 从中间页开始:");
        System.out.println("  Excel数据: " + taskJson2);
        System.out.println("  字段映射: 首页号, 页数");
        System.out.println("  预期结果: 页号范围[20-24], 共5页");
        System.out.println();

        // 测试用例3：异常情况 - 页数为0
        Map<String, Object> testData3 = new HashMap<>();
        testData3.put("首页号", "1");
        testData3.put("页数", "0");
        String taskJson3 = createTaskJson(testData3);

        System.out.println("测试用例3 - 异常情况:");
        System.out.println("  Excel数据: " + taskJson3);
        System.out.println("  字段映射: 首页号, 页数");
        System.out.println("  预期结果: 页数必须大于0错误");
        System.out.println();
    }

    /**
     * 测试起止页号格式解析
     */
    @Test
    void testPageRangeFormatParsing() {
        System.out.println("=== 测试起止页号格式解析 ===");
        
        // 设置字段映射
        String ruleMapping = "[{\"key\":\"field1\",\"label\":\"起止页号\"}]";
        mockConfigDTO.setRuleMapping(ruleMapping);
        
        // 测试用例1：标准格式 1-5
        Map<String, Object> testData1 = new HashMap<>();
        testData1.put("起止页号", "1-5");
        mockFormData.setTaskJson(createTaskJson(testData1));
        
        System.out.println("测试用例1 - 标准格式: 起止页号=1-5");
        System.out.println("预期结果: 页号范围[1-5], 共5页");
        
        // 测试用例2：波浪号格式 10~15
        Map<String, Object> testData2 = new HashMap<>();
        testData2.put("起止页号", "10~15");
        mockFormData.setTaskJson(createTaskJson(testData2));
        
        System.out.println("测试用例2 - 波浪号格式: 起止页号=10~15");
        System.out.println("预期结果: 页号范围[10-15], 共6页");
        
        // 测试用例3：中文格式 1至5
        Map<String, Object> testData3 = new HashMap<>();
        testData3.put("起止页号", "1至5");
        mockFormData.setTaskJson(createTaskJson(testData3));
        
        System.out.println("测试用例3 - 中文格式: 起止页号=1至5");
        System.out.println("预期结果: 页号范围[1-5], 共5页");
        
        // 测试用例4：中文格式 20到25
        Map<String, Object> testData4 = new HashMap<>();
        testData4.put("起止页号", "20到25");
        mockFormData.setTaskJson(createTaskJson(testData4));
        
        System.out.println("测试用例4 - 中文格式: 起止页号=20到25");
        System.out.println("预期结果: 页号范围[20-25], 共6页");
        
        // 测试用例5：异常格式
        Map<String, Object> testData5 = new HashMap<>();
        testData5.put("起止页号", "无效格式");
        mockFormData.setTaskJson(createTaskJson(testData5));
        
        System.out.println("测试用例5 - 异常格式: 起止页号=无效格式");
        System.out.println("预期结果: 格式不正确错误");
    }

    /**
     * 测试边界条件
     */
    @Test
    void testBoundaryConditions() {
        System.out.println("=== 测试边界条件 ===");
        
        // 测试用例1：空数据
        mockFormData.setTaskJson("{}");
        System.out.println("测试用例1 - 空数据");
        System.out.println("预期结果: 未找到字段错误");
        
        // 测试用例2：null值
        Map<String, Object> testData2 = new HashMap<>();
        testData2.put("首页号", null);
        testData2.put("尾页号", "5");
        mockFormData.setTaskJson(createTaskJson(testData2));
        
        System.out.println("测试用例2 - null值: 首页号=null, 尾页号=5");
        System.out.println("预期结果: 首页号值为空错误");
        
        // 测试用例3：空字符串
        Map<String, Object> testData3 = new HashMap<>();
        testData3.put("首页号", "");
        testData3.put("尾页号", "5");
        mockFormData.setTaskJson(createTaskJson(testData3));
        
        System.out.println("测试用例3 - 空字符串: 首页号='', 尾页号=5");
        System.out.println("预期结果: 首页号值为空字符串错误");
    }

    /**
     * 手动测试方法 - 用于在实际环境中验证
     */
    public static void main(String[] args) {
        System.out.println("=== 页号范围解析器测试 ===");
        
        PageRangeParserTest test = new PageRangeParserTest();
        test.setUp();
        
        System.out.println("\n1. 测试首页号+尾页号解析");
        test.testStartEndPageParsing();
        
        System.out.println("\n2. 测试首页号+页数解析");
        test.testStartPageCountParsing();
        
        System.out.println("\n3. 测试起止页号格式解析");
        test.testPageRangeFormatParsing();
        
        System.out.println("\n4. 测试边界条件");
        test.testBoundaryConditions();
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("注意：这些是预期结果，需要在实际环境中运行来验证解析逻辑是否正确工作。");
        System.out.println("建议使用反射或集成测试来调用私有方法进行验证。");
    }
}
