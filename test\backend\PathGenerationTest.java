import java.io.File;

/**
 * 路径生成测试
 * 
 * 用于验证路径生成逻辑是否正确
 * 
 * <AUTHOR> Assistant
 * @date 2024-12-19
 */
public class PathGenerationTest {

    public static void main(String[] args) {
        System.out.println("=== 路径生成测试 ===");
        
        // 模拟配置
        String uploadPath = "D:\\exactness\\upload";
        String taskId = "1eyknewc03102l83to";
        
        // 测试路径生成
        testPathGeneration(uploadPath, taskId);
        
        // 测试路径规范化
        testPathNormalization();
        
        System.out.println("\n✅ 路径生成测试完成");
    }
    
    private static void testPathGeneration(String uploadPath, String taskId) {
        System.out.println("\n--- 路径生成测试 ---");
        
        String PATH_IMAGE = "imagepath";
        String PATH_EXCEL = "excelpath";
        String PATH_PDF = "pdfpath";
        
        // 原始路径生成
        String imagePath = uploadPath + File.separator + taskId + File.separator + PATH_IMAGE + File.separator;
        String excelPath = uploadPath + File.separator + taskId + File.separator + PATH_EXCEL + File.separator;
        String pdfPath = uploadPath + File.separator + taskId + File.separator + PATH_PDF + File.separator;
        
        System.out.println("原始路径:");
        System.out.println("  图片路径: " + imagePath);
        System.out.println("  Excel路径: " + excelPath);
        System.out.println("  PDF路径: " + pdfPath);
        
        // 规范化路径
        imagePath = normalizePath(imagePath);
        excelPath = normalizePath(excelPath);
        pdfPath = normalizePath(pdfPath);
        
        System.out.println("\n规范化后路径:");
        System.out.println("  图片路径: " + imagePath);
        System.out.println("  Excel路径: " + excelPath);
        System.out.println("  PDF路径: " + pdfPath);
        
        // 测试文件路径拼接
        String dataKey = "123/024/00002";
        String fileName = "00001.jpg";
        
        String fullImagePath = imagePath + dataKey + File.separator + fileName;
        fullImagePath = normalizePath(fullImagePath);
        
        System.out.println("\n完整文件路径:");
        System.out.println("  数据键: " + dataKey);
        System.out.println("  文件名: " + fileName);
        System.out.println("  完整路径: " + fullImagePath);
        
        // 检查路径是否绝对路径
        File file = new File(fullImagePath);
        System.out.println("  是否绝对路径: " + file.isAbsolute());
        System.out.println("  父目录: " + file.getParentFile().getAbsolutePath());
    }
    
    private static void testPathNormalization() {
        System.out.println("\n--- 路径规范化测试 ---");
        
        String[] testPaths = {
            "D:\\exactness\\upload/taskId\\imagepath/",
            "D:/exactness/upload\\taskId/imagepath\\",
            "D:\\exactness\\\\upload\\\\taskId\\\\imagepath\\\\",
            "D:/exactness//upload//taskId//imagepath//",
            "exactness/upload/taskId/imagepath/"
        };
        
        for (String testPath : testPaths) {
            String normalized = normalizePath(testPath);
            System.out.println("原始: " + testPath);
            System.out.println("规范: " + normalized);
            System.out.println("绝对: " + new File(normalized).isAbsolute());
            System.out.println("---");
        }
    }
    
    private static String normalizePath(String path) {
        // 规范化路径，确保使用正确的分隔符
        path = path.replace("/", File.separator).replace("\\", File.separator);
        // 处理重复的分隔符
        while (path.contains(File.separator + File.separator)) {
            path = path.replace(File.separator + File.separator, File.separator);
        }
        // 确保路径是绝对路径
        File pathFile = new File(path);
        if (!pathFile.isAbsolute()) {
            path = new File(System.getProperty("user.dir"), path).getAbsolutePath();
            if (!path.endsWith(File.separator)) {
                path += File.separator;
            }
        }
        return path;
    }
}
