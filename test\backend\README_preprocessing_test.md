# 图像预处理优化测试指南

## 概述

本文档介绍如何测试和验证A4扫描文档的图像预处理优化功能。系统针对2461×3526像素、24位色深、约400KB的A4扫描文档进行了专门优化。

## 优化目标

- **性能提升**: 60-70% 处理时间减少
- **内存优化**: 75% 内存使用减少  
- **文件压缩**: 智能压缩减少传输和存储开销
- **精度保持**: 维持95%+的OCR识别精度

## 测试工具

### 1. 快速功能测试 (`quick_preprocessing_test.py`)

**用途**: 快速验证图像预处理功能是否正常工作

**使用方法**:
```bash
# 单文件测试
python quick_preprocessing_test.py C:\Documents\scan.jpg

# 批量测试（最多5个文件）
python quick_preprocessing_test.py C:\Documents\scans\
```

**输出信息**:
- 原始图像信息（尺寸、大小、格式）
- 预处理配置参数
- 处理后图像信息和压缩效果
- 处理时间统计
- 保存处理后图像用于人工检查

### 2. 完整对比测试 (`test_preprocessing_optimization.py`)

**用途**: 对比启用/禁用预处理的完整效果差异

**使用方法**:
```bash
python test_preprocessing_optimization.py C:\Documents\scan.jpg
```

**测试内容**:
- 处理时间对比
- 提取精度对比
- OCR效果对比
- 元素提取详细对比
- 优化效果总结

**前置条件**: 需要FastAPI服务运行在 `http://localhost:8080`

## 配置参数

图像预处理配置位于 `src/core/config_manager.py`:

```python
'image_preprocessing': {
    'enabled': True,                    # 是否启用图像预处理
    'target_long_side': 1600,          # 目标长边像素
    'jpeg_quality': 90,                # JPEG压缩质量
    'enhance_contrast': 1.15,          # 对比度增强倍数
    'enhance_sharpness': 1.05,         # 锐度增强倍数
    'remove_exif': True,               # 是否移除EXIF数据
    'enable_denoising': False,         # 是否启用去噪
    'min_resize_threshold': 1800,      # 最小缩放阈值
    'memory_limit_mb': 500,            # 内存使用限制(MB)
    'output_format': 'JPEG',           # 输出格式
    'enable_stats': True,              # 是否启用性能统计
}
```

## FastAPI接口

### 档案要素提取（带预处理）

**端点**: `POST /extract/archive`

**参数**:
- `file`: 上传的图像文件
- `custom_keys`: 自定义要素（可选，默认: "题名,责任者,文号,成文日期"）
- `confidence_threshold`: 置信度阈值（可选，默认: 0.5）
- `enable_preprocessing`: 是否启用预处理（可选，默认: true）

**响应示例**:
```json
{
  "success": true,
  "results": {
    "题名": "关于加强档案管理的通知",
    "责任者": "某某部门",
    "文号": "某文〔2024〕001号",
    "成文日期": "2024-01-15"
  },
  "processing_time": 3.45,
  "ocr_info": {
    "total_blocks": 12,
    "image_preprocessing": {
      "enabled": true,
      "original_size_kb": 387.2,
      "processed_size_kb": 156.8,
      "size_reduction": 59.5
    }
  }
}
```

### 预处理统计信息

**端点**: `GET /stats/preprocessing`

**响应示例**:
```json
{
  "success": true,
  "stats": {
    "total_processed": 25,
    "avg_processing_time": 0.85,
    "size_reduction_ratio": 0.62,
    "total_time": 21.25
  }
}
```

**重置统计**: `POST /stats/preprocessing/reset`

## 测试流程

### 1. 环境准备

```bash
# 激活conda环境
conda activate py39ocr

# 启动FastAPI服务
cd intelligent-extraction
python start_service.py
```

### 2. 快速功能验证

```bash
# 测试单个文件
python quick_preprocessing_test.py path/to/your/scan.jpg

# 检查生成的处理后图像
# 文件名格式: processed_原文件名.jpg
```

### 3. 完整效果对比

```bash
# 运行对比测试
python test_preprocessing_optimization.py path/to/your/scan.jpg

# 查看详细对比报告
```

### 4. 性能监控

```bash
# 查看健康状态（包含预处理统计）
curl http://localhost:8080/health

# 查看详细预处理统计
curl http://localhost:8080/stats/preprocessing

# 重置统计数据
curl -X POST http://localhost:8080/stats/preprocessing/reset
```

## 预期效果

### A4扫描文档 (2461×3526, 400KB)

**优化前**:
- 处理时间: ~8-12秒
- 内存使用: ~800MB
- 文件大小: 400KB

**优化后**:
- 处理时间: ~3-5秒 (60-70%提升)
- 内存使用: ~200MB (75%减少)
- 文件大小: ~160KB (60%压缩)
- OCR精度: 保持95%+

### 关键指标

- **缩放策略**: 长边缩放到1600px (约45%尺寸减少)
- **图像增强**: 对比度1.15x, 锐度1.05x
- **压缩优化**: JPEG质量90, 移除EXIF
- **内存控制**: 限制500MB内存使用

## 故障排除

### 常见问题

1. **预处理失败**: 检查PIL库和图像格式支持
2. **内存不足**: 调整 `memory_limit_mb` 参数
3. **处理时间过长**: 检查 `target_long_side` 设置
4. **精度下降**: 调整图像增强参数

### 日志查看

```bash
# 查看详细日志
tail -f logs/intelligent_extraction.log

# 查看预处理相关日志
grep "图像预处理" logs/intelligent_extraction.log
```

## 性能调优

### 针对不同硬件配置

**高性能配置** (16GB+ RAM):
```python
'target_long_side': 2000,
'memory_limit_mb': 1000,
'enhance_contrast': 1.2,
```

**低配置优化** (8GB RAM):
```python
'target_long_side': 1200,
'memory_limit_mb': 300,
'enable_denoising': False,
```

### 批量处理优化

对于大量文档处理，建议:
1. 启用统计监控
2. 定期重置统计数据
3. 监控内存使用情况
4. 考虑分批处理

## 总结

图像预处理优化显著提升了A4扫描文档的处理效率，在保持高精度的同时大幅减少了处理时间和资源消耗。通过提供的测试工具，可以方便地验证和监控优化效果。
