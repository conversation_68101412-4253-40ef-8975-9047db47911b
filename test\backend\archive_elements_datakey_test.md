# 档案要素检查DataKey和PartNumber测试

## 测试目的
验证ArchiveElementsCheckRule中的图像分件处理是否正确保持dataKey和partNumber的完整性。

## 问题描述
在迁移过程中发现，传递给Python服务的图像数据中：
- **期望**: dataKey="123-023-00001", partNumber="0001"
- **实际**: dataKey="123-023", partNumber="00001"

## 关键修复点

### 1. 图像分件处理逻辑
确保在`createImageGroupForPageRange`方法中：
```java
// ✅ 正确：保持完整的dataKey和partNumber
imageGroup.setDataKey(formData.getDataKey());     // "123-023-00001"
imageGroup.setPartNumber(formData.getPartNumber()); // "0001"
```

### 2. 图像数据传递
在`callPythonArchiveService`方法中：
```java
// ✅ 确保传递正确的数据
imageInfo.put("dataKey", imageData.getDataKey());       // 完整dataKey
imageInfo.put("partNumber", imageData.getPartNumber()); // 完整partNumber
```

## 测试验证步骤

### 步骤1：检查日志输出
运行档案要素检查，观察日志中的关键信息：

```
📋 Excel数据行: dataKey=123-023-00001, partNumber=0001, rowNum=2
...
🐍 传递给Python的图像数据: dataKey=123-023-00001, partNumber=0001, path=..., imageNames=...
```

### 步骤2：验证数据完整性
确保以下数据保持一致：
- Excel数据中的dataKey和partNumber
- 图像分件处理后的dataKey和partNumber  
- 传递给Python服务的dataKey和partNumber

### 步骤3：对比修复前后
**修复前（错误）**：
```
📋 Excel数据行: dataKey=123-023-00001, partNumber=0001, rowNum=2
🐍 传递给Python的图像数据: dataKey=123-023, partNumber=00001
```

**修复后（正确）**：
```
📋 Excel数据行: dataKey=123-023-00001, partNumber=0001, rowNum=2
🐍 传递给Python的图像数据: dataKey=123-023-00001, partNumber=0001
```

## 核心修复内容

### 1. 完整迁移WorkImpl.java中的分件逻辑
- `processStartEndPageGrouping`方法
- `createImageGroupForPageRange`方法
- `getFieldValueFromFormData`方法
- 完整的ImageGroupingResult类定义

### 2. 保持数据一致性
确保在整个处理流程中，dataKey和partNumber始终保持原始值，不被意外修改。

### 3. 错误处理
在分件处理失败时，正确回退到原始图像数据，保持数据完整性。

## 预期结果
修复后，档案要素检查应该：
1. 正确保持Excel数据中的dataKey和partNumber格式
2. 在图像分件处理中不丢失或修改这些关键标识
3. 向Python服务传递完整准确的数据
4. 确保AI服务能够正确匹配Excel数据和图像数据

## 验证成功标准
- [ ] Excel数据解析正确
- [ ] 图像分件处理保持数据完整性
- [ ] Python服务接收到正确的dataKey和partNumber
- [ ] 档案要素比对结果准确
- [ ] 错误结果正确保存到数据库
