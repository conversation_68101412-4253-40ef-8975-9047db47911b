# 档案要素检查集成测试

## 测试目的
验证档案要素检查功能从WorkImpl.executor方法到ArchiveElementsCheckRule的完整集成是否正常工作。

## 测试环境准备

### 1. 数据库准备
确保数据库中存在以下测试数据：
- 规则配置：ruleCode为"archiveElements"的规则
- 任务配置：包含档案要素字段映射的任务配置
- Excel数据：包含档案要素相关字段的表单数据
- 图像数据：对应的图像文件路径数据

### 2. Python服务准备
确保Python档案要素检查服务正在运行：
- 服务地址：http://localhost:8080/extract/archive/batch_compare
- 服务状态：正常响应

## 测试步骤

### 步骤1：验证规则执行器注册
```java
// 测试RuleExecuteFactory能否正确找到ArchiveElementsCheckRule
RuleExecuteFactory factory = SpringContext.getBean(RuleExecuteFactory.class);
RuleExecuteFactoryService service = factory.get("archiveElements");
assertNotNull(service);
assertTrue(service instanceof ArchiveElementsCheckRule);
```

### 步骤2：验证异步执行
```java
// 创建测试任务
ProjectTaskInfoDTO taskInfo = createTestTaskInfo();
WorkImpl workImpl = SpringContext.getBean(WorkImpl.class);

// 执行任务
workImpl.executor(taskInfo);

// 验证档案要素检查是否被异步执行
// 检查日志输出是否包含档案要素检查的标识信息
```

### 步骤3：验证图像分件逻辑
测试不同的图像分件场景：

#### 3.1 无需分件场景
- 字段库中不包含分件相关字段
- 预期：使用原始图像数据，不进行分件处理

#### 3.2 起始页码+终止页码分件
- 字段库包含"起始页码"和"终止页码"字段
- Excel数据包含具体的页码范围
- 预期：按页码范围进行图像分件

#### 3.3 起始页码+页数分件
- 字段库包含"起始页码"和"页数"字段
- Excel数据包含起始页码和页数信息
- 预期：按页数计算终止页码进行分件

#### 3.4 页码范围分件
- 字段库包含"页码范围"字段
- Excel数据包含页码范围字符串（如"1-5,7,9-12"）
- 预期：解析页码范围进行分件

### 步骤4：验证字段映射
测试档案要素到Excel字段的映射：

```java
// 测试默认映射
Map<String, String> defaultMapping = new HashMap<>();
defaultMapping.put("title", "卷内题名");
defaultMapping.put("responsible_party", "责任者");
defaultMapping.put("document_number", "文号");
defaultMapping.put("issue_date", "发文日期");

// 测试自定义映射（通过规则配置）
// 验证能否正确从taskConfigDTO.buildRuleKeyFieldMap()获取映射
```

### 步骤5：验证Python服务调用
测试与Python AI服务的集成：

```java
// 准备测试数据
List<Map<String, Object>> excelData = createTestExcelData();
List<ProjectTaskImageDataDTO> imageData = createTestImageData();

// 调用Python服务
ArchiveElementsCheckResult result = callPythonArchiveService(taskId, excelData, imageData);

// 验证响应
assertNotNull(result);
assertTrue(result.isSuccess());
assertNotNull(result.getComparisonResult());
```

### 步骤6：验证错误结果转换
测试检查结果到TaskErrorResultDO的转换：

```java
// 模拟Python服务返回的比对结果
ArchiveElementsCheckResult mockResult = createMockResult();

// 转换为错误结果
List<TaskErrorResultDO> errorResults = convertToTaskErrorResults(mockResult, taskConfigDTO, taskInfoDTO, ruleDTO);

// 验证转换结果
assertNotNull(errorResults);
for (TaskErrorResultDO error : errorResults) {
    assertNotNull(error.getTaskId());
    assertNotNull(error.getFieldName());
    assertNotNull(error.getRuleName());
    assertEquals(ErrorResultType.RULE.getNum(), error.getErrorType());
}
```

## 预期结果

### 成功场景
1. **规则注册成功**：RuleExecuteFactory能够找到ArchiveElementsCheckRule
2. **异步执行正常**：档案要素检查在独立线程中执行，不阻塞主流程
3. **图像分件正确**：根据字段配置正确进行图像分件处理
4. **字段映射准确**：档案要素正确映射到Excel字段
5. **AI服务调用成功**：Python服务正常响应并返回比对结果
6. **错误结果保存**：检查结果正确转换并保存到数据库

### 异常场景处理
1. **Python服务不可用**：记录错误日志，不中断整体流程
2. **图像分件失败**：回退到原始图像数据
3. **字段映射失败**：使用默认映射
4. **数据格式异常**：记录错误并跳过异常数据

## 测试验证点

### 日志验证
检查日志输出是否包含以下关键信息：
- `🔍🔍🔍 === 开始档案要素检查 === 🔍🔍🔍`
- `🚀 开始执行图像分件处理` (如果需要分件)
- `📋 开始档案要素提取和比对`
- `🐍🐍🐍 === 调用Python服务参数 === 🐍🐍🐍`
- `✅✅✅ === Java端解析完成 === ✅✅✅`

### 数据库验证
检查TaskErrorResultDO表中是否正确保存了错误结果：
- taskId正确
- fieldName为Excel列名
- ruleName为规则别名
- errorType为RULE类型
- errorFileValue包含详细错误信息

### 性能验证
- 档案要素检查不应阻塞其他规则的执行
- 异步执行应在合理时间内完成
- 内存使用应保持在正常范围

## 故障排查

### 常见问题
1. **规则执行器未找到**：检查@Service注解和bean名称
2. **Python服务连接失败**：检查服务地址和网络连接
3. **图像分件异常**：检查字段配置和Excel数据格式
4. **字段映射错误**：检查taskConfigDTO的字段库配置

### 调试建议
1. 启用详细日志输出
2. 使用断点调试关键方法
3. 检查Spring容器中的bean注册情况
4. 验证数据库连接和事务状态
