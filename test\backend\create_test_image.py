#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图像并测试OCR
"""
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.utils.image_preprocessor import DocumentImageProcessor

def create_and_preprocess_test():
    """创建测试图像并进行预处理"""
    print("🧪 创建测试图像并进行预处理")
    print("=" * 50)
    
    # 1. 创建一个中等大小的测试图像
    print("📋 步骤1: 创建测试图像")
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 36)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    # 添加一些测试文本
    test_texts = [
        "档案标题：重要文件管理规定",
        "责任者：某某公司行政部",
        "文件编号：XZ-2024-001",
        "成文日期：2024年1月15日",
        "This is a test document",
        "测试文档内容"
    ]
    
    y_pos = 50
    for text in test_texts:
        draw.text((50, y_pos), text, fill='black', font=font)
        y_pos += 60
    
    # 保存原始测试图像
    original_path = Path(__file__).parent / "test_medium_image.jpg"
    img.save(original_path, 'JPEG', quality=90)
    
    file_size = original_path.stat().st_size
    print(f"✅ 原始测试图像创建: {original_path}")
    print(f"📊 原始图像: 800×600, {file_size/1024:.1f}KB")
    
    # 2. 使用图像预处理器处理
    print(f"\n📋 步骤2: 图像预处理")
    try:
        processor = DocumentImageProcessor()
        processed_data, stats = processor.process_image(
            image_input=str(original_path),
            output_format='JPEG'
        )
        
        # 保存预处理后的图像
        processed_path = Path(__file__).parent / "test_processed_image.jpg"
        with open(processed_path, 'wb') as f:
            f.write(processed_data)
        
        processed_size = processed_path.stat().st_size
        print(f"✅ 预处理完成: {processed_path}")
        print(f"📊 预处理后: {processed_size/1024:.1f}KB")
        print(f"📊 处理统计: {stats}")
        
        return str(original_path), str(processed_path)
        
    except Exception as e:
        print(f"❌ 图像预处理失败: {e}")
        return str(original_path), None


def main():
    """主函数"""
    original_path, processed_path = create_and_preprocess_test()
    
    print(f"\n🎯 测试图像已准备完成:")
    print(f"   原始图像: {original_path}")
    if processed_path:
        print(f"   预处理图像: {processed_path}")
        print(f"\n💡 现在可以使用以下命令测试OCR:")
        print(f"   python test_simple_ocr.py {original_path}")
        print(f"   python test_simple_ocr.py {processed_path}")
    else:
        print(f"   预处理失败，只能测试原始图像")
        print(f"\n💡 现在可以使用以下命令测试OCR:")
        print(f"   python test_simple_ocr.py {original_path}")


if __name__ == "__main__":
    main()
