#!/usr/bin/env python3
"""
OCR性能诊断工具
分析为什么GPU推理一张图像需要16秒
"""

import time
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_rapidocr_performance():
    """测试RapidOCR性能"""
    print("🔍 RapidOCR性能测试")
    print("=" * 50)
    
    try:
        from rapidocr import RapidOCR
        from rapidocr.utils.typings import OCRVersion, ModelType
        import numpy as np
        from PIL import Image
        
        # 创建测试图像
        test_image = create_test_image(600, 800)
        test_image_path = "test_image_600x800.jpg"
        test_image.save(test_image_path)
        
        print(f"📋 测试图像: {test_image_path} (600x800)")
        
        # 测试Mobile模型
        print("\n🧪 测试Mobile模型...")
        mobile_params = {
            'Det.ocr_version': OCRVersion.PPOCRV5,
            'Rec.ocr_version': OCRVersion.PPOCRV5,
            'Det.model_type': ModelType.MOBILE,
            'Rec.model_type': ModelType.MOBILE,
            'Det.limit_side_len': 960,
            'Rec.rec_batch_num': 6,
        }
        
        # 初始化时间
        start_time = time.time()
        mobile_ocr = RapidOCR(params=mobile_params)
        init_time = time.time() - start_time
        print(f"   初始化耗时: {init_time:.3f}s")
        
        # 首次推理（包含模型加载）
        start_time = time.time()
        result1 = mobile_ocr(test_image_path)
        first_inference_time = time.time() - start_time
        print(f"   首次推理耗时: {first_inference_time:.3f}s")
        
        # 第二次推理（模型已加载）
        start_time = time.time()
        result2 = mobile_ocr(test_image_path)
        second_inference_time = time.time() - start_time
        print(f"   第二次推理耗时: {second_inference_time:.3f}s")
        
        # 测试Server模型
        print("\n🧪 测试Server模型...")
        server_params = {
            'Det.ocr_version': OCRVersion.PPOCRV5,
            'Rec.ocr_version': OCRVersion.PPOCRV5,
            'Det.model_type': ModelType.SERVER,
            'Rec.model_type': ModelType.SERVER,
            'Det.limit_side_len': 960,
            'Rec.rec_batch_num': 6,
        }
        
        # 初始化时间
        start_time = time.time()
        server_ocr = RapidOCR(params=server_params)
        init_time = time.time() - start_time
        print(f"   初始化耗时: {init_time:.3f}s")
        
        # 首次推理
        start_time = time.time()
        result3 = server_ocr(test_image_path)
        first_inference_time = time.time() - start_time
        print(f"   首次推理耗时: {first_inference_time:.3f}s")
        
        # 第二次推理
        start_time = time.time()
        result4 = server_ocr(test_image_path)
        second_inference_time = time.time() - start_time
        print(f"   第二次推理耗时: {second_inference_time:.3f}s")
        
        # 清理测试文件
        Path(test_image_path).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ RapidOCR性能测试失败: {e}")
        return False


def test_rapidocr_engine_performance():
    """测试我们的RapidOCREngine性能"""
    print("\n🔍 RapidOCREngine性能测试")
    print("=" * 50)
    
    try:
        from src.models.rapidocr_engine import RapidOCREngine
        import numpy as np
        from PIL import Image
        
        # 创建测试图像
        test_image = create_test_image(600, 800)
        test_image_path = "test_image_600x800_engine.jpg"
        test_image.save(test_image_path)
        
        print(f"📋 测试图像: {test_image_path} (600x800)")
        
        # 测试Server模型配置
        config = {
            'ocr_version': 'ppocrv5',
            'model_type': 'server',
            'det_limit_side_len': 960,
            'rec_batch_num': 6,
            'force_cpu': False
        }
        
        # 初始化引擎
        start_time = time.time()
        engine = RapidOCREngine(device_id=0, config=config)
        init_time = time.time() - start_time
        print(f"   引擎创建耗时: {init_time:.3f}s")
        
        # 首次推理（包含延迟初始化）
        start_time = time.time()
        result1 = engine.recognize(test_image_path, confidence_threshold=0.5)
        first_inference_time = time.time() - start_time
        print(f"   首次推理耗时: {first_inference_time:.3f}s")
        print(f"   识别结果: {result1.get('total_blocks', 0)}个文本块")
        
        # 第二次推理
        start_time = time.time()
        result2 = engine.recognize(test_image_path, confidence_threshold=0.5)
        second_inference_time = time.time() - start_time
        print(f"   第二次推理耗时: {second_inference_time:.3f}s")
        
        # 测试带印章检测的推理
        print("\n🧪 测试印章检测推理...")
        start_time = time.time()
        result3 = engine.recognize_with_stamp_detection(
            test_image_path, 
            confidence_threshold=0.5,
            enable_stamp_processing=True,
            stamp_confidence_threshold=0.5
        )
        stamp_inference_time = time.time() - start_time
        print(f"   印章检测推理耗时: {stamp_inference_time:.3f}s")
        
        # 清理测试文件
        Path(test_image_path).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ RapidOCREngine性能测试失败: {e}")
        return False


def create_test_image(width, height):
    """创建测试图像"""
    from PIL import Image, ImageDraw, ImageFont
    
    # 创建白色背景图像
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 添加一些测试文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    # 添加中文文字
    texts = [
        "这是一个测试文档",
        "用于性能测试",
        "文档编号：TEST-2025-001", 
        "日期：2025年7月10日",
        "责任者：测试部门"
    ]
    
    y_offset = 50
    for text in texts:
        draw.text((50, y_offset), text, fill='black', font=font)
        y_offset += 40
    
    return image


def check_gpu_memory():
    """检查GPU内存使用"""
    print("\n🔍 GPU内存检查")
    print("=" * 50)
    
    try:
        import torch
        if torch.cuda.is_available():
            device = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(device)
            memory_total = torch.cuda.get_device_properties(device).total_memory / 1024**3
            memory_allocated = torch.cuda.memory_allocated(device) / 1024**3
            memory_cached = torch.cuda.memory_reserved(device) / 1024**3
            
            print(f"📋 GPU: {gpu_name}")
            print(f"📋 总显存: {memory_total:.2f}GB")
            print(f"📋 已分配: {memory_allocated:.2f}GB")
            print(f"📋 已缓存: {memory_cached:.2f}GB")
            print(f"📋 可用显存: {memory_total - memory_cached:.2f}GB")
            
            return True
        else:
            print("❌ CUDA不可用")
            return False
            
    except Exception as e:
        print(f"❌ GPU内存检查失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 OCR性能诊断工具")
    print("=" * 60)
    
    # 检查GPU内存
    check_gpu_memory()
    
    # 测试原生RapidOCR性能
    test_rapidocr_performance()
    
    # 测试我们的RapidOCREngine性能
    test_rapidocr_engine_performance()
    
    print("\n💡 性能优化建议:")
    print("1. 如果首次推理很慢，这是正常的（模型加载）")
    print("2. 如果后续推理仍然很慢，检查:")
    print("   - GPU内存是否充足")
    print("   - 是否有其他进程占用GPU")
    print("   - 图像预处理是否耗时过长")
    print("3. 印章检测会增加额外耗时")
    print("4. Server模型比Mobile模型更慢但更准确")


if __name__ == "__main__":
    main()
