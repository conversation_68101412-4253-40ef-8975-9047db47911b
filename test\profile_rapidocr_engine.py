#!/usr/bin/env python3
"""
分析RapidOCREngine性能瓶颈
找出为什么我们的封装比原生RapidOCR慢
"""

import time
import sys
from pathlib import Path
import cProfile
import pstats
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_test_image():
    """创建测试图像"""
    image = Image.new('RGB', (600, 800), 'white')
    draw = ImageDraw.Draw(image)
    
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    texts = ["测试文档", "编号：TEST-001", "日期：2025-07-10", "这是性能测试"]
    y = 100
    for text in texts:
        draw.text((50, y), text, fill='black', font=font)
        y += 50
    
    test_image_path = "profile_test_image.jpg"
    image.save(test_image_path)
    return test_image_path


def profile_rapidocr_engine():
    """分析RapidOCREngine性能"""
    print("🔍 分析RapidOCREngine性能瓶颈")
    print("=" * 50)
    
    try:
        from src.models.rapidocr_engine import RapidOCREngine
        
        # 创建测试图像
        test_image_path = create_test_image()
        
        # 配置Server模型
        config = {
            'ocr_version': 'ppocrv5',
            'model_type': 'server',
            'det_limit_side_len': 960,
            'rec_batch_num': 6,
            'force_cpu': False
        }
        
        # 创建引擎
        engine = RapidOCREngine(device_id=0, config=config)
        
        # 预热（初始化）
        print("⏱️ 预热初始化...")
        start_time = time.time()
        result = engine.recognize(test_image_path, confidence_threshold=0.5)
        warmup_time = time.time() - start_time
        print(f"   预热耗时: {warmup_time:.3f}s")
        
        # 性能分析
        print("\n⏱️ 性能分析...")
        
        def run_recognition():
            return engine.recognize(test_image_path, confidence_threshold=0.5)
        
        # 使用cProfile分析
        profiler = cProfile.Profile()
        profiler.enable()
        
        # 运行多次以获得更准确的分析
        for i in range(3):
            result = run_recognition()
        
        profiler.disable()
        
        # 分析结果
        stats = pstats.Stats(profiler)
        stats.sort_stats('cumulative')
        
        print("📊 性能分析结果（前20个最耗时的函数）:")
        stats.print_stats(20)
        
        # 清理
        Path(test_image_path).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 性能分析失败: {e}")
        return False


def test_step_by_step():
    """逐步测试各个组件的耗时"""
    print("\n🔍 逐步测试各组件耗时")
    print("=" * 50)
    
    try:
        from src.models.rapidocr_engine import RapidOCREngine
        from rapidocr import RapidOCR
        from rapidocr.utils.typings import OCRVersion, ModelType
        
        # 创建测试图像
        test_image_path = create_test_image()
        
        # 1. 测试原生RapidOCR
        print("📋 1. 原生RapidOCR测试:")
        server_params = {
            'Det.ocr_version': OCRVersion.PPOCRV5,
            'Rec.ocr_version': OCRVersion.PPOCRV5,
            'Det.model_type': ModelType.SERVER,
            'Rec.model_type': ModelType.SERVER,
        }
        
        start_time = time.time()
        native_ocr = RapidOCR(params=server_params)
        init_time = time.time() - start_time
        
        start_time = time.time()
        native_result = native_ocr(test_image_path)
        native_inference_time = time.time() - start_time
        
        print(f"   初始化: {init_time:.3f}s")
        print(f"   推理: {native_inference_time:.3f}s")
        print(f"   总计: {init_time + native_inference_time:.3f}s")
        
        # 2. 测试我们的引擎（分步骤）
        print("\n📋 2. RapidOCREngine分步测试:")
        
        config = {
            'ocr_version': 'ppocrv5',
            'model_type': 'server',
            'det_limit_side_len': 960,
            'rec_batch_num': 6,
            'force_cpu': False
        }
        
        # 创建引擎
        start_time = time.time()
        engine = RapidOCREngine(device_id=0, config=config)
        engine_create_time = time.time() - start_time
        print(f"   引擎创建: {engine_create_time:.3f}s")
        
        # 延迟初始化
        start_time = time.time()
        engine._lazy_init()
        lazy_init_time = time.time() - start_time
        print(f"   延迟初始化: {lazy_init_time:.3f}s")
        
        # 纯OCR推理
        start_time = time.time()
        ocr_result = engine._ocr_engine(str(test_image_path))
        pure_ocr_time = time.time() - start_time
        print(f"   纯OCR推理: {pure_ocr_time:.3f}s")
        
        # 结果处理
        start_time = time.time()
        if hasattr(ocr_result, 'txts'):
            txts = ocr_result.txts or []
            boxes = ocr_result.boxes if hasattr(ocr_result, 'boxes') else []
            scores = ocr_result.scores if hasattr(ocr_result, 'scores') else []
            
            ocr_list = []
            if txts and len(txts) > 0:
                for i, txt in enumerate(txts):
                    box = boxes[i] if boxes is not None and i < len(boxes) else None
                    score = scores[i] if scores is not None and i < len(scores) else 1.0
                    ocr_list.append([box, txt, score])
        else:
            ocr_list = ocr_result if isinstance(ocr_result, list) else []
        
        # 处理结果
        processed_results = engine._process_ocr_result(ocr_list, 0.5)
        result_processing_time = time.time() - start_time
        print(f"   结果处理: {result_processing_time:.3f}s")
        
        # 总计
        total_engine_time = lazy_init_time + pure_ocr_time + result_processing_time
        print(f"   总计: {total_engine_time:.3f}s")
        
        # 3. 对比分析
        print(f"\n📊 性能对比:")
        print(f"   原生RapidOCR: {native_inference_time:.3f}s")
        print(f"   我们的引擎: {total_engine_time:.3f}s")
        print(f"   性能损失: {total_engine_time - native_inference_time:.3f}s ({(total_engine_time/native_inference_time-1)*100:.1f}%)")
        
        if result_processing_time > 0.1:
            print(f"⚠️ 结果处理耗时较长: {result_processing_time:.3f}s")
        
        # 清理
        Path(test_image_path).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 逐步测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 RapidOCREngine性能分析工具")
    print("=" * 60)
    
    # 逐步测试
    test_step_by_step()
    
    # 性能分析
    profile_rapidocr_engine()
    
    print("\n💡 优化建议:")
    print("1. 如果结果处理耗时较长，优化_process_ocr_result方法")
    print("2. 如果延迟初始化耗时较长，考虑预初始化")
    print("3. 检查是否有不必要的数据转换")


if __name__ == "__main__":
    main()
