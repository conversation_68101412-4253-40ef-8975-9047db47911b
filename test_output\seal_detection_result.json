{"input_path": "G:\\workshop\\RUYI\\ruyi-aimp\\test\\sample_archive_folder\\test_seal_image.jpg", "page_index": null, "dt_polys": [[[1141, 1615], [1144, 1616], [1146, 1617], [1157, 1618], [1160, 1619], [1172, 1624], [1175, 1626], [1179, 1630], [1182, 1634], [1182, 1636], [1185, 1639], [1188, 1643], [1188, 1650], [1189, 1654], [1189, 1658], [1192, 1667], [1192, 1674], [1193, 1675], [1193, 1678], [1194, 1680], [1194, 1696], [1193, 1699], [1193, 1704], [1191, 1708], [1189, 1710], [1186, 1712], [1178, 1716], [1176, 1716], [1173, 1719], [1168, 1721], [1164, 1722], [1163, 1722], [1159, 1724], [1153, 1723], [1150, 1722], [1136, 1721], [1134, 1721], [1123, 1717], [1122, 1718], [1118, 1718], [1112, 1716], [1109, 1716], [1105, 1715], [1104, 1715], [1100, 1714], [1096, 1711], [1094, 1708], [1092, 1707], [1090, 1702], [1087, 1687], [1084, 1681], [1078, 1674], [1076, 1670], [1076, 1668], [1073, 1662], [1071, 1657], [1068, 1653], [1068, 1651], [1066, 1647], [1066, 1637], [1067, 1633], [1068, 1631], [1071, 1628], [1072, 1628], [1072, 1627], [1075, 1624], [1076, 1623], [1077, 1622], [1080, 1621], [1084, 1618], [1090, 1618], [1094, 1617], [1098, 1617], [1100, 1616], [1105, 1615], [1118, 1615], [1121, 1614], [1125, 1614], [1126, 1615], [1131, 1614], [1137, 1614]], [[1193, 1533], [1195, 1534], [1226, 1546], [1230, 1548], [1240, 1557], [1241, 1561], [1257, 1600], [1272, 1622], [1274, 1625], [1286, 1669], [1286, 1673], [1282, 1709], [1281, 1714], [1248, 1772], [1245, 1775], [1233, 1785], [1229, 1787], [1225, 1787], [1217, 1786], [1212, 1785], [1210, 1783], [1186, 1758], [1183, 1753], [1180, 1741], [1180, 1736], [1182, 1732], [1209, 1691], [1205, 1650], [1185, 1609], [1171, 1597], [1147, 1587], [1123, 1584], [1105, 1590], [1081, 1608], [1076, 1625], [1075, 1628], [1054, 1664], [1051, 1694], [1072, 1728], [1073, 1733], [1075, 1752], [1075, 1757], [1065, 1787], [1062, 1791], [1060, 1793], [1053, 1797], [1048, 1798], [1042, 1797], [1031, 1792], [1026, 1787], [993, 1723], [992, 1721], [979, 1670], [979, 1665], [984, 1631], [985, 1628], [1017, 1568], [1019, 1565], [1036, 1548], [1039, 1546], [1055, 1538], [1058, 1537], [1126, 1522], [1130, 1522]], [[979, 986], [979, 987], [980, 987], [984, 986], [986, 987], [988, 989], [998, 1009], [998, 1012], [994, 1020], [996, 1022], [996, 1026], [994, 1028], [991, 1029], [987, 1029], [983, 1036], [980, 1038], [976, 1038], [972, 1041], [967, 1042], [964, 1042], [962, 1041], [958, 1037], [951, 1038], [945, 1038], [942, 1037], [938, 1033], [922, 1037], [919, 1037], [918, 1036], [917, 1036], [913, 1034], [905, 1025], [875, 1016], [845, 1023], [845, 1026], [877, 1053], [879, 1056], [879, 1059], [875, 1067], [872, 1070], [867, 1072], [867, 1074], [864, 1077], [863, 1077], [862, 1079], [859, 1081], [857, 1081], [850, 1080], [847, 1079], [846, 1076], [847, 1074], [846, 1074], [843, 1072], [842, 1070], [842, 1069], [831, 1070], [828, 1069], [826, 1066], [826, 1063], [821, 1063], [818, 1060], [818, 1059], [815, 1055], [813, 1055], [810, 1052], [810, 1046], [809, 1046], [806, 1042], [804, 1041], [802, 1038], [801, 1038], [798, 1041], [794, 1041], [791, 1042], [789, 1041], [787, 1042], [785, 1041], [784, 1042], [784, 1044], [783, 1047], [781, 1048], [781, 1049], [780, 1052], [778, 1054], [776, 1054], [776, 1055], [773, 1057], [770, 1057], [769, 1056], [767, 1060], [764, 1062], [736, 1065], [734, 1064], [732, 1065], [729, 1064], [728, 1063], [725, 1059], [721, 1061], [718, 1061], [715, 1060], [715, 1058], [714, 1058], [711, 1055], [711, 1052], [714, 1041], [716, 1038], [719, 1037], [722, 1038], [723, 1038], [726, 1035], [729, 1034], [774, 1023], [773, 1020], [728, 1000], [726, 998], [725, 995], [726, 992], [729, 990], [743, 986], [745, 986], [749, 982], [760, 982], [766, 983], [766, 982], [768, 979], [769, 978], [794, 969], [794, 966], [795, 963], [799, 962], [844, 962], [847, 963], [849, 965], [849, 969], [848, 970], [859, 974], [899, 978], [901, 979], [903, 982], [903, 983], [904, 982], [908, 982], [911, 984], [910, 982], [911, 979], [914, 977], [942, 970], [946, 971], [955, 976], [957, 974], [961, 974], [964, 977], [967, 983], [969, 982], [976, 982]], [[1007, 389], [1005, 396], [1001, 400], [996, 402], [976, 409], [970, 410], [964, 409], [961, 407], [956, 407], [922, 410], [921, 411], [922, 414], [922, 420], [920, 426], [916, 431], [911, 434], [905, 435], [683, 432], [613, 444], [606, 444], [560, 433], [365, 443], [360, 443], [288, 427], [282, 424], [278, 421], [276, 418], [264, 396], [262, 389], [262, 386], [263, 328], [264, 322], [267, 317], [271, 313], [307, 289], [312, 286], [318, 286], [323, 287], [347, 296], [434, 285], [436, 285], [970, 293], [1027, 283], [1033, 283], [1088, 293], [1094, 295], [1099, 299], [1102, 305], [1111, 330], [1115, 333], [1120, 314], [1122, 308], [1126, 304], [1130, 302], [1181, 280], [1187, 279], [1193, 279], [1262, 298], [1350, 281], [1356, 281], [1361, 283], [1366, 286], [1409, 328], [1412, 333], [1414, 338], [1413, 347], [1393, 407], [1390, 412], [1387, 415], [1356, 439], [1351, 442], [1342, 443], [1289, 435], [1284, 433], [1279, 429], [1275, 424], [1274, 419], [1274, 415], [1276, 402], [1271, 402], [1273, 414], [1273, 420], [1271, 426], [1267, 431], [1262, 434], [1195, 463], [1189, 464], [1184, 464], [1142, 455], [1137, 453], [1132, 449], [1129, 443], [1128, 437], [1128, 428], [1125, 429], [1105, 435], [1097, 436], [1021, 423], [1015, 421], [1011, 417], [1007, 412], [1006, 406], [1006, 400], [1009, 395], [1014, 389]]], "dt_scores": [0.9841770203963609, 0.976132899164018, 0.8561643230760344, 0.9491888906263136]}