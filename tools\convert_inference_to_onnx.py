#!/usr/bin/env python3
"""
将PaddleOCR推理模型转换为ONNX格式
"""

import os
import sys
import logging
import json
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_paddle_inference_to_onnx():
    """将PaddlePaddle推理模型转换为ONNX"""
    
    # 推理模型路径
    inference_model_dir = Path("models/seal_detection/inference_model")
    output_dir = Path("models/seal_detection")
    
    # 检查推理模型文件
    inference_json = inference_model_dir / "inference.json"
    inference_params = inference_model_dir / "inference.pdiparams"
    inference_yml = inference_model_dir / "inference.yml"
    
    if not all([f.exists() for f in [inference_json, inference_params]]):
        logger.error("缺少推理模型文件")
        return False
    
    logger.info("找到推理模型文件:")
    logger.info(f"  模型结构: {inference_json} ({inference_json.stat().st_size} bytes)")
    logger.info(f"  模型参数: {inference_params} ({inference_params.stat().st_size} bytes)")
    
    try:
        # 方法1: 使用paddle2onnx命令行工具
        logger.info("尝试使用paddle2onnx命令行工具...")
        
        onnx_output = output_dir / "seal_detection_v2.onnx"
        
        # 构建转换命令
        cmd_parts = [
            "paddle2onnx",
            "--model_dir", str(inference_model_dir),
            "--model_filename", "inference.json",
            "--params_filename", "inference.pdiparams", 
            "--save_file", str(onnx_output),
            "--opset_version", "11",
            "--enable_onnx_checker", "True"
        ]
        
        cmd = " ".join(cmd_parts)
        logger.info(f"执行命令: {cmd}")
        
        import subprocess
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("paddle2onnx转换成功!")
            logger.info(f"ONNX模型保存到: {onnx_output}")
            
            # 验证ONNX文件
            if onnx_output.exists() and onnx_output.stat().st_size > 0:
                logger.info(f"ONNX文件大小: {onnx_output.stat().st_size / 1024 / 1024:.2f} MB")
                return True
            else:
                logger.error("ONNX文件生成失败或为空")
                return False
        else:
            logger.error(f"paddle2onnx转换失败: {result.stderr}")
            
    except Exception as e:
        logger.error(f"命令行转换失败: {e}")
    
    try:
        # 方法2: 使用Python API
        logger.info("尝试使用paddle2onnx Python API...")
        
        import paddle2onnx
        
        # 读取模型文件
        with open(inference_params, 'rb') as f:
            params_data = f.read()
        
        # 对于JSON格式，我们需要特殊处理
        logger.info("处理JSON格式模型...")
        
        # 尝试直接转换（可能不会成功，但值得一试）
        try:
            onnx_model = paddle2onnx.command.c_paddle_to_onnx(
                model_file=str(inference_json),
                params_file=str(inference_params),
                opset_version=11,
                enable_onnx_checker=True
            )
            
            if onnx_model:
                onnx_output = output_dir / "seal_detection_api.onnx"
                with open(onnx_output, 'wb') as f:
                    f.write(onnx_model)
                
                logger.info(f"API转换成功: {onnx_output}")
                logger.info(f"ONNX文件大小: {onnx_output.stat().st_size / 1024 / 1024:.2f} MB")
                return True
                
        except Exception as e:
            logger.warning(f"直接API转换失败: {e}")
        
    except Exception as e:
        logger.error(f"Python API转换失败: {e}")
    
    # 方法3: 创建配置信息，供后续使用
    logger.info("创建模型配置信息...")
    
    try:
        # 读取配置文件
        config_data = {}
        if inference_yml.exists():
            import yaml
            with open(inference_yml, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        
        # 读取JSON模型信息
        with open(inference_json, 'r', encoding='utf-8') as f:
            model_info = json.load(f)
        
        # 创建详细配置
        detailed_config = {
            "model_type": "seal_detection",
            "framework": "paddleocr",
            "format": "paddle_inference",
            "files": {
                "model_structure": str(inference_json),
                "model_params": str(inference_params),
                "config": str(inference_yml) if inference_yml.exists() else None
            },
            "model_info": model_info,
            "config_data": config_data,
            "conversion_notes": [
                "这是PaddleOCR的印章检测模型",
                "模型使用JSON格式存储结构信息",
                "需要使用PaddlePaddle推理引擎加载",
                "或者需要特殊的转换工具处理JSON格式"
            ],
            "usage_example": {
                "python_code": """
# 使用PaddlePaddle推理
import paddle.inference as paddle_infer

config = paddle_infer.Config('inference.json', 'inference.pdiparams')
config.enable_use_gpu(1000, 0)  # 启用GPU
predictor = paddle_infer.create_predictor(config)

# 获取输入输出信息
input_names = predictor.get_input_names()
output_names = predictor.get_output_names()
"""
            }
        }
        
        config_file = output_dir / "seal_detection_detailed_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"详细配置已保存: {config_file}")
        return True
        
    except Exception as e:
        logger.error(f"创建配置失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始PaddleOCR推理模型到ONNX转换...")
    
    if convert_paddle_inference_to_onnx():
        logger.info("转换流程完成")
        return True
    else:
        logger.error("转换流程失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
