#!/usr/bin/env python3
"""
修复ONNX Runtime GPU支持问题
解决Server模型使用CPU而Mobile模型使用GPU的问题
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description=""):
    """运行命令并显示结果"""
    print(f"🔧 {description}")
    print(f"   命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ 成功")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()}")
        else:
            print(f"   ❌ 失败 (返回码: {result.returncode})")
            if result.stderr.strip():
                print(f"   错误: {result.stderr.strip()}")
        return result.returncode == 0
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False


def check_current_installation():
    """检查当前安装状态"""
    print("🔍 检查当前安装状态")
    print("=" * 50)
    
    # 检查ONNX Runtime
    try:
        import onnxruntime as ort
        print(f"✅ ONNX Runtime版本: {ort.__version__}")
        providers = ort.get_available_providers()
        print(f"📋 可用提供者: {providers}")
        
        cuda_available = 'CUDAExecutionProvider' in providers
        print(f"🔧 CUDA支持: {'✅ 是' if cuda_available else '❌ 否'}")
        
        return {
            'version': ort.__version__,
            'cuda_available': cuda_available,
            'providers': providers
        }
    except ImportError:
        print("❌ ONNX Runtime未安装")
        return None


def fix_onnx_runtime():
    """修复ONNX Runtime GPU支持"""
    print("\n🛠️ 修复ONNX Runtime GPU支持")
    print("=" * 50)
    
    # 步骤1: 卸载现有版本
    print("📋 步骤1: 卸载现有ONNX Runtime版本")
    commands = [
        "pip uninstall -y onnxruntime",
        "pip uninstall -y onnxruntime-gpu",
        "pip uninstall -y onnxruntime-directml"
    ]
    
    for cmd in commands:
        run_command(cmd, f"卸载: {cmd.split()[-1]}")
    
    # 步骤2: 安装GPU版本
    print("\n📋 步骤2: 安装ONNX Runtime GPU版本")
    
    # 根据CUDA版本选择合适的ONNX Runtime版本
    # CUDA 11.6 对应的ONNX Runtime GPU版本
    gpu_install_cmd = "pip install onnxruntime-gpu==1.19.2"
    
    if run_command(gpu_install_cmd, "安装ONNX Runtime GPU"):
        print("✅ ONNX Runtime GPU安装成功")
    else:
        print("❌ ONNX Runtime GPU安装失败")
        # 尝试安装最新版本
        fallback_cmd = "pip install onnxruntime-gpu"
        run_command(fallback_cmd, "安装最新版ONNX Runtime GPU")


def fix_rapidocr():
    """修复RapidOCR安装"""
    print("\n🛠️ 修复RapidOCR安装")
    print("=" * 50)
    
    # 检查当前RapidOCR安装
    try:
        import rapidocr
        print("✅ RapidOCR已安装")
        return True
    except ImportError:
        pass
    
    try:
        import rapidocr_onnxruntime
        print("⚠️ 检测到rapidocr-onnxruntime，建议升级到rapidocr")
    except ImportError:
        pass
    
    # 卸载旧版本
    uninstall_commands = [
        "pip uninstall -y rapidocr-onnxruntime",
        "pip uninstall -y rapidocr-paddle",
        "pip uninstall -y rapidocr"
    ]
    
    for cmd in uninstall_commands:
        run_command(cmd, f"卸载: {cmd.split()[-1]}")
    
    # 安装新版本
    install_cmd = "pip install rapidocr"
    if run_command(install_cmd, "安装RapidOCR"):
        print("✅ RapidOCR安装成功")
        return True
    else:
        print("❌ RapidOCR安装失败")
        return False


def verify_installation():
    """验证安装结果"""
    print("\n🧪 验证安装结果")
    print("=" * 50)
    
    success = True
    
    # 验证ONNX Runtime
    try:
        import onnxruntime as ort
        print(f"✅ ONNX Runtime版本: {ort.__version__}")
        
        providers = ort.get_available_providers()
        print(f"📋 可用提供者: {providers}")
        
        cuda_available = 'CUDAExecutionProvider' in providers
        tensorrt_available = 'TensorrtExecutionProvider' in providers
        
        print(f"🔧 CUDA提供者: {'✅ 可用' if cuda_available else '❌ 不可用'}")
        print(f"🔧 TensorRT提供者: {'✅ 可用' if tensorrt_available else '❌ 不可用'}")
        
        if not cuda_available:
            print("⚠️ CUDA提供者仍然不可用，可能需要:")
            print("   1. 更新CUDA驱动")
            print("   2. 重启系统")
            print("   3. 检查CUDA版本兼容性")
            success = False
            
    except ImportError as e:
        print(f"❌ ONNX Runtime导入失败: {e}")
        success = False
    
    # 验证RapidOCR
    try:
        from rapidocr import RapidOCR
        from rapidocr.utils.typings import OCRVersion, ModelType
        print("✅ RapidOCR导入成功")
        
        # 测试初始化
        try:
            ocr = RapidOCR()
            print("✅ RapidOCR初始化成功")
        except Exception as e:
            print(f"⚠️ RapidOCR初始化警告: {e}")
            
    except ImportError as e:
        print(f"❌ RapidOCR导入失败: {e}")
        success = False
    
    return success


def update_environment_variables():
    """更新环境变量建议"""
    print("\n🔧 环境变量建议")
    print("=" * 50)
    
    print("建议设置以下环境变量:")
    print("1. CUDA_HOME=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.6")
    print("2. CUDNN_PATH=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.6")
    print("3. 将CUDA bin目录添加到PATH")
    print()
    print("PowerShell命令:")
    print('$env:CUDA_HOME="C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.6"')
    print('$env:CUDNN_PATH="C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.6"')
    print()
    print("注意: 请根据实际CUDA安装路径调整")


def main():
    """主函数"""
    print("🚀 ONNX Runtime GPU支持修复工具")
    print("=" * 60)
    
    # 检查当前状态
    current_state = check_current_installation()
    
    if current_state and current_state.get('cuda_available'):
        print("✅ ONNX Runtime GPU支持已正常，无需修复")
        return
    
    print("\n开始修复过程...")
    
    # 修复ONNX Runtime
    fix_onnx_runtime()
    
    # 修复RapidOCR
    fix_rapidocr()
    
    # 验证结果
    if verify_installation():
        print("\n🎉 修复完成！")
        print("建议重启Python环境或重新启动服务以确保更改生效")
    else:
        print("\n⚠️ 修复过程中遇到问题")
        print("请检查错误信息并手动解决")
    
    # 环境变量建议
    update_environment_variables()


if __name__ == "__main__":
    main()
